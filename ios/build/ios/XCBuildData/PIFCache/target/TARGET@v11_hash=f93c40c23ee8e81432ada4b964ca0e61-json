{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d7469731bb69625d700787f558e4f27", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844517657495ba354bdc5f8f09ae0b5c6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844517657495ba354bdc5f8f09ae0b5c6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981a21d7aa9f9d84a3c2c1eb015a4102ea", "guid": "bfdfe7dc352907fc980b868725387e985176eb7aa89517dd79b622378e05c296", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987df72fa20b6e31d79dbb15adf59f0ed7", "guid": "bfdfe7dc352907fc980b868725387e98af69fb08e313617716d0d228cf215245", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ec20e41012e61212bab94f5a70f75db", "guid": "bfdfe7dc352907fc980b868725387e985e2e93614b4b998292a11ff2a2c13b6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98154563dc713069d6b48b4eeaf62608d7", "guid": "bfdfe7dc352907fc980b868725387e98f39594556e495895558fa1a08b655ba5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98299805f180f235b040328aef597f432e", "guid": "bfdfe7dc352907fc980b868725387e9892d2936f43a59179d3e2fc2a4021e53e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888c4610bed11470f45069e5569f1ff3e", "guid": "bfdfe7dc352907fc980b868725387e98de9bba16cdb7cae0aadac8b6746bb22f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3ca89fa9b0dad08ee30d4c189f5013", "guid": "bfdfe7dc352907fc980b868725387e986f2fd5217ee9ef9b9f743d7150a25c60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804dc634887f7f828dda4b93644989135", "guid": "bfdfe7dc352907fc980b868725387e9838280bfcd8b736867eaa4077fbc0d61c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4068a5809112975d14f58c36549325d", "guid": "bfdfe7dc352907fc980b868725387e985212bf823b2dbcc093385db1f1b30dec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ec4e6e61b11e94dab5f522c88751482", "guid": "bfdfe7dc352907fc980b868725387e983b3e9d753ef701196ebaaa81a1e77d66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860203c442b44a95961aaeede203f634d", "guid": "bfdfe7dc352907fc980b868725387e985cf8cb83875e6694983c229dac8bc9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff5d5b0f2e48e6c29442ff4e9a73ecac", "guid": "bfdfe7dc352907fc980b868725387e988145d23612ab237ea91016460d73945a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982af82d943314e9f91291d91e97a5ba3f", "guid": "bfdfe7dc352907fc980b868725387e982690b7c043bffc9f7499a3599fc5db55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982353ae4d8e0fe9a80d305a44729d74cd", "guid": "bfdfe7dc352907fc980b868725387e984bcf2821c445a13f1ca5ddd1387c63d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846c5f9d778574e116bca9f2f9b8e4a0b", "guid": "bfdfe7dc352907fc980b868725387e9814e771116f0efcc61da8fc335aa4ca3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bdd3079af948447bac03b077adba627", "guid": "bfdfe7dc352907fc980b868725387e9896786428e6a8d6c7862d939cae65f773", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ba49c946da5d7a387c03e993a04bfe", "guid": "bfdfe7dc352907fc980b868725387e98790199ae3165d72360ab21230766b727", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987ec2a6010044755843381eeb71f4020a", "guid": "bfdfe7dc352907fc980b868725387e98ac5c52be3190abee156605bfd48912ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98551e967990d4f0d48914333b6173b3b9", "guid": "bfdfe7dc352907fc980b868725387e98e1de61ab1ff97cc94cd06a5371923da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834918d367ef3169ec0d99969f2c0c0d0", "guid": "bfdfe7dc352907fc980b868725387e98d0242bdacab0261eec7e248947d90b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98625bba8c302dbeebe0e770b3e96f4e88", "guid": "bfdfe7dc352907fc980b868725387e982d942064c23276142327490fa33537b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e540ac3dd3e6dcc9f8e7203ae7680a30", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852bfa68783457088f3a74e24f98fdcb6", "guid": "bfdfe7dc352907fc980b868725387e987f77bace7e1c1bfd0b3f575d26dfb06f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b57744e6a9070a1da65c61c0eb2fd39", "guid": "bfdfe7dc352907fc980b868725387e9815ed68575bdb30e9bf33141f83bd83b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98268ac69e304092068996c42f8bdada6e", "guid": "bfdfe7dc352907fc980b868725387e98590b8f41b31928b0a4926ce46a04415a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c5cf41cd3b7bb15b173a65dd4270a11", "guid": "bfdfe7dc352907fc980b868725387e98f6d629ffc18995e49d3d77a3aef0dcb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986816d52dc648fc8d17fec9f24bf5b20c", "guid": "bfdfe7dc352907fc980b868725387e98af9f015ea26609a22120bcae2d16071a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcf101a5e6144ac6139f5e0a8812c88b", "guid": "bfdfe7dc352907fc980b868725387e989c0bfab68da1fedec51cf36689157069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ffaf1462721555135ae54cb5f23c77e", "guid": "bfdfe7dc352907fc980b868725387e98c5820ac9b4d0a4a76d9404b84c0ec2b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878233ce5401ace9ed36dc4592998f72a", "guid": "bfdfe7dc352907fc980b868725387e98df9697c7b74a7cf4593f9d66eda4c228"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98714a695b0c8b241a1601aa89b3f6b149", "guid": "bfdfe7dc352907fc980b868725387e98df10a50fc47d08262ba22e5741f99c0e"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}