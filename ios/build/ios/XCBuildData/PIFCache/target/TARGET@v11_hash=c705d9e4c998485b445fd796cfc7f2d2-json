{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef6faaf5530b3b20c57c5ff8ebac958c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cbd99bcfa8f2fdd96f6c289d3af5f2a6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cbd99bcfa8f2fdd96f6c289d3af5f2a6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982fd1f4162e800d68b4d4f1ee387b2fda", "guid": "bfdfe7dc352907fc980b868725387e98c4e0c5a3f9916bda01a7d363bb754855", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d215039b9e0de5c03a23fdea5e629664", "guid": "bfdfe7dc352907fc980b868725387e981b1ea614c42ca86bde1a04a014785f06", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98748b171c681b953aed4c33ea0ff054d1", "guid": "bfdfe7dc352907fc980b868725387e986a50945d414d8eb3142de386b0d5589d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb10c2958c99e6398e66050b33a51f1a", "guid": "bfdfe7dc352907fc980b868725387e987c18475cb5deccc97fd66c4ceb551f0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba300e3ba325af4e0d284fec6327e0f3", "guid": "bfdfe7dc352907fc980b868725387e988620f784398a50f7b76cf5f79648390e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bfa4da8d4c6b90059f57cede6feefde", "guid": "bfdfe7dc352907fc980b868725387e9878e7fff8e3e6f985635b0a9adc1b4330", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74304a67332c326ac5282bee6fc1cd4", "guid": "bfdfe7dc352907fc980b868725387e9830790846cc1cdb42b933a43750ee8d97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf1f39ce19cf2fd86810d072e031918", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885580ecc2a55e7cb25d204f3c12ec75a", "guid": "bfdfe7dc352907fc980b868725387e98778ae28dc14921572eb2c41efb09898c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddc9653a8d60d7a13d896a600fcd9a49", "guid": "bfdfe7dc352907fc980b868725387e98283c1456459201a97e77d7554ad9c163", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a51c988bd43d1f3db024c773307cef8", "guid": "bfdfe7dc352907fc980b868725387e987199b04369963a149b09403847908049", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a31a3f51c6bf3349cc7c9b29424af2ae", "guid": "bfdfe7dc352907fc980b868725387e98d0c41795fb0967a0334ebab480010788", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b3043a869fbdb17d097906ed9d3ddeb2", "guid": "bfdfe7dc352907fc980b868725387e987b916b544758839551cf50642114b270"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba6de3cab26d566dd7b764f2edec286c", "guid": "bfdfe7dc352907fc980b868725387e9873038c7484276ac4450bee12a87e78c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc77b9a0f2fa30ce9e5cbab6ec447c0", "guid": "bfdfe7dc352907fc980b868725387e9836be5e3c9bc2d1e3cfa7a2f75c4b20aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825acaa55d5301ae678fd39dbb065a6d4", "guid": "bfdfe7dc352907fc980b868725387e98aa920eb15394517f34c6670614ee2259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bedc481ea663aaef2b72fa31fc0f49e9", "guid": "bfdfe7dc352907fc980b868725387e9835d735d19f849c47f714a4dd9108565f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd59eba7fcdc27ac411a7bd28521b69", "guid": "bfdfe7dc352907fc980b868725387e98ef79d2bf6051d9ac5448091d025308ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98727ef1e59ec2be8c59cbb0c938c2cd31", "guid": "bfdfe7dc352907fc980b868725387e981cf9a3ef93e12373cffb3a9c5cee4b6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885705a77783fca1bed32055b23931c4e", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd0f7e2dc12906d00809b031873df8c", "guid": "bfdfe7dc352907fc980b868725387e983e42b775bebc9095cd8128c4414a12a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b1194095ee50e5aebf20379805eaf0b", "guid": "bfdfe7dc352907fc980b868725387e98ab3118b4c65ae68be3f60a01d3893960"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d99ddfae3b4878f8b9219b15c85c1d", "guid": "bfdfe7dc352907fc980b868725387e98c5b0a4165f310cc711f3115b8a6945b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408ae2dd51000e5faa036c9e543a3be2", "guid": "bfdfe7dc352907fc980b868725387e986725d015aac3460881d02c6618f63eaf"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}