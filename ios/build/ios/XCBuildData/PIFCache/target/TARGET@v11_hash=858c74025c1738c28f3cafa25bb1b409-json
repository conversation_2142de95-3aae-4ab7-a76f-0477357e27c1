{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871d4b02b3df0658553c71caac3177d36", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820a2cb7398185ca719ae889d48923b7e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c3e1e241e0bdba7f0e6441254f89574", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845584cf9911842a36472dca74a350c68", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c3e1e241e0bdba7f0e6441254f89574", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fce723c8686d8574addfefe416592132", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9849252de0ea835451b87a95e881327b36", "guid": "bfdfe7dc352907fc980b868725387e98f9cef44151710a8578a0e8b5a6757972", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c4fa83b7b1a76a9e8fbfc60b833e572", "guid": "bfdfe7dc352907fc980b868725387e984af319b1d61196c8e1f55b3813851828", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ad6ae780dbacfe746bc84dc71e7db1b", "guid": "bfdfe7dc352907fc980b868725387e98593981748603e5f7470eb5421d703334", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a14f828bb6db199c0d34886b273c500", "guid": "bfdfe7dc352907fc980b868725387e98fc9d1809fa01319c86f15be5510a78a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda45200a975d535c9d17447a458173d", "guid": "bfdfe7dc352907fc980b868725387e98d01c8a52e4bc2beb60a23c170956c0d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834bdcf00e489fddce7d18a565439f7fc", "guid": "bfdfe7dc352907fc980b868725387e981bff5b75cd1c4b75fba8a13cc1ffeac6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98880fb32cb2eae8d2fd5d23a5844f38da", "guid": "bfdfe7dc352907fc980b868725387e9869bb8a9912b12534c2f08ae56dbe1b68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864f200b5fec591a7dfe46060f50c1b0e", "guid": "bfdfe7dc352907fc980b868725387e98651ec35877cef5c7842a8740956b505f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ce250a9e81972bddef309453d2e85fb", "guid": "bfdfe7dc352907fc980b868725387e9815b146573554ecbd23ce9ef7d8206543", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981928993ab42a6b0775de1f2e10a2d962", "guid": "bfdfe7dc352907fc980b868725387e98f004959d7f9b4ed02659fde7b189cbc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad4ce42319aa2165486ed1b735d4d6cf", "guid": "bfdfe7dc352907fc980b868725387e98885b5af608e3716c0eeb84a193aed92d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f083a96a8338e4979b8e6de0e2bf186", "guid": "bfdfe7dc352907fc980b868725387e985150a75806bcbc2e76583b29545a3684", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f08ece2928a6c68648b9256370287136", "guid": "bfdfe7dc352907fc980b868725387e981f80272216a7f342e1b5582202cfebf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850888e6ba39b685f7d1f8cdfc80e54be", "guid": "bfdfe7dc352907fc980b868725387e98a81cf48cae4e9aa095af5e5186237e62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d71a86a8268ca56858652324a6ac37e3", "guid": "bfdfe7dc352907fc980b868725387e98ca82a2a37a19117ba9f8b8c44762c0c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf560c206bd8f2296f9d07550033251e", "guid": "bfdfe7dc352907fc980b868725387e986b0e9522120a8249f07177f38676f343", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981220135ca6690450fa04c75a6cf25f15", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ef647b9f0d25785d46c69d5bd01e1d91", "guid": "bfdfe7dc352907fc980b868725387e98a7d71889ae54b4e59207075d95e4a490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc920c784198a5de8e52e1c3d57b30e0", "guid": "bfdfe7dc352907fc980b868725387e989a2034edb45398b123f72f7a05c4a288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc46790ea1fe2cc479ba649782adc20", "guid": "bfdfe7dc352907fc980b868725387e98dfc8dace853a5ce5ecccb8788c8c79d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a92831dd24e873829c4f6961150d52", "guid": "bfdfe7dc352907fc980b868725387e984ed5e8852a8d64a53876235537415c54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817e1306b82ee4a460b9662627eb81bd2", "guid": "bfdfe7dc352907fc980b868725387e98dce16ed5a025a752a5b3d53e0f605bef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef5182d2ad58e0e371b35b384e02a446", "guid": "bfdfe7dc352907fc980b868725387e986e3b34a7c72c36c644ceb82e2cd7eeb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98612f3cc84eb10193e1b928a05366f79c", "guid": "bfdfe7dc352907fc980b868725387e98996d41fb8619bee1a5a1c64c1d63b98e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d954551ed94925fdc8fb3a8611b6eff", "guid": "bfdfe7dc352907fc980b868725387e9886dad12b9b588c9f9735be6489779885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6d5e401cae7dcd367636ebaecaa3aa0", "guid": "bfdfe7dc352907fc980b868725387e984b317917d0edeb5d4ad967e4d3ab3bdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e29723057fb05bcffa728445eec1a68c", "guid": "bfdfe7dc352907fc980b868725387e988831a512a63f59528340d53b4d0f94d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f9df97f77a7fd92f1060eb9514bb08e", "guid": "bfdfe7dc352907fc980b868725387e981b4ac6ff2fb493e93a7094e86bc28a76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854f038e5e4ea15f5977daf2917d06432", "guid": "bfdfe7dc352907fc980b868725387e988bb0c7ff74e92cd2b77cdc9c2125aca8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98473236d77faf6811a41900ae5eff5dee", "guid": "bfdfe7dc352907fc980b868725387e98a89679ba4a0c8c4ac5da389b1037d52c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e580fbe5b7a88430f7c4e292c16848a", "guid": "bfdfe7dc352907fc980b868725387e98912a317c31e1aec549ca80a68788c255"}], "guid": "bfdfe7dc352907fc980b868725387e98cc2d13f408a8064d8a6b28875feb99e2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b98b33a9043d0650dc73e4201e52aa73", "guid": "bfdfe7dc352907fc980b868725387e98b5ae0e6e0fdd2521ec19a7f8a89252e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e988ddb8d5b5e6c0d5e9a3b3e52fc5fabdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b03320a09664f613e59afdaeff8fa090", "guid": "bfdfe7dc352907fc980b868725387e98dbb8fd4ab7c00e66f1681d29cad47a45"}], "guid": "bfdfe7dc352907fc980b868725387e98bc805078eb0d455d7e9a0b7639826924", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989fc01a956173dc7f0ec6e92897942b9a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98ae4cba9dafde33c1f8a32ad5773d6cd3", "name": "GCDWebServer", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ea940bf2ad9c0f24726e4c14aa315ff6", "name": "GCDWebServer.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}