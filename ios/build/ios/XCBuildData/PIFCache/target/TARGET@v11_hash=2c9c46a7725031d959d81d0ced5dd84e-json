{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7df3151db9927db7ce3e070e7bf0395", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985a3db6c199c96e21123ac1982ea5d65d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b6f5daa3342c5c863352fc32ef8e84b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9893422d0b936bb46030638bffc4992546", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b6f5daa3342c5c863352fc32ef8e84b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982021652103a822616604388a956ff707", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983ff616d1de074ace12c4deff2184c52c", "guid": "bfdfe7dc352907fc980b868725387e981b0a8095391249f5f73327380dae8e8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0023599aed304e69fd52c3f2161318d", "guid": "bfdfe7dc352907fc980b868725387e98a17df20359ed78692bc72500375ff072", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870aca20f7db6f28e2e130cf74d725503", "guid": "bfdfe7dc352907fc980b868725387e981a8b2d466784ba6a98c423d73fb57ab5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880a34fb70a2408cdb37449905f3bee68", "guid": "bfdfe7dc352907fc980b868725387e98c10798a71854b1b4542a3f600afdf691", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcfa431c798423cda5c9f16fcf1e9b3c", "guid": "bfdfe7dc352907fc980b868725387e98ab0cbcc9fb1156f6024c58cf7451542c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98748778fc2a876e13c83b03294cb6b774", "guid": "bfdfe7dc352907fc980b868725387e9894a76fd8a26e0555c0ea61479d1d7fce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cdfa4fc16adbbb1bb12bd272a9da8b4", "guid": "bfdfe7dc352907fc980b868725387e980f21f57b41ea31fbfc877d55c73f1fa1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9877272a598863c43d78644937804c8625", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98235dcd38131404ed4f03e7442e3d5005", "guid": "bfdfe7dc352907fc980b868725387e980912e59c6251eec7187a008d9cff05c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f542b8af3a0f60c37064aaae7ae0f9", "guid": "bfdfe7dc352907fc980b868725387e98a67108a784702306c5733b6ea1989960"}, {"additionalCompilerOptions": "-fobjc-arc-exceptions", "fileReference": "bfdfe7dc352907fc980b868725387e989ecc3e4df7b81b0bca81e1adf7fe1dfd", "guid": "bfdfe7dc352907fc980b868725387e980785958f61fe5c72069331b1f77a3753"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b313d215c3b73277f649eae339f2aad", "guid": "bfdfe7dc352907fc980b868725387e98b93c6fe1ee0c4e2e8c1ae21cf2ac6024"}], "guid": "bfdfe7dc352907fc980b868725387e98dc1be155cf23b9354efc00cdfb9ad0b2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e980e65187e1a9c1b46cba7d0e0a75871f3"}], "guid": "bfdfe7dc352907fc980b868725387e98cd31cb7a7fe62e8ad7378c8bd14331e5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988b87d450bb056f1995af8e95e4f10242", "targetReference": "bfdfe7dc352907fc980b868725387e98956b709ce41abfabe34269a056c97973"}], "guid": "bfdfe7dc352907fc980b868725387e98a659deb2d1b1b9f46800f1fb92efa2bb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98956b709ce41abfabe34269a056c97973", "name": "PINCache-PINCache"}, {"guid": "bfdfe7dc352907fc980b868725387e98d09cfc976f4622a7180a4de1a3b0a290", "name": "PINOperation"}], "guid": "bfdfe7dc352907fc980b868725387e980a765b211b0c8c508dddcb830a52bbab", "name": "PINCache", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98329516f5a905f2fb033999940b6a62f1", "name": "PINCache.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}