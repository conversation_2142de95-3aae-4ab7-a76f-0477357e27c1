{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98164b646411cad1efcc0996494beb7cbb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9819c491b8b9c46d93df68f7aecb1d8f12", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98589e98ed0d9450c8cd358506bee0057e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9811a596f78049c98a81c550a1166e4b40", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98589e98ed0d9450c8cd358506bee0057e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9809662a081fc78a901f8776757b7525cb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ec09eba8410ff5f1d88ac27473ca281a", "guid": "bfdfe7dc352907fc980b868725387e98fdf4f6f3cfcf202b7aad698ad22c5b39", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9803e718f716275816f86e5445ddde77c3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9857515a8467ef4e6b4e53e4590ca82510", "guid": "bfdfe7dc352907fc980b868725387e9848d1406a7d60baedd7e24312730253eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f5f593875245e7e97fdea0ab0a7aaf9", "guid": "bfdfe7dc352907fc980b868725387e981e9592da6ffb42a3df577179b093c769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878ad8e1103865909147c4601fcdbbf95", "guid": "bfdfe7dc352907fc980b868725387e98b6c369e8f55aab5a5cf25ffbebae11a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841bb92f23007121a19fe8d9032802889", "guid": "bfdfe7dc352907fc980b868725387e9825c3e1b8fb91a580b78112fbddcf48ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e57878b6b1ac549535ce358f4325ea63", "guid": "bfdfe7dc352907fc980b868725387e98e9964713e58651f69e54a22b1267974f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98488817d40c120e27322ad10528774915", "guid": "bfdfe7dc352907fc980b868725387e98cd56cd05f14a8debf3a4179596277e3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813f2c76fd39c73cfc06b7f16a3fe5eed", "guid": "bfdfe7dc352907fc980b868725387e985db1d429593238942a0a8cbf9d8a7450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdb6dcdd6f29fb2c76405dbb299df3f8", "guid": "bfdfe7dc352907fc980b868725387e98d5400cbcc9e7fd01bbe07a040ad8f238"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c803bc770b24d08e91e017a891c1e54f", "guid": "bfdfe7dc352907fc980b868725387e9821d4bc4a974e61544fa603b54c089a75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac1a8fde12c24ef60b847c07b9ddbf8c", "guid": "bfdfe7dc352907fc980b868725387e988f7a63334447305bd9d740496c877c49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860af9fe11585c239c87da6f7cb49c7b7", "guid": "bfdfe7dc352907fc980b868725387e989ff09a3d06875ff51d47f52155deb6af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875e9a121e113bbcd53a54754e376047a", "guid": "bfdfe7dc352907fc980b868725387e9842294fe7997ab25594f86f6aa8cbc840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98505a0798bf5d794b4fe18f5181eb315a", "guid": "bfdfe7dc352907fc980b868725387e98af97ed3e8fdbe59d796f5c9360ba99a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98607405685d2526ca437d5991556b1331", "guid": "bfdfe7dc352907fc980b868725387e98ae14b2f88e9d6e21e1679180decc6bd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812b16e162ee4bd660f6d1477ad7da0b9", "guid": "bfdfe7dc352907fc980b868725387e98c61e280448ae53e7eadd7a89a60812fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e868c04befa45ef62d15181e9e8f9dd9", "guid": "bfdfe7dc352907fc980b868725387e98f364057cd1b6bf0a2eb34f7d6013b3c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ef57c23fa17fff45e5734d71ce29bd7", "guid": "bfdfe7dc352907fc980b868725387e984f4d71dbccccf8d11130b579b75bf89f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ae427a3714773a24f76083cd49a9145", "guid": "bfdfe7dc352907fc980b868725387e98138b8b6ed27930f5f488aa6779272b01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d0953ecd009d94500b72639ec9eea3", "guid": "bfdfe7dc352907fc980b868725387e9845ddc1b6d7f967964024aa9fe4efc2da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e264680db739a9e49bb648490ac5e6c", "guid": "bfdfe7dc352907fc980b868725387e98de2ba9b84148be7141bb86c2813e417a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852489d37f736c2bd4af288074830b603", "guid": "bfdfe7dc352907fc980b868725387e98ae294f3e9112714fa336d4bd9e5e8d2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eddf3ec1a25e4bb94c26f3f18b00514c", "guid": "bfdfe7dc352907fc980b868725387e98f3464d1b96a27489303cdfd1e5ca3824"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecf133c6b8e655e629395cb44f2d26cb", "guid": "bfdfe7dc352907fc980b868725387e98845738e1a1702edfa6186be0207d4386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98648fb7aa85bb65cbabc586bf0945d3f5", "guid": "bfdfe7dc352907fc980b868725387e98963954c4d78a40da247d54205ddb4254"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb5815698d25b81a36b55c73c1ac83db", "guid": "bfdfe7dc352907fc980b868725387e98f3a1924150a56961401d9b4d65b9a8b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813bd3c9e02eb4e0ce46a5d7086a95dc4", "guid": "bfdfe7dc352907fc980b868725387e98eee59649227b77af8222a536175b2df6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98447c68f3abca88ede2f7b4838606833e", "guid": "bfdfe7dc352907fc980b868725387e98dba51e1ee85977e1ff5261b38d5cd31f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee0cbc6e57a1e266727bdcf35b1f12e", "guid": "bfdfe7dc352907fc980b868725387e9863729b96885c46e7ea3d56515f6496a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98753974a96f8eb619534e09fafb5d8f9d", "guid": "bfdfe7dc352907fc980b868725387e98c31506c5c2dda7be099d37185af1c519"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33122c4cd2b7945ad94d25aee991bff", "guid": "bfdfe7dc352907fc980b868725387e986acbea0c6f997b01eb654df526e22167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98959e304d9df7ab6d0950c7f249696536", "guid": "bfdfe7dc352907fc980b868725387e98dcd65ca4fe88590160b5f52308b0e6a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811fa52cacf08461617d064b47757a84f", "guid": "bfdfe7dc352907fc980b868725387e98641528d2d9f4cffef700013170d4e529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e3fc9c9db946c4b257b5af5cd268ce0", "guid": "bfdfe7dc352907fc980b868725387e9899125d5b2f7b83a7ed38a0c6ba7cce26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3b27573f3d74acd17904d0033b9feb9", "guid": "bfdfe7dc352907fc980b868725387e98a91d1190938238727483a444f3816d7e"}], "guid": "bfdfe7dc352907fc980b868725387e983a0aa2a1f5d801f8c09ee3559e1daed5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e986c38e0252e037583c5b90ea60873512d"}], "guid": "bfdfe7dc352907fc980b868725387e9805391d61e995fa68e1f2d97a3734eef9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f88e3121499c86961ced27909ba01cd6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98687f14f834c716a0a6b72b2f678d5d39", "name": "<PERSON><PERSON>", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98fdbf8692076b1f163449c92d81d4a201", "name": "Cache.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}