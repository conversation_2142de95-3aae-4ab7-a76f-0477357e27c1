{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981169f23290c87320b497469e0a8fff98", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1561142554987022257ccf4e4543559", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98876267416684c584eb95a8f02f330d7d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9881667d2ab62a32799afda42b358f7fc7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98876267416684c584eb95a8f02f330d7d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f3b4c9fdea73ff99b7b526cefee359b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98854a11eba3d84a6aa4ed5fd33bb3ea29", "guid": "bfdfe7dc352907fc980b868725387e9864302f71e033c26b5d835179c932de96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981aed4d7fa11a99dbeb0ad12d81c74da0", "guid": "bfdfe7dc352907fc980b868725387e9838a944bc17087d7223c15ffcdb27fece", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816282cb76705dc54a0ff69fb5d04f972", "guid": "bfdfe7dc352907fc980b868725387e98b6895ae60f2130270cbe1abb24e71c9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe14d120b858cb747e47b52ec3cd74f0", "guid": "bfdfe7dc352907fc980b868725387e98342aba8bbfdc042d3a3e68bdad8fc8fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893bd19e530a1094a2193491e1f522aab", "guid": "bfdfe7dc352907fc980b868725387e98b36fcbf7de7062d654692d3d2187ae3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc39be0c3e37a84011c07045119bb790", "guid": "bfdfe7dc352907fc980b868725387e986d1efb8b31dc0694d5bb7ead7d099dc7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e120916b00dbd2c73819adc6bb454ae0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9802373f1959e50cc8bd334a56e8019cd4", "guid": "bfdfe7dc352907fc980b868725387e98d1a2b06fe1dc0a6711c7b877b42921d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8d705f366428bf7a0d491659e10662f", "guid": "bfdfe7dc352907fc980b868725387e984960ef3538233a07ad3d751586ce066f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7298fb211863a3e1beb7c41e923b489", "guid": "bfdfe7dc352907fc980b868725387e9876e077aa510c764da27bbd1e4e373fea"}], "guid": "bfdfe7dc352907fc980b868725387e98f207cfc0d9933d61facc977c6b7950cb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e9869716c56dea6563a20ff6fbc770acd60"}], "guid": "bfdfe7dc352907fc980b868725387e98a877b3b0361edd0959b5dd9b958542dd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b0e5d0b2627f2f1a1f6470a6ef318ee1", "targetReference": "bfdfe7dc352907fc980b868725387e9872a1d0c9b4d65372f854ad7ceb04716f"}], "guid": "bfdfe7dc352907fc980b868725387e98a59c565a8b738948fd5461c315da084e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9872a1d0c9b4d65372f854ad7ceb04716f", "name": "PINOperation-PINOperation"}], "guid": "bfdfe7dc352907fc980b868725387e98d09cfc976f4622a7180a4de1a3b0a290", "name": "PINOperation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ccd5063b353607b0bc283d743114ffaf", "name": "PINOperation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}