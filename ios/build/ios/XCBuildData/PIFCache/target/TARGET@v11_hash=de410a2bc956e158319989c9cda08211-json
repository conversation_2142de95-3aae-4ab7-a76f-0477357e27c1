{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989eabca16e0c74c9b03f1297a1f0da350", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a2306391936207ff3a77bf532265ba12", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1834b4d2b9ed7b65d0adab075fab81e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98487ed0955f6ee94f3b149ed57f321128", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1834b4d2b9ed7b65d0adab075fab81e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f8dc2559c8635ba7bcf52e3f977ac5f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981add95b4b1c3f85c84dc16e076499591", "guid": "bfdfe7dc352907fc980b868725387e98dbc829ea22378301ce6b63512887d7c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984044183fbc7d80edbb20c53eee919a3c", "guid": "bfdfe7dc352907fc980b868725387e98a35748dc66a1175cba584c374c1545a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e041c85c42e38fae211ddcbb40fc5846", "guid": "bfdfe7dc352907fc980b868725387e989d2e415c0d0cc4d8b159d78bd1dafdfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a798bcaaba048d09a9ea669be4d2d3db", "guid": "bfdfe7dc352907fc980b868725387e9885329d22b46ddee43be63e33c565b2f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3c7472dd02eb2954934371592d8b82a", "guid": "bfdfe7dc352907fc980b868725387e98595b4349a1f7c77c14e5b7f9e16301e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b67e813e69e271e27f486ea9152fbb0c", "guid": "bfdfe7dc352907fc980b868725387e980e08ceaae609ea827fb9e33cb3c306ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf895bf107cebeb35bcb3d92a00c202c", "guid": "bfdfe7dc352907fc980b868725387e98ac131ccc565252a66ea22cff730f3938", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ffc37dcf7dfc8d307d9bdae73bf1ce4", "guid": "bfdfe7dc352907fc980b868725387e982fd84f3b642f9acf839189acd4e097a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818027c513030af1de0259787af47b2d5", "guid": "bfdfe7dc352907fc980b868725387e98aef6853839a0a179b789b150c3c2deb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98031e7f2469669a5feb5c084afb094449", "guid": "bfdfe7dc352907fc980b868725387e9837ee286f79c4bd6f63bac3cc37abb923", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afb9a0c870f25a6108adf88a67dd23a2", "guid": "bfdfe7dc352907fc980b868725387e985b1c995dbed98a74e1d4c44ea83eaf3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de50c734a42c480a0d13c88fe5445775", "guid": "bfdfe7dc352907fc980b868725387e98c50d4437e7acb589eae6faa4afc04835", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc94bbd520f03bd72ef7b4dbaa4edaf", "guid": "bfdfe7dc352907fc980b868725387e986cbd9f98945145eb9f56903428dcdc9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5efc0f2e71fd7aef963c82c22945df2", "guid": "bfdfe7dc352907fc980b868725387e9829358fbc3bd8b7c49a5582f4ec031ef0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119643e4c874dd9238d81133bf468fe8", "guid": "bfdfe7dc352907fc980b868725387e982f1b6d6c3e214bcf71d983f14c3d74bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989daa79ba156b851ae14af46906b3a0a9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b24dc01cbd01e139f43dce956ccd9261", "guid": "bfdfe7dc352907fc980b868725387e98f10bd42ebb90b6bd5eae927aa5bc1f86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e0ca21beb55ede14d39a581b4935e7c", "guid": "bfdfe7dc352907fc980b868725387e9843bf07b9c6cc28bcb0ef96475830bbaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e31ecff1880f3d42dde162e3002b292", "guid": "bfdfe7dc352907fc980b868725387e9897f50a8046e2506549bdb4d402ef28f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7e6a7745c0bfeae107e75aa6ce6d844", "guid": "bfdfe7dc352907fc980b868725387e985922fc01134b7804c443bb87cfdd123b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d983216f7bfd4ade0fdb0f4080b57d49", "guid": "bfdfe7dc352907fc980b868725387e98b99291fe524c86fc056370e8a0f821be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b526589b71edbd96da65bde0373b186f", "guid": "bfdfe7dc352907fc980b868725387e9881d4db5003a68a8b9b64da19dbf967ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851ff76a1a39f04cfe4448594bea99a4c", "guid": "bfdfe7dc352907fc980b868725387e9886b57c55a876b9470465eed683162a88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b3888daa654831a09d9f87f89c5dbf1", "guid": "bfdfe7dc352907fc980b868725387e9861330553f7fe162574f2ade22382791f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca5985e373a9dc8b7d508f168b962618", "guid": "bfdfe7dc352907fc980b868725387e985c24b228791b2c987fd77984c0790993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832f79ef6664e8ff5c9dfc180045665b5", "guid": "bfdfe7dc352907fc980b868725387e9824fa5f337caedc30235c8947f615f9a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98102a07d3a0f0c22ff4b00e2bea9f25c8", "guid": "bfdfe7dc352907fc980b868725387e985e2616d0ec1bffd414cf25b4c63ba122"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981575823b11f7d740729a0abf1eb0ca9d", "guid": "bfdfe7dc352907fc980b868725387e984a1a2774f6b2777dc237bb18ccd2b00f"}], "guid": "bfdfe7dc352907fc980b868725387e98badc07441c3d3f800ea824e69bb5beff", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e98e3f6bfa9b742e772ecd92d7503f55e33"}], "guid": "bfdfe7dc352907fc980b868725387e98d7f82e88a905b7e353a4e6f9718eb7f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cc8ac38168987ce84f85d8beb6728369", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98558074b4aea693c85351e88b3b5ba2ae", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}