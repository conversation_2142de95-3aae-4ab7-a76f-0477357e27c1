{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f468de59dfe1553fc90c603a91d20f20", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da1d76674a7238fe5599da7c4ad9764c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb875b649631f680be20ad323619c74c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98525da784869e238ec0a0bf9b0b63d516", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb875b649631f680be20ad323619c74c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a28856bf4a924d13082e96dbcc12cc0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985e51e6ea47474a7fa67b3570e999382a", "guid": "bfdfe7dc352907fc980b868725387e98b432d7d19a76cb57970fa015e1382884", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98da12a821bf5d82da46aef3b46ba847d5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98025c7b47359ed9bee20a7506a2bbf908", "guid": "bfdfe7dc352907fc980b868725387e98b2bbc9a584d76347d980f3a6ad691ff8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feab4f9922f4babf10579b405f4b34ec", "guid": "bfdfe7dc352907fc980b868725387e98895402059bac14025afacd9aa8ab0bfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f98f8b8cd4b9f522d69f48e557342c53", "guid": "bfdfe7dc352907fc980b868725387e983112fd8c85b950a7800694d30b0a4ba0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f596bed320ac78c474ebce0741564ae9", "guid": "bfdfe7dc352907fc980b868725387e984ec69e7117a04d4fc1d0b7d6de53c68b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810abfc3ae503cfca35634ce2ba37f822", "guid": "bfdfe7dc352907fc980b868725387e984d5d5bae5942ecda290a931d1f2457cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7a7cd1f995f8c950a5d43a13526936b", "guid": "bfdfe7dc352907fc980b868725387e98dd11d44cc61557bce379ebef798702de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c46174b99e53c4d9408e0ff7818e5d", "guid": "bfdfe7dc352907fc980b868725387e98b9437eda7fce09b16c993d03b17660a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98326b1302ea631db21d87ed6dc512f480", "guid": "bfdfe7dc352907fc980b868725387e98bee901adf3218d0ed80667fd335113d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850c67e6f32e45363670cc1a30f2cf743", "guid": "bfdfe7dc352907fc980b868725387e9806c2e29d31460337c89a8aefb0193b7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982590e35ecc4727ab9b35da1929224984", "guid": "bfdfe7dc352907fc980b868725387e9882e7fece13fc654cb14c6de9b0631e37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817374e29af7e77c5fc12f99c41755c3e", "guid": "bfdfe7dc352907fc980b868725387e98087f1ca32d762287dd7070b6e1d04209"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5cd6180890314715807c52090f32edf", "guid": "bfdfe7dc352907fc980b868725387e981f8042d6cf8035350490bf93773f92de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a7b1ab78aea0ba6db3c23431bc9471e", "guid": "bfdfe7dc352907fc980b868725387e98f13b26233fc7188e73aa9b5a57a5f64b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b95e71ae2db9c24341c5ad6f6a6b2273", "guid": "bfdfe7dc352907fc980b868725387e98cfb8218c1ccfee08dd47100ac9b49de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af34b8f3607883db093192b69b2c4029", "guid": "bfdfe7dc352907fc980b868725387e98c810499aa83154be0d67b29426d66162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814db049a07db11f2b417761fa4989529", "guid": "bfdfe7dc352907fc980b868725387e98456fe823968e4e6221d9a6c07e7ad0d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d635fd4e84d0273b030d2a680b7ebdf", "guid": "bfdfe7dc352907fc980b868725387e98b2adab852c0465e1118edaa52250726e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981514b17cc3b9d301fbce5458d285fe43", "guid": "bfdfe7dc352907fc980b868725387e98d935e8354c26e3428dc6b8b9b6bc2368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881792443e64b9a61b7bb135cc9112b67", "guid": "bfdfe7dc352907fc980b868725387e98f8bca5f9054d0735b1328fa5055f23e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98087e25e955728f11f51c12b0f2a2dcf3", "guid": "bfdfe7dc352907fc980b868725387e986af907df8ab40a61f40cd25ac0467692"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981571ab11eac54df0f4c4df267e8c7883", "guid": "bfdfe7dc352907fc980b868725387e9890b11d9110b4dd148c7e1f8d5e8ea1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0e0c713de343bf34b96298919c2f5c6", "guid": "bfdfe7dc352907fc980b868725387e98a41b7399f8c1656049eff2de1f2d4dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897459cb85177d5ab3403cc03ad6242c2", "guid": "bfdfe7dc352907fc980b868725387e98d18c15f2342434bf15d394735c087349"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feb806ae7986c69dd4a11212d972cf19", "guid": "bfdfe7dc352907fc980b868725387e98fce2a3819deb90e91567526ea7fc2e5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880b7edda338e4258f9916889805aed8a", "guid": "bfdfe7dc352907fc980b868725387e98a05eca2f023bf0d3d28df5cb4e7b61f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980495e6968de52427a21c9a5b3d0da46e", "guid": "bfdfe7dc352907fc980b868725387e9832c7aebdea1957ba1b85f189689ae647"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98587ea9d5fd7a6380750adbc146599ba1", "guid": "bfdfe7dc352907fc980b868725387e986346518aeb5912cb9fdf66ecdb422c4e"}], "guid": "bfdfe7dc352907fc980b868725387e9818d55e82230273baaf167aca77c65999", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981d8d83d3ace7c58ebce387c74bdd8e0a", "guid": "bfdfe7dc352907fc980b868725387e98afe853108959e893ccec6795c1dffa52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98313deb4c669aeab28184d4597ebb2c1f", "guid": "bfdfe7dc352907fc980b868725387e9807d174b74f400a3c0522465f77f53624"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e9872f9b9d5aa3a043e3650e2f0b8f7b7ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f101059fb6bf7b94e44131db4ff0e3d3", "guid": "bfdfe7dc352907fc980b868725387e98eff3505cc8efe957e06236fd69328540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8b0d4ba0eb86e5e9cf9109401ecfb13", "guid": "bfdfe7dc352907fc980b868725387e98233de6ee85312094c51bef847bebf5ce"}], "guid": "bfdfe7dc352907fc980b868725387e987852923c6706431e969638a4e38cb7df", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98244b3f0e731a0969847a615824287888", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e9886fd9dff156f36f2b155b36d323c203a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}