{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c5b2406cd4cd9ea23ca904cf91af207", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bce04919b890bd7aed80d2f6fa8850e8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98921d3ee04829f80a6efe036a1a82b0cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a37971b3de621c0e880098ce46b6a95f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98921d3ee04829f80a6efe036a1a82b0cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_3.32.7/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f292dc7022274121f181fce13cce41f0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c8af0b348b24dad6d0fd895dc7ba1a98", "guid": "bfdfe7dc352907fc980b868725387e98def3817ff757e8824fb55deff783e74d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff6d251d71fdba1b8fefdc7d657d0d4a", "guid": "bfdfe7dc352907fc980b868725387e98d3f6cb3d6d13f5952189dca7019c4e8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5456d9141410d4883e7c87e7a01543", "guid": "bfdfe7dc352907fc980b868725387e98a525d24ea560a195f343bd7a4da9df7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98202da9323bf32e24761e39463e62f34c", "guid": "bfdfe7dc352907fc980b868725387e98787893c247d785e774cdb5a9b1d413cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98732825a4083ae772839b1f3c62b17531", "guid": "bfdfe7dc352907fc980b868725387e98d629e5c02acaf107788664fef609a357", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426f3349d7f11eda09c666ae406d53a2", "guid": "bfdfe7dc352907fc980b868725387e983114fc70a17993afe610b5a108b1ab0f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982c000c07650eaff32c57e4ed7a779f7d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc35e3d3c4ebd022d737d4d148790436", "guid": "bfdfe7dc352907fc980b868725387e987d068e393c7db76c74bfb3478f647423"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e579a3bc2ba349b8a682d721f027cee", "guid": "bfdfe7dc352907fc980b868725387e981ebabe205c557e279d5887ba334a5ee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c39be862c36e426275767b4a63da22b", "guid": "bfdfe7dc352907fc980b868725387e98add256552759310d2ebd6e8d76b209a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983989c6d641296a5f40e482adcf10bee1", "guid": "bfdfe7dc352907fc980b868725387e98bf49c8a1a75d78998861fbc3f76013cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da6a2ebf510dde5b8bce68ae0e1c929f", "guid": "bfdfe7dc352907fc980b868725387e98d0886ad09c40f4a98ec7536038e936cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887c45dc58abfae7a78c6dc898eae8549", "guid": "bfdfe7dc352907fc980b868725387e9824298712a7118f05c333bf26b3ec5c80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c71964e71c4cea11af96fc79994f3a4", "guid": "bfdfe7dc352907fc980b868725387e98926603937a5484e83a973d5ff233d7f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985118155a91c87c05f7574a8ee5985560", "guid": "bfdfe7dc352907fc980b868725387e9823fcc93dee70429b699c4afc34d205fa"}], "guid": "bfdfe7dc352907fc980b868725387e9849c17fa96abf8401a2430c1fe1b392c8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d417e69938a8be3304b445dd8565a70", "guid": "bfdfe7dc352907fc980b868725387e9807d167788c31b760c3b7dd16df674602"}], "guid": "bfdfe7dc352907fc980b868725387e98a5829fed6fe0b0d418f78ccfbe06fcac", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98025b77a20342d026edf273eff28d1d3d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98687f14f834c716a0a6b72b2f678d5d39", "name": "<PERSON><PERSON>"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98ae4cba9dafde33c1f8a32ad5773d6cd3", "name": "GCDWebServer"}, {"guid": "bfdfe7dc352907fc980b868725387e989f6c9e975471ecf29d91b182619839be", "name": "HLSCachingReverseProxyServer"}, {"guid": "bfdfe7dc352907fc980b868725387e980a765b211b0c8c508dddcb830a52bbab", "name": "PINCache"}], "guid": "bfdfe7dc352907fc980b868725387e986cd267420621a3ff1d813b97fdcfc70c", "name": "better_player_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982979aa52633ff059b6ad20ce50d04f84", "name": "better_player_plus.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}