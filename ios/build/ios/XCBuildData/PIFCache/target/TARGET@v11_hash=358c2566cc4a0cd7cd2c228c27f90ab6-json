{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b4f0de27a84a1c8f748999eeaf520e91", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9818dc297fa74601a0d0daca73d860044c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa06691561379b3dff962b873f2a5d42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e980b88f2ff4be6d0338826d801ef4d72a6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa06691561379b3dff962b873f2a5d42", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983a8a4d85aea7d5a61ce7c0e05a84382c", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98521e28b875a6bd3fba68d3785d76618b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98dcd88bf818a7b9b128119acf66a4d22f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98efc56e0096617688d8877beacde7fbb3", "guid": "bfdfe7dc352907fc980b868725387e9857ff2af0a74fb2e7bf2fd469efc68223"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac0cbb23353d0d61fa35d684008a227", "guid": "bfdfe7dc352907fc980b868725387e9858c26cb574f4451580caa8d4965329f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984121e9b8dd146eb398ac6eee44724fe6", "guid": "bfdfe7dc352907fc980b868725387e9898106e3d36807981842e37eba511250b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d97604d1b6dccd29a4a6cdf5a00a2b2", "guid": "bfdfe7dc352907fc980b868725387e980e9cc8b60490347091d2118d52f7dac1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a64cbbb5e585570d06e362b64eeb33", "guid": "bfdfe7dc352907fc980b868725387e98b870b724a921a637df95a6a297e9e58c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874065a3fb90ec286dca66361982a4f73", "guid": "bfdfe7dc352907fc980b868725387e98a20050c250df240b92fc72d44504f5bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa5e8208b0e0c8d5ef256780652591f4", "guid": "bfdfe7dc352907fc980b868725387e98dd64ecb9803525a6860329e80ee30633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b89165e9c02ac3961e268a126d11b4bd", "guid": "bfdfe7dc352907fc980b868725387e98955b0fa2038cc88b2c0ce77f48032c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98423bd9051b9b9ed00cdd3a3563f11277", "guid": "bfdfe7dc352907fc980b868725387e98ccf620b46ce4285969a68a67d7a3bb21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0b110f1a94905753cef3907c2697907", "guid": "bfdfe7dc352907fc980b868725387e982c3f1030079a5d26ab648747a6d5a9fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f67ec261470c41879c5be2ebc454117", "guid": "bfdfe7dc352907fc980b868725387e985c1a207d02c7f148d6390387e408f30c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840054b63fccd89909ed3c2e0c822a8df", "guid": "bfdfe7dc352907fc980b868725387e9814e4271b9466b894a46ab783dd1be419"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98036350bb17c7fc88ca24483163240690", "guid": "bfdfe7dc352907fc980b868725387e986a01b182fce6e09c955880b8683a6504"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bbdb0839c5869d825ad2bdb04837a6e", "guid": "bfdfe7dc352907fc980b868725387e98fc58e514dbd56052d06b3458b603c5a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa617fc5b6e59bb24918b5170783237f", "guid": "bfdfe7dc352907fc980b868725387e983b305ae7c6936df43505de38535ca840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988595c3e1b00b7649a7151fb36b373e9e", "guid": "bfdfe7dc352907fc980b868725387e983e8679c983a9e17e00b4589aaf66a32d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843682bbd271ebf826c0ef3b107a91bae", "guid": "bfdfe7dc352907fc980b868725387e9816656ca78f240975b4e8a6258914bb5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef501fc525f41916314df8387d82c9d7", "guid": "bfdfe7dc352907fc980b868725387e9869cc60ba3882e5ccd3f8e9711f37ab93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860811602d48fb54d2411d4152d5a0f9f", "guid": "bfdfe7dc352907fc980b868725387e985b6ae8950e482ddb62e22db2ab22af9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d14f6886f2e844ee0b728c65d7211409", "guid": "bfdfe7dc352907fc980b868725387e98f76d769e5057d8b39ef52b9f8959d252"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98225ac55793dd8dd26d3b68742dd02e86", "guid": "bfdfe7dc352907fc980b868725387e9812e67586ecd5df2648752487a8c548d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de0115e919b1227d8fd12b2b1bd87ec8", "guid": "bfdfe7dc352907fc980b868725387e9805bea953aec7588484834d0a10c7832e"}], "guid": "bfdfe7dc352907fc980b868725387e98d0b885c2599700ccf03f779970a42afa", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}