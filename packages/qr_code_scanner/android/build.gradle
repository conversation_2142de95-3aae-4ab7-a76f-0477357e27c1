group 'net.touchcapture.qr.flutterqr'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.8.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion 35
    namespace "net.touchcapture.qr.flutterqr"

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        // minSdkVersion is determined by Native View.
        minSdkVersion 23
        targetSdkVersion 35
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
    }

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true
        // Sets Java compatibility to Java 8
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation('com.journeyapps:zxing-android-embedded:4.3.0') { transitive = false }
    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'com.google.zxing:core:3.5.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.0'
}
