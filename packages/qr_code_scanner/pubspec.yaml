name: qr_code_scanner
description: QR code scanner that can be embedded inside flutter. It uses zxing in Android and MTBBarcode scanner in iOS.
version: 1.0.1
homepage: https://juliuscanute.com
repository: https://github.com/juliuscanute/qr_code_scanner

environment:
  sdk: '>=2.17.0 <3.0.0'
  flutter: ">=1.12.0"

dependencies:
  js: ^0.6.3
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter

dev_dependencies:
  flutter_lints: ^1.0.4

flutter:
  plugin:
    platforms:
      android:
        package: net.touchcapture.qr.flutterqr
        pluginClass: FlutterQrPlugin
      ios:
        pluginClass: FlutterQrPlugin
#      web:
#        pluginClass: FlutterQrPlugin
#        fileName: flutter_qr_web.dart
