import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/dub_meditation.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/meditations_model.dart';
import 'package:injectable/injectable.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:retrofit/retrofit.dart';
part 'meditations_services.g.dart';

@LazySingleton()
@RestApi(baseUrl: '')
abstract class MeditationsServices {
  @factoryMethod
  factory MeditationsServices(Dio dio, Configuration configuration) {
    return _MeditationsServices(dio, baseUrl: configuration.getApiUrl);
  }

// Services Method to fetch Meditations
  @GET('meditation')
  Future<BaseResponseModel<List<MeditationModel>>> fetchMeditations();

  @GET('meditation/{id}')
  Future<MeditationModel> getMeditationsById({
    @Path('id') required int id,
  });


  // Services Method
  @GET('questions/thought/{id}')
  Future<List<QuestionsModel>> getQuestionsByThoughtId({
    @Path('id') required int id,
  });

  @GET('meditation/sub/{id}')
  Future<BaseResponseModel<List<SubMeditation>>> getsubMeditation({
    @Path('id') required String id,
  });


}
