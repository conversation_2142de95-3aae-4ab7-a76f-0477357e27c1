import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/data/repositories/base_repository_impl.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/domain/services/remote/concerns_services.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/dub_meditation.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/meditations_model.dart';
import 'package:clean_arc/features/meditation_feature/domain/services/remote/meditations_services.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

abstract class MeditationsRepository {
// Repository method for Meditations
  Future<Either<Failure, BaseResponseModel<List<MeditationModel>>>>
      fetchMeditations();

  Future<Either<Failure, MeditationModel>> getMeditationsById({
    required int id,
  });

  // Repository
  Future<Either<Failure, List<QuestionsModel>>> getQuestionsByThoughtId({
    required int id,
  });

  Future<Either<Failure, BaseResponseModel<List<SubMeditation>>>>
      getsubMeditation({
    required String id,
  });
}

@LazySingleton(as: MeditationsRepository)
class MeditationsRepositoryImpl extends BaseRepositoryImpl
    implements MeditationsRepository {
  final MeditationsServices _services;

  MeditationsRepositoryImpl(super.logger, this._services);

  // Repository Implementation for Meditations
  Future<Either<Failure, BaseResponseModel<List<MeditationModel>>>>
      fetchMeditations() {
    return request(() async {
      final result = await _services.fetchMeditations();
      return Right(result);
    });
  }

  Future<Either<Failure, MeditationModel>> getMeditationsById({
    required int id,
  }) {
    return request(() async {
      final result = await _services.getMeditationsById(id: id);
      return Right(result);
    });
  }

  // Repository Implementation
  Future<Either<Failure, List<QuestionsModel>>> getQuestionsByThoughtId({
    required int id,
  }) {
    return request(() async {
      final result = await _services.getQuestionsByThoughtId(id: id);
      return Right(result);
    });
  }

  @override
  Future<Either<Failure, BaseResponseModel<List<SubMeditation>>>>
      getsubMeditation({
    required String id,
  }) {
    return request(() async {
      final result = await _services.getsubMeditation(
        id: id,
      );
      return Right(result);
    });
  }
}
