class SubCategoryModel {
  SubCategoryModel({
    required this.id,
    required this.title,
    required this.description,
    required this.audioUrl,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final String? title;
  final String? description;
  final String? audioUrl;
  final num? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory SubCategoryModel.fromJson(Map<String, dynamic> json) {
    return SubCategoryModel(
      id: json["id"],
      title: json["title"],
      description: json["description"],
      audioUrl: json["audio_url"],
      isActive: json["is_active"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "audio_url": audioUrl,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class MeditationModel {
  MeditationModel({
    required this.id,
    required this.title,
    required this.description,
    required this.image,
    required this.isActive,
    required this.meditationId,
    required this.audioUrl,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
    required this.subCategories,
  });

  final int? id;
  final String? title;
  final String? description;
  final String? image;
  final num? isActive;
  final dynamic meditationId;
  final dynamic audioUrl;
  final String? type;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<SubCategoryModel>? subCategories;

  factory MeditationModel.fromJson(Map<String, dynamic> json) {
    return MeditationModel(
      id: json["id"],
      title: json["title"],
      description: json["description"],
      image: json["image"],
      isActive: json["is_active"],
      meditationId: json["meditation_id"],
      audioUrl: json["audio_url"],
      type: json["type"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      subCategories: json["sub_categories"] != null
          ? (json["sub_categories"] as List)
              .map((e) => SubCategoryModel.fromJson(e))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "image": image,
        "is_active": isActive,
        "meditation_id": meditationId,
        "audio_url": audioUrl,
        "type": type,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "sub_categories": subCategories?.map((e) => e.toJson()).toList(),
      };
}
