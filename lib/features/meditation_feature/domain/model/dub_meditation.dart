class SubMeditation {
  SubMeditation({
    required this.subId,
    required this.meditationId,
    required this.subTitle,
    required this.subType,
    required this.audioUrls,
  });

  final int? subId;
  final int? meditationId;
  final String? subTitle;
  final String? subType;
  final String? audioUrls;

  factory SubMeditation.fromJson(Map<String, dynamic> json){
    return SubMeditation(
      subId: json["sub_id"],
      meditationId: json["meditation_id"],
      subTitle: json["sub_title"],
      subType: json["sub_type"],
      audioUrls: json["audio_urls"],
    );
  }

  Map<String, dynamic> toJson() => {
    "sub_id": subId,
    "meditation_id": meditationId,
    "sub_title": subTitle,
    "sub_type": subType,
    "audio_urls": audioUrls,
  };

}
