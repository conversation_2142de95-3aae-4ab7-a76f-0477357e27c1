import 'dart:convert';
import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/dub_meditation.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/meditations_model.dart';
import 'package:clean_arc/features/meditation_feature/domain/repository/meditations_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

part 'meditations_cubit.freezed.dart';

part 'meditations_state.dart';

@injectable
class MeditationsCubit extends Cubit<MeditationsState> {
  MeditationsCubit(this.repository) : super(MeditationsState.initial());

  final MeditationsRepository repository;

  Future<void> fetchMeditations() async {
    emit(state.copyWith(
      isLoadingMeditations: true,
      successMeditations: null,
      errorMeditations: null,
    ));
    final result = await repository.fetchMeditations();

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingMeditations: false,
          errorMeditations: failure,
          successMeditations: null,
        ));
      },
      (concerns) {
        emit(state.copyWith(
          isLoadingMeditations: false,
          errorMeditations: null,
          successMeditations: concerns,
        ));
      },
    );
  }

  Future<void> getMeditationsById({
    required int id,
  }) async {
    emit(state.copyWith(
      isLoadingGetMeditationById: true,
      successGetMeditationById: null,
      errorGetMeditationById: null,
    ));

    final result = await repository.getMeditationsById(id: id);

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingGetMeditationById: false,
          errorGetMeditationById: failure,
          successGetMeditationById: null,
        ));
      },
      (success) {
        emit(state.copyWith(
          isLoadingGetMeditationById: false,
          errorGetMeditationById: null,
          successGetMeditationById: success,
        ));
      },
    );
  }

  Future<void> getQuestionsByThoughtId({
    required int id,
  }) async {
    emit(state.copyWith(
      isLoadingQuestions: true,
      successQuestions: null,
      errorQuestions: null,
    ));

    final result = await repository.getQuestionsByThoughtId(id: id);

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingQuestions: false,
          errorQuestions: failure,
          successQuestions: null,
        ));
      },
      (success) {
        emit(state.copyWith(
          isLoadingQuestions: false,
          errorQuestions: null,
          successQuestions: success,
        ));
      },
    );
  }

  Future<void> getsubMeditation({
    required String id,
  }) async {
    emit(state.copyWith(
      isLoadingGetsubMeditation: true,
      successGetsubMeditation: null,
      errorGetsubMeditation: null,
    ));

    final result = await repository.getsubMeditation(
      id: id,
    );

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingGetsubMeditation: false,
          errorGetsubMeditation: failure,
          successGetsubMeditation: null,
        ));
      },
      (response) {
        emit(state.copyWith(
          isLoadingGetsubMeditation: false,
          errorGetsubMeditation: null,
          successGetsubMeditation: response,
        ));
      },
    );
  }
}
