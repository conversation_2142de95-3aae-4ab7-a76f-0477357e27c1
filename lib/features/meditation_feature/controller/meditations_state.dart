part of 'meditations_cubit.dart';

@freezed
class MeditationsState with _$MeditationsState {
  factory MeditationsState({
    ///============================================================

    required bool? isLoadingMeditations,
    required Failure? errorMeditations,
    required BaseResponseModel<List<MeditationModel>>? successMeditations,

    ///============================================================
    required bool? isLoadingGetMeditationById,
    required Failure? errorGetMeditationById,
    required MeditationModel? successGetMeditationById,

    ///============================================================

    required bool? isLoadingQuestions,
    required Failure? errorQuestions,
    required List<QuestionsModel>? successQuestions,

    ///============================================================

    required bool? isLoadingGetsubMeditation,
    required Failure? errorGetsubMeditation,
    required BaseResponseModel<List<SubMeditation>>? successGetsubMeditation,
    @Default(true) bool isChecked,
  }) = _$MeditationsStateImpl;

  factory MeditationsState.initial() => MeditationsState(
        errorMeditations: null,
        isLoadingMeditations: null,
        successMeditations: null,
        errorGetMeditationById: null,
        isLoadingGetMeditationById: null,
        successGetMeditationById: null,
        isLoadingQuestions: null,
        successQuestions: null,
        errorQuestions: null,

        ///===================================
        isLoadingGetsubMeditation: null,
        errorGetsubMeditation: null,
        successGetsubMeditation: null,
      );
}
