// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'meditations_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MeditationsState {
  ///============================================================
  bool? get isLoadingMeditations => throw _privateConstructorUsedError;
  Failure? get errorMeditations => throw _privateConstructorUsedError;
  BaseResponseModel<List<MeditationModel>>? get successMeditations =>
      throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingGetMeditationById => throw _privateConstructorUsedError;
  Failure? get errorGetMeditationById => throw _privateConstructorUsedError;
  MeditationModel? get successGetMeditationById =>
      throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingQuestions => throw _privateConstructorUsedError;
  Failure? get errorQuestions => throw _privateConstructorUsedError;
  List<QuestionsModel>? get successQuestions =>
      throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingGetsubMeditation => throw _privateConstructorUsedError;
  Failure? get errorGetsubMeditation => throw _privateConstructorUsedError;
  BaseResponseModel<List<SubMeditation>>? get successGetsubMeditation =>
      throw _privateConstructorUsedError;
  bool get isChecked => throw _privateConstructorUsedError;

  /// Create a copy of MeditationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MeditationsStateCopyWith<MeditationsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MeditationsStateCopyWith<$Res> {
  factory $MeditationsStateCopyWith(
          MeditationsState value, $Res Function(MeditationsState) then) =
      _$MeditationsStateCopyWithImpl<$Res, MeditationsState>;
  @useResult
  $Res call(
      {bool? isLoadingMeditations,
      Failure? errorMeditations,
      BaseResponseModel<List<MeditationModel>>? successMeditations,
      bool? isLoadingGetMeditationById,
      Failure? errorGetMeditationById,
      MeditationModel? successGetMeditationById,
      bool? isLoadingQuestions,
      Failure? errorQuestions,
      List<QuestionsModel>? successQuestions,
      bool? isLoadingGetsubMeditation,
      Failure? errorGetsubMeditation,
      BaseResponseModel<List<SubMeditation>>? successGetsubMeditation,
      bool isChecked});
}

/// @nodoc
class _$MeditationsStateCopyWithImpl<$Res, $Val extends MeditationsState>
    implements $MeditationsStateCopyWith<$Res> {
  _$MeditationsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MeditationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingMeditations = freezed,
    Object? errorMeditations = freezed,
    Object? successMeditations = freezed,
    Object? isLoadingGetMeditationById = freezed,
    Object? errorGetMeditationById = freezed,
    Object? successGetMeditationById = freezed,
    Object? isLoadingQuestions = freezed,
    Object? errorQuestions = freezed,
    Object? successQuestions = freezed,
    Object? isLoadingGetsubMeditation = freezed,
    Object? errorGetsubMeditation = freezed,
    Object? successGetsubMeditation = freezed,
    Object? isChecked = null,
  }) {
    return _then(_value.copyWith(
      isLoadingMeditations: freezed == isLoadingMeditations
          ? _value.isLoadingMeditations
          : isLoadingMeditations // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorMeditations: freezed == errorMeditations
          ? _value.errorMeditations
          : errorMeditations // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successMeditations: freezed == successMeditations
          ? _value.successMeditations
          : successMeditations // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<List<MeditationModel>>?,
      isLoadingGetMeditationById: freezed == isLoadingGetMeditationById
          ? _value.isLoadingGetMeditationById
          : isLoadingGetMeditationById // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetMeditationById: freezed == errorGetMeditationById
          ? _value.errorGetMeditationById
          : errorGetMeditationById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetMeditationById: freezed == successGetMeditationById
          ? _value.successGetMeditationById
          : successGetMeditationById // ignore: cast_nullable_to_non_nullable
              as MeditationModel?,
      isLoadingQuestions: freezed == isLoadingQuestions
          ? _value.isLoadingQuestions
          : isLoadingQuestions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorQuestions: freezed == errorQuestions
          ? _value.errorQuestions
          : errorQuestions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successQuestions: freezed == successQuestions
          ? _value.successQuestions
          : successQuestions // ignore: cast_nullable_to_non_nullable
              as List<QuestionsModel>?,
      isLoadingGetsubMeditation: freezed == isLoadingGetsubMeditation
          ? _value.isLoadingGetsubMeditation
          : isLoadingGetsubMeditation // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetsubMeditation: freezed == errorGetsubMeditation
          ? _value.errorGetsubMeditation
          : errorGetsubMeditation // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetsubMeditation: freezed == successGetsubMeditation
          ? _value.successGetsubMeditation
          : successGetsubMeditation // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<List<SubMeditation>>?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$$MeditationsStateImplImplCopyWith<$Res>
    implements $MeditationsStateCopyWith<$Res> {
  factory _$$$MeditationsStateImplImplCopyWith(
          _$$MeditationsStateImplImpl value,
          $Res Function(_$$MeditationsStateImplImpl) then) =
      __$$$MeditationsStateImplImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isLoadingMeditations,
      Failure? errorMeditations,
      BaseResponseModel<List<MeditationModel>>? successMeditations,
      bool? isLoadingGetMeditationById,
      Failure? errorGetMeditationById,
      MeditationModel? successGetMeditationById,
      bool? isLoadingQuestions,
      Failure? errorQuestions,
      List<QuestionsModel>? successQuestions,
      bool? isLoadingGetsubMeditation,
      Failure? errorGetsubMeditation,
      BaseResponseModel<List<SubMeditation>>? successGetsubMeditation,
      bool isChecked});
}

/// @nodoc
class __$$$MeditationsStateImplImplCopyWithImpl<$Res>
    extends _$MeditationsStateCopyWithImpl<$Res, _$$MeditationsStateImplImpl>
    implements _$$$MeditationsStateImplImplCopyWith<$Res> {
  __$$$MeditationsStateImplImplCopyWithImpl(_$$MeditationsStateImplImpl _value,
      $Res Function(_$$MeditationsStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of MeditationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingMeditations = freezed,
    Object? errorMeditations = freezed,
    Object? successMeditations = freezed,
    Object? isLoadingGetMeditationById = freezed,
    Object? errorGetMeditationById = freezed,
    Object? successGetMeditationById = freezed,
    Object? isLoadingQuestions = freezed,
    Object? errorQuestions = freezed,
    Object? successQuestions = freezed,
    Object? isLoadingGetsubMeditation = freezed,
    Object? errorGetsubMeditation = freezed,
    Object? successGetsubMeditation = freezed,
    Object? isChecked = null,
  }) {
    return _then(_$$MeditationsStateImplImpl(
      isLoadingMeditations: freezed == isLoadingMeditations
          ? _value.isLoadingMeditations
          : isLoadingMeditations // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorMeditations: freezed == errorMeditations
          ? _value.errorMeditations
          : errorMeditations // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successMeditations: freezed == successMeditations
          ? _value.successMeditations
          : successMeditations // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<List<MeditationModel>>?,
      isLoadingGetMeditationById: freezed == isLoadingGetMeditationById
          ? _value.isLoadingGetMeditationById
          : isLoadingGetMeditationById // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetMeditationById: freezed == errorGetMeditationById
          ? _value.errorGetMeditationById
          : errorGetMeditationById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetMeditationById: freezed == successGetMeditationById
          ? _value.successGetMeditationById
          : successGetMeditationById // ignore: cast_nullable_to_non_nullable
              as MeditationModel?,
      isLoadingQuestions: freezed == isLoadingQuestions
          ? _value.isLoadingQuestions
          : isLoadingQuestions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorQuestions: freezed == errorQuestions
          ? _value.errorQuestions
          : errorQuestions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successQuestions: freezed == successQuestions
          ? _value._successQuestions
          : successQuestions // ignore: cast_nullable_to_non_nullable
              as List<QuestionsModel>?,
      isLoadingGetsubMeditation: freezed == isLoadingGetsubMeditation
          ? _value.isLoadingGetsubMeditation
          : isLoadingGetsubMeditation // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetsubMeditation: freezed == errorGetsubMeditation
          ? _value.errorGetsubMeditation
          : errorGetsubMeditation // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetsubMeditation: freezed == successGetsubMeditation
          ? _value.successGetsubMeditation
          : successGetsubMeditation // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<List<SubMeditation>>?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$$MeditationsStateImplImpl implements _$MeditationsStateImpl {
  _$$MeditationsStateImplImpl(
      {required this.isLoadingMeditations,
      required this.errorMeditations,
      required this.successMeditations,
      required this.isLoadingGetMeditationById,
      required this.errorGetMeditationById,
      required this.successGetMeditationById,
      required this.isLoadingQuestions,
      required this.errorQuestions,
      required final List<QuestionsModel>? successQuestions,
      required this.isLoadingGetsubMeditation,
      required this.errorGetsubMeditation,
      required this.successGetsubMeditation,
      this.isChecked = true})
      : _successQuestions = successQuestions;

  ///============================================================
  @override
  final bool? isLoadingMeditations;
  @override
  final Failure? errorMeditations;
  @override
  final BaseResponseModel<List<MeditationModel>>? successMeditations;

  ///============================================================
  @override
  final bool? isLoadingGetMeditationById;
  @override
  final Failure? errorGetMeditationById;
  @override
  final MeditationModel? successGetMeditationById;

  ///============================================================
  @override
  final bool? isLoadingQuestions;
  @override
  final Failure? errorQuestions;
  final List<QuestionsModel>? _successQuestions;
  @override
  List<QuestionsModel>? get successQuestions {
    final value = _successQuestions;
    if (value == null) return null;
    if (_successQuestions is EqualUnmodifiableListView)
      return _successQuestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  ///============================================================
  @override
  final bool? isLoadingGetsubMeditation;
  @override
  final Failure? errorGetsubMeditation;
  @override
  final BaseResponseModel<List<SubMeditation>>? successGetsubMeditation;
  @override
  @JsonKey()
  final bool isChecked;

  @override
  String toString() {
    return 'MeditationsState(isLoadingMeditations: $isLoadingMeditations, errorMeditations: $errorMeditations, successMeditations: $successMeditations, isLoadingGetMeditationById: $isLoadingGetMeditationById, errorGetMeditationById: $errorGetMeditationById, successGetMeditationById: $successGetMeditationById, isLoadingQuestions: $isLoadingQuestions, errorQuestions: $errorQuestions, successQuestions: $successQuestions, isLoadingGetsubMeditation: $isLoadingGetsubMeditation, errorGetsubMeditation: $errorGetsubMeditation, successGetsubMeditation: $successGetsubMeditation, isChecked: $isChecked)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$$MeditationsStateImplImpl &&
            (identical(other.isLoadingMeditations, isLoadingMeditations) ||
                other.isLoadingMeditations == isLoadingMeditations) &&
            (identical(other.errorMeditations, errorMeditations) ||
                other.errorMeditations == errorMeditations) &&
            (identical(other.successMeditations, successMeditations) ||
                other.successMeditations == successMeditations) &&
            (identical(other.isLoadingGetMeditationById,
                    isLoadingGetMeditationById) ||
                other.isLoadingGetMeditationById ==
                    isLoadingGetMeditationById) &&
            (identical(other.errorGetMeditationById, errorGetMeditationById) ||
                other.errorGetMeditationById == errorGetMeditationById) &&
            (identical(
                    other.successGetMeditationById, successGetMeditationById) ||
                other.successGetMeditationById == successGetMeditationById) &&
            (identical(other.isLoadingQuestions, isLoadingQuestions) ||
                other.isLoadingQuestions == isLoadingQuestions) &&
            (identical(other.errorQuestions, errorQuestions) ||
                other.errorQuestions == errorQuestions) &&
            const DeepCollectionEquality()
                .equals(other._successQuestions, _successQuestions) &&
            (identical(other.isLoadingGetsubMeditation,
                    isLoadingGetsubMeditation) ||
                other.isLoadingGetsubMeditation == isLoadingGetsubMeditation) &&
            (identical(other.errorGetsubMeditation, errorGetsubMeditation) ||
                other.errorGetsubMeditation == errorGetsubMeditation) &&
            (identical(
                    other.successGetsubMeditation, successGetsubMeditation) ||
                other.successGetsubMeditation == successGetsubMeditation) &&
            (identical(other.isChecked, isChecked) ||
                other.isChecked == isChecked));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoadingMeditations,
      errorMeditations,
      successMeditations,
      isLoadingGetMeditationById,
      errorGetMeditationById,
      successGetMeditationById,
      isLoadingQuestions,
      errorQuestions,
      const DeepCollectionEquality().hash(_successQuestions),
      isLoadingGetsubMeditation,
      errorGetsubMeditation,
      successGetsubMeditation,
      isChecked);

  /// Create a copy of MeditationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$$MeditationsStateImplImplCopyWith<_$$MeditationsStateImplImpl>
      get copyWith => __$$$MeditationsStateImplImplCopyWithImpl<
          _$$MeditationsStateImplImpl>(this, _$identity);
}

abstract class _$MeditationsStateImpl implements MeditationsState {
  factory _$MeditationsStateImpl(
      {required final bool? isLoadingMeditations,
      required final Failure? errorMeditations,
      required final BaseResponseModel<List<MeditationModel>>?
          successMeditations,
      required final bool? isLoadingGetMeditationById,
      required final Failure? errorGetMeditationById,
      required final MeditationModel? successGetMeditationById,
      required final bool? isLoadingQuestions,
      required final Failure? errorQuestions,
      required final List<QuestionsModel>? successQuestions,
      required final bool? isLoadingGetsubMeditation,
      required final Failure? errorGetsubMeditation,
      required final BaseResponseModel<List<SubMeditation>>?
          successGetsubMeditation,
      final bool isChecked}) = _$$MeditationsStateImplImpl;

  ///============================================================
  @override
  bool? get isLoadingMeditations;
  @override
  Failure? get errorMeditations;
  @override
  BaseResponseModel<List<MeditationModel>>? get successMeditations;

  ///============================================================
  @override
  bool? get isLoadingGetMeditationById;
  @override
  Failure? get errorGetMeditationById;
  @override
  MeditationModel? get successGetMeditationById;

  ///============================================================
  @override
  bool? get isLoadingQuestions;
  @override
  Failure? get errorQuestions;
  @override
  List<QuestionsModel>? get successQuestions;

  ///============================================================
  @override
  bool? get isLoadingGetsubMeditation;
  @override
  Failure? get errorGetsubMeditation;
  @override
  BaseResponseModel<List<SubMeditation>>? get successGetsubMeditation;
  @override
  bool get isChecked;

  /// Create a copy of MeditationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$$MeditationsStateImplImplCopyWith<_$$MeditationsStateImplImpl>
      get copyWith => throw _privateConstructorUsedError;
}
