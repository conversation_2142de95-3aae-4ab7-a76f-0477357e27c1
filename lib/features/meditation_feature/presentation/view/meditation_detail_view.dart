import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/meditations_model.dart';
import 'package:clean_arc/features/player_feature/controller/now_playing_controller.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/domain/services/songs_data.dart'
    as songs_data;
import 'package:clean_arc/features/user_layout/lip_feature/controller/lib_controller.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/termination_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MeditationDetailView extends StatefulWidget {
  final MeditationModel meditation;

  const MeditationDetailView({
    Key? key,
    required this.meditation,
  }) : super(key: key);

  @override
  State<MeditationDetailView> createState() => _MeditationDetailViewState();
}

class _MeditationDetailViewState extends State<MeditationDetailView> {
  late NowPlayingController controller;
  SubCategoryModel? currentSubCategory;

  @override
  void initState() {
    super.initState();
    controller = context.read<NowPlayingController>();
    // Get the first (and only) sub category
    if (widget.meditation.subCategories?.isNotEmpty ?? false) {
      currentSubCategory = widget.meditation.subCategories!.first;
    }
  }

  @override
  void dispose() {
    // Stop audio when leaving the page
    controller.player.stop();
    songs_data.currentSong = '';
    songs_data.currentPlayingUrl = '';
    super.dispose();
  }

  @override
  void deactivate() {
    // Also stop audio when navigating away
    controller.player.stop();
    songs_data.currentSong = '';
    songs_data.currentPlayingUrl = '';
    super.deactivate();
  }

  void _playPause() {
    if (currentSubCategory == null) return;

    final audioUrl = getBaseUrl + (currentSubCategory!.audioUrl ?? '');

    if (controller.player.playing && songs_data.currentPlayingUrl == audioUrl) {
      // Just pause, don't stop - this preserves the position
      controller.player.pause();
    } else {
      // Check if this is the same song that was paused
      if (songs_data.currentPlayingUrl == audioUrl) {
        // Same song, just resume playing from current position
        controller.player.play();
      } else {
        // Different song, stop current and start new one from beginning
        controller.player.stop();
        songs_data.currentSong = currentSubCategory!.title ?? '';
        songs_data.currentPlayingUrl = audioUrl;

        // Create playlist with single song
        controller.changetoSingerplaylist([
          SongModel(
            album: '',
            artist: widget.meditation.title ?? '',
            song: currentSubCategory!.title ?? '',
            url: audioUrl,
          )
        ], 0);

        controller.player.seek(Duration.zero);
        controller.player.play();
      }
    }
    setState(() {});
  }

  void _seekForward() {
    final currentPosition = controller.player.position;
    final newPosition = currentPosition + Duration(seconds: 10);
    final duration = controller.player.duration ?? Duration.zero;

    if (newPosition < duration) {
      controller.player.seek(newPosition);
    } else {
      controller.player.seek(duration);
    }
  }

  void _seekBackward() {
    final currentPosition = controller.player.position;
    final newPosition = currentPosition - Duration(seconds: 10);

    if (newPosition > Duration.zero) {
      controller.player.seek(newPosition);
    } else {
      controller.player.seek(Duration.zero);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (currentSubCategory == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(widget.meditation.title ?? ''),
        ),
        body: Center(
          child: Text('No audio available'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          ValueListenableBuilder<Set<int>>(
            valueListenable: BookManage.savedMeditations,
            builder: (context, savedBooks, child) {
              return IconButton(
                icon: Icon(
                  savedBooks.contains(widget.meditation.id)
                      ? Icons.bookmark
                      : Icons.bookmark_border,
                  color: Colors.orange,
                ),
                onPressed: () {
                  BookManage.toggleMeditation(
                    MeditationCashModel.fromJson(widget.meditation.toJson()),
                  );
                  setState(() {});
                },
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Top 3/4 section with image
          Container(
            height: MediaQuery.of(context).size.height * 0.45,
            width: double.infinity,
            padding: EdgeInsets.all(32),
            child: Center(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: AspectRatio(
                  aspectRatio: 1,
                  child: widget.meditation.image != null
                      ? Image.network(
                          getBaseUrl + widget.meditation.image!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[800],
                              child: Icon(
                                Icons.music_note,
                                size: 100,
                                color: Colors.white54,
                              ),
                            );
                          },
                        )
                      : Container(
                          color: Colors.grey[800],
                          child: Icon(
                            Icons.music_note,
                            size: 100,
                            color: Colors.white54,
                          ),
                        ),
                ),
              ),
            ),
          ),

          // Bottom 1/4 section with controls
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Song title
                  Text(
                    currentSubCategory!.title ?? '',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8),

                  // Artist name
                  Text(
                    widget.meditation.title ?? '',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 24),

                  // Progress bar
                  StreamBuilder<Duration>(
                    stream: controller.player.positionStream,
                    builder: (context, snapshot) {
                      final position = snapshot.data ?? Duration.zero;
                      final duration =
                          controller.player.duration ?? Duration.zero;

                      return Column(
                        children: [
                          Slider(
                            value: duration.inMilliseconds > 0
                                ? position.inMilliseconds /
                                    duration.inMilliseconds
                                : 0.0,
                            onChanged: (value) {
                              final newPosition = Duration(
                                milliseconds:
                                    (value * duration.inMilliseconds).round(),
                              );
                              controller.player.seek(newPosition);
                            },
                            activeColor: Colors.black,
                            inactiveColor: Colors.grey[300],
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  _formatDuration(position),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                Text(
                                  _formatDuration(duration),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  SizedBox(height: 24),

                  // Control buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // 10 seconds backward
                      IconButton(
                        onPressed: _seekBackward,
                        icon: Icon(
                          Icons.replay_10,
                          color: Colors.black,
                          size: 32,
                        ),
                      ),

                      // Play/Pause button
                      StreamBuilder<bool>(
                        stream: controller.player.playingStream,
                        builder: (context, snapshot) {
                          final isPlaying = snapshot.data ?? false;
                          final isCurrentSong = songs_data.currentPlayingUrl ==
                              (getBaseUrl +
                                  (currentSubCategory!.audioUrl ?? ''));

                          return Container(
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.black,
                            ),
                            child: InkWell(
                              onTap: _playPause,
                              child: Icon(
                                (isPlaying && isCurrentSong)
                                    ? Icons.pause
                                    : Icons.play_arrow,
                                color: Colors.white,
                                size: 40,
                              ),
                            ),
                          );
                        },
                      ),

                      // 10 seconds forward
                      IconButton(
                        onPressed: _seekForward,
                        icon: Icon(
                          Icons.forward_10,
                          color: Colors.black,
                          size: 32,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
