import 'package:clean_arc/core/presentation/extintions/widget_extensions.dart';
import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/meditation_feature/controller/meditations_cubit.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/meditations_model.dart';
import 'package:clean_arc/features/meditation_feature/presentation/shimmer_componant/medidation_shimmer_component.dart';
import 'package:clean_arc/features/meditation_feature/presentation/view/meditation_detail_view.dart';
import 'package:clean_arc/features/user_layout/lip_feature/controller/lib_controller.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/termination_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MeditationsComponent extends StatefulWidget {
  const MeditationsComponent({super.key});

  @override
  State<MeditationsComponent> createState() => _MeditationsComponentState();
}

class _MeditationsComponentState extends State<MeditationsComponent> {
  @override
  void initState() {
    context.read<MeditationsCubit>().fetchMeditations();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MeditationsCubit, MeditationsState>(
        builder: (context, state) {
      if (state.errorMeditations != null) {
        CustomErrorWidget(
          failure: state.errorMeditations,
          onPressed: () {
            context.read<MeditationsCubit>().fetchMeditations();
          },
        );
      } else if (state.isLoadingMeditations == true) {
        return MeditationsShimmerComponent();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              TextApp(
                text: 'الخيال الموجه: طريقك الى الإسترخاء',
                style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeLarge,
                  fontWeight: FontWeightHelper.bold,
                ),
              ),
              SizedBox(
                height: 5,
              ),
              TextApp(
                text: 'انطلق الى حيث الراحة والطمأنينة',
                style: context.textStyle.copyWith(
                    fontSize: AppDimensions.fontSizeDefault,
                    fontWeight: FontWeightHelper.bold,
                    color: context.color.grayColor),
              ),
            ],
          ),
          SizedBox(
            height: 10,
          ),
          Container(
            height: 300,
            child: ListView.separated(
              separatorBuilder: (context, index) => SizedBox(
                width: 5,
              ),
              scrollDirection: Axis.horizontal,
              itemCount: state.successMeditations?.data?.length ?? 0,
              itemBuilder: (context, index) {
                MeditationModel? meditation =
                    state.successMeditations?.data?[index];

                return InkWell(
                  onTap: () async {
                    // Use new meditation detail view for single song experience
                    if (meditation?.subCategories?.isNotEmpty ?? false) {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                MeditationDetailView(meditation: meditation!),
                          ));
                    }

                    // Navigator.push(
                    //     context,
                    //     MaterialPageRoute(
                    //       builder: (context) => ArtistProfile(
                    //         title: meditation!.name ?? '',
                    //         soung: [
                    //           SongModel(
                    //               album:
                    //                   'https://images.unsplash.com/photo-1546707012-c46675f12716?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwyNjQwNTF8MHwxfHNlYXJjaHwyNHx8Y29uY2VydHxlbnwwfHx8fDE2MzI5MTA0OTM&ixlib=rb-1.2.1&q=80&w=1080',
                    //               artist: 'ZakharValaha',
                    //               song: 'Cinematic Fairy Tale Story (Main)1',
                    //               url:
                    //                   'https://cdn.pixabay.com/download/audio/2021/09/25/audio_153f263349.mp3?filename=cinematic-fairy-tale-story-main-8697.mp3'),
                    //           SongModel(
                    //               album:
                    //                   'https://images.unsplash.com/photo-1546707012-c46675f12716?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwyNjQwNTF8MHwxfHNlYXJjaHwyNHx8Y29uY2VydHxlbnwwfHx8fDE2MzI5MTA0OTM&ixlib=rb-1.2.1&q=80&w=1080',
                    //               artist: 'ZakharValaha',
                    //               song: 'Cinematic Fairy Tale Story (Main)2',
                    //               url:
                    //                   'https://cdn.pixabay.com/download/audio/2021/09/25/audio_153f263349.mp3?filename=cinematic-fairy-tale-story-main-8697.mp3'),
                    //           SongModel(
                    //               album:
                    //                   'https://images.unsplash.com/photo-1546707012-c46675f12716?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwyNjQwNTF8MHwxfHNlYXJjaHwyNHx8Y29uY2VydHxlbnwwfHx8fDE2MzI5MTA0OTM&ixlib=rb-1.2.1&q=80&w=1080',
                    //               artist: 'ZakharValaha',
                    //               song: 'Cinematic Fairy Tale Story (Main)3',
                    //               url:
                    //                   'https://cdn.pixabay.com/download/audio/2021/09/25/audio_153f263349.mp3?filename=cinematic-fairy-tale-story-main-8697.mp3'),
                    //           SongModel(
                    //               album:
                    //                   'https://images.unsplash.com/photo-1546707012-c46675f12716?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwyNjQwNTF8MHwxfHNlYXJjaHwyNHx8Y29uY2VydHxlbnwwfHx8fDE2MzI5MTA0OTM&ixlib=rb-1.2.1&q=80&w=1080',
                    //               artist: 'ZakharValaha',
                    //               song: 'Cinematic Fairy Tale Story (Main)4',
                    //               url:
                    //                   'https://cdn.pixabay.com/download/audio/2021/09/25/audio_153f263349.mp3?filename=cinematic-fairy-tale-story-main-8697.mp3'),
                    //         ],
                    //         // allsongs[1].artist??'',
                    //       ),
                    //     ));
                  },
                  child: Stack(
                    children: [
                      Column(
                        children: [
                          Center(
                              child: Container(
                                  height: 280,
                                  width: 250,
                                  child: CustomCachedNetworkImage(
                                    imageUrl: meditation?.image ?? '',
                                    fit: BoxFit.fill,
                                  )).cornerRadiusWithClipRRect(10)),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Center(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // Padding(
                                //   padding: const EdgeInsets.all(8.0),
                                //   child: TextApp(
                                //     text: meditation?.title ?? '',
                                //     style: context.textStyle.copyWith(
                                //       fontSize: AppDimensions.fontSizeDefault,
                                //       // color: context.color.whiteColor,
                                //       fontWeight: FontWeightHelper.bold,
                                //     ),
                                //     textAlign: TextAlign.center,
                                //   ),
                                // ),
                              ],
                            )),
                          ),
                          // Center(
                          //   child: Container(
                          //     decoration: BoxDecoration(
                          //         borderRadius: BorderRadius.circular(10),
                          //         color: context.color.textColor?.withOpacity(.5)),
                          //     height: 300,
                          //     width: 250,
                          //     child: Padding(
                          //       padding: const EdgeInsets.all(8.0),
                          //       child: Center(
                          //           child: Column(
                          //         crossAxisAlignment: CrossAxisAlignment.start,
                          //         mainAxisAlignment: MainAxisAlignment.end,
                          //         children: [
                          //           Padding(
                          //             padding: const EdgeInsets.all(8.0),
                          //             child: TextApp(
                          //               text: meditation.name ?? '',
                          //               style: context.textStyle.copyWith(
                          //                 fontSize: AppDimensions.fontSizeDefault,
                          //                 color: context.color.whiteColor,
                          //                 fontWeight: FontWeightHelper.bold,
                          //               ),
                          //               textAlign: TextAlign.center,
                          //             ),
                          //           ),
                          //         ],
                          //       )),
                          //     ),
                          //   ),
                          // )
                        ],
                      ),
                      InkWell(
                        onTap: () {
                          BookManage.toggleMeditation(
                              MeditationCashModel.fromJson(
                                  meditation?.toJson() ?? {}));
                        },
                        child: ValueListenableBuilder<Set<int>>(
                          valueListenable: BookManage.savedMeditations,
                          builder: (context, savedBooks, child) {
                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(
                                savedBooks.contains(meditation?.id)
                                    ? Icons.bookmark
                                    : Icons.bookmark_border,
                                color: context.color.yellowColor,
                              ),
                            );
                          },
                        ),
                      )
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }
}
