import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';

class ProfileItem extends StatelessWidget {
  const ProfileItem({
    required this.title,
    this.titleLeftWidget = '',
    required this.icon,
    this.onTap,
    this.showDivider = true,
    super.key,
  });

  final String title;

  final void Function()? onTap;
  final Widget icon;
  final bool showDivider;
  final String titleLeftWidget;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
            child: Row(
              children: [
                // icon,
                // SizedBox(
                //   width: 16,
                // ),
                TextApp(
                  text: title,
                  style: context.textStyle.copyWith(
                      fontSize: AppDimensions.fontSizeLarge,
                      color: context.color.hintColor,
                      fontWeight: FontWeightHelper.semiBold),
                ),
                Spacer(),

                TextApp(
                  text: titleLeftWidget,
                  textAlign: TextAlign.start,
                  maxLines: 1,
                  textOverflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: context.color.hintColor,
                      fontSize: AppDimensions.fontSizeDefault,
                      fontWeight: FontWeightHelper.semiBold),
                ),
                const SizedBox(
                  width: 4,
                ),

                Icon(
                  Icons.arrow_forward_ios,
                  size: 18,
                )
              ],
            ),
          ),
          // SizedBox(
          //   height: 5,
          // ),
          if (showDivider)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 0),
              child: Container(
                width: context.width,
                height: 1,
                color: context.color.borderColor,
              ),
            )
        ],
      ),
    );
  }
}
