import 'package:clean_arc/core/app/app_cubit/app_cubit.dart';
import 'package:clean_arc/core/data/services/shared_prefs/shared_pref.dart';
import 'package:clean_arc/core/data/services/shared_prefs/shared_prefs_key.dart';
import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/presentation/widget/dark_and_lang_button.dart';
import 'package:clean_arc/core/presentation/widget/rotate_image.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:clean_arc/features/layout_feature/cubit_cubit/layout_cubit.dart';
import 'package:clean_arc/features/user_profile_feature/item/profile_item.dart';
import 'package:clean_arc/features/user_profile_feature/view/componant/lang_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class UserProfileView extends StatelessWidget {
  static const String path = '/UserProfileView';

  const UserProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(context, title: context.translate.profile),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: context.color.whiteColor,
                    borderRadius: BorderRadius.circular(10)),
                child: Column(
                  children: [
                    Row(
                      children: [
                        AppImages.images.core.userImage
                            .image(width: 60, height: 60),
                        SizedBox(
                          width: 10,
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextApp(
                              text: 'Ahmed Prop',
                              style: context.textStyle.copyWith(
                                  height: 1,
                                  color: context.color.hintColorLight,
                                  fontWeight: FontWeightHelper.bold,
                                  fontSize: AppDimensions.fontSizeExtraLarge),
                            ),
                            TextApp(
                              text: 'Normal User',
                              style: context.textStyle.copyWith(
                                  height: 1,
                                  color: context.color.hintColorLight,
                                  fontWeight: FontWeightHelper.bold,
                                  fontSize: AppDimensions.fontSizeDefault),
                            ),
                          ],
                        )
                      ],
                    ),
                    ProfileItem(
                        title: context.translate.updateProfile,
                        icon: AppImages.images.core.profileIcon.svg(
                            color: context.color.primaryColor,
                            height: 20,
                            width: 20,
                            fit: BoxFit.cover),
                        onTap: () {
                          context.router.push(UpdateProfileViewRoute());
                        }),
                    ProfileItem(
                      // titleLeftWidget: context.translate.curLang,
                      title: context.translate.changeLanguage,
                      icon: AppImages.images.core.languageSquare.svg(
                          color: context.color.primaryColor,
                          height: 20,
                          width: 20,
                          fit: BoxFit.cover),
                      onTap: () {
                        homeBottomSheet(
                          context: context,
                          child: SizedBox(
                            height: context.height * 0.3,
                            child: const LangWidget(),
                          ),
                        );
                      },
                      showDivider: false,
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 16,
              ),
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: context.color.whiteColor,
                    borderRadius: BorderRadius.circular(10)),
                child: Column(
                  children: [
                    ProfileItem(
                        title: context.translate.myCreditDebitCards,
                        icon: AppImages.images.core.profileIcon.svg(
                            color: context.color.primaryColor,
                            height: 20,
                            width: 20,
                            fit: BoxFit.cover),
                        onTap: () {
                          context.router.push(CardsViewRoute());
                        }),
                    ProfileItem(
                      title: context.translate.paymentHistory,
                      icon: AppImages.images.core.profileIcon.svg(
                          color: context.color.primaryColor,
                          height: 20,
                          width: 20,
                          fit: BoxFit.cover),
                      onTap: () {
                        context.router.push(UpdateProfileViewRoute());
                      },
                      showDivider: false,
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 16,
              ),
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: context.color.whiteColor,
                    borderRadius: BorderRadius.circular(10)),
                child: Column(
                  children: [
                    ProfileItem(
                        title: context.translate.supportPage,
                        icon: AppImages.images.core.profileIcon.svg(
                            color: context.color.primaryColor,
                            height: 20,
                            width: 20,
                            fit: BoxFit.cover),
                        onTap: () {
                          context.router.push(SupportViewRoute());
                        }),
                    ProfileItem(
                      title: context.translate.shareApplication,
                      icon: AppImages.images.core.profileIcon.svg(
                          color: context.color.primaryColor,
                          height: 20,
                          width: 20,
                          fit: BoxFit.cover),
                      onTap: () {
                        context.router.push(UpdateProfileViewRoute());
                      },
                      showDivider: false,
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 16,
              ),
              InkWell(
                  onTap: () async {
                    await SharedPref().removePreference(PrefKeys.currentUser);
                    currentUser?.value.auth = false;
                    context.read<LayoutCubit>().changePageIndex(0);
                    context.router.replaceAll([LoginViewRoute()]);
                  },
                  child: Padding(
                    padding: EdgeInsets.all(10),
                    child: TextApp(
                      text: context.translate.logOutCurrentAccount,
                      textAlign: TextAlign.start,
                      maxLines: 1,
                      textOverflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: context.color.redColor,
                          fontSize: AppDimensions.fontSizeDefault,
                          fontWeight: FontWeightHelper.semiBold),
                    ),
                  ))
            ],
          ),
        ),
      ),
    );
  }
}

Future<dynamic> homeBottomSheet({
  required BuildContext context,
  required Widget child,
}) {
  final w = MediaQuery.of(context).size.width;
  return showModalBottomSheet(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topRight: Radius.circular(w * 0.06),
        topLeft: Radius.circular(w * 0.06),
      ),
    ),
    isDismissible: true,
    isScrollControlled: true,
    context: context,
    builder: (context) =>
        Padding(padding: MediaQuery.of(context).viewInsets, child: child),
  );
}
