import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:clean_arc/injection/injection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class UpdateProfileView extends StatelessWidget {
  const UpdateProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(context,
            title: context.translate.changeProfile, showNotification: false),
        body: Padding(
          padding: const EdgeInsets.only(
              top: AppDimensions.paddingSizeDefault,
              bottom: AppDimensions.paddingSizeDefault,
              right: AppDimensions.paddingSizeDefault,
              left: AppDimensions.paddingSizeDefault),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          CustomTextField(
                            controller: TextEditingController(
                                text: currentUser?.value?.user?.name),
                            inputType: TextInputType.name,
                            hintText: context.translate.userName,
                            prefixIcon: Padding(
                              padding: const EdgeInsets.only(right: 12.0),
                              child: AppImages.images.core.profileIcon
                                  .svg(color: Color(0xff8F8FA6)),
                            ),
                          ),
                          SizedBox(
                            height: 24,
                          ),
                          Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    TextApp(
                                      text: context.translate.phoneNumber,
                                      style: TextStyle(
                                          fontWeight: FontWeightHelper.semiBold,
                                          fontSize:
                                              AppDimensions.fontSizeDefault,
                                          color: Color(0xff696985)),
                                    ),
                                    SizedBox(
                                      height: 8,
                                    ),
                                    CustomTextField(
                                      controller: TextEditingController(
                                          text: currentUser?.value.user?.phone.toString()),
                                      isEnabled: false,
                                      inputType: TextInputType.phone,
                                      hintText: context.translate.phoneNumber,
                                      prefixIcon: Padding(
                                        padding:
                                            const EdgeInsets.only(right: 12.0),
                                        child: AppImages.images.core.phoneCall
                                            .svg(color: Color(0xff8F8FA6)),
                                      ),
                                    ),
                                  ],
                                ),
                          CustomTextField(
                            controller: TextEditingController(
                                text: currentUser?.value?.user?.email),
                            isEnabled: false,
                            hintText: context.translate.email,
                            inputType: TextInputType.emailAddress,
                            prefixIcon: Padding(
                              padding: const EdgeInsets.only(right: 12.0),
                              child: AppImages.images.core.email
                                  .svg(color: Color(0xff8F8FA6)),
                            ),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              CustomButton(
                  width: context.width,
                  onPressed: () {},
                  child: TextApp(
                    text: context.translate.changeProfile,
                    style: context.textStyleButton,
                  ))
            ],
          ),
        ));
  }
}
