import 'package:clean_arc/core/app/app_cubit/app_cubit.dart';
import 'package:clean_arc/core/presentation/extintions/context_extintions.dart';
import 'package:clean_arc/core/presentation/widget/custom_button_button.dart';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LangWidget extends StatefulWidget {
  const LangWidget({super.key});

  @override
  State<LangWidget> createState() => _LangWidgetState();
}

class _LangWidgetState extends State<LangWidget> {
  @override
  void initState() {
    // TODO: implement initState

    Future.delayed(Duration.zero, () {
      currentLanguage = context.translate.localeName;
      setState(() {});
    });
    super.initState();
  }

  String currentLanguage = 'ar';

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder:
          (BuildContext context, void Function(void Function()) setState2) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: context.width * 0.04),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(context.width * 0.06),
              topLeft: Radius.circular(context.width * 0.06),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Column(
                children: [
                  SizedBox(
                    height: 16,
                  ),
                  InkWell(
                    onTap: () {
                      setState2(() {
                        currentLanguage = 'ar';
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        children: [
                          TextApp(
                            text: 'العربيه',
                            style: context.textStyle.copyWith(
                              fontSize: 16,
                              color: (currentLanguage == 'ar')
                                  ? context.color.primaryColor
                                  : context.color.textColor,
                              fontWeight: (currentLanguage == 'ar')
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Divider(
                    color: Colors.grey.withOpacity(0.7),
                    height: 1,
                    thickness: 1,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  InkWell(
                    onTap: () {
                      setState2(() {
                        currentLanguage = 'en';
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        children: [
                          TextApp(
                            text: 'English',
                            style: context.textStyle.copyWith(
                              fontSize: 16,
                              color: (currentLanguage == 'en')
                                  ? context.color.primaryColor
                                  : context.color.textColor,
                              fontWeight: (currentLanguage == 'en')
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 20,
              ),
              CustomButton(
                width: double.infinity,
                child: TextApp(
                  text: context.translate.saveChanges,
                  style: context.textStyleButton,
                ),
                // : Colors.white,
                borderColor: context.color.primaryColor,
                // fontSize: 18,
                onPressed: () async {
                  context
                      .read<AppCubit>()
                      .changeLang2(currentLanguage)
                      .then((value) => Navigator.pop(context));
                },
                // color: context.color.primaryColor,
                // borderRadius: context.width * 0.04,
              ),
            ],
          ),
        );
      },
    );
  }
}
