class ConcernModel {
  ConcernModel({
    required this.id,
    required this.name,
    required this.image,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.thoughts,
  });

  final int? id;
  final String? name;
  final String? image;
  final int? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<Thought> thoughts;

  factory ConcernModel.fromJson(Map<String, dynamic> json) {
    return ConcernModel(
      id: json["id"],
      name: json["name"],
      image: json["image"],
      isActive: json["is_active"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      thoughts: json["thoughts"] == null
          ? []
          : List<Thought>.from(
              json["thoughts"]!.map((x) => Thought.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "thoughts": thoughts.map((x) => x?.toJson()).toList(),
      };
}

class Thought {
  Thought({
    required this.id,
    required this.concernId,
    required this.content,
    required this.audioUrl,
    required this.duration,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.questions,
  });

  final int? id;
  final int? concernId;
  final String? content;
  final String? audioUrl;
  final String? duration;
  final int? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<QuestionsModel> questions;

  factory Thought.fromJson(Map<String, dynamic> json) {
    return Thought(
      id: json["id"],
      concernId: json["concern_id"],
      content: json["content"],
      audioUrl: json["audio_url"],
      duration: json["duration"],
      isActive: json["is_active"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      questions: json["questions"] == null
          ? []
          : List<QuestionsModel>.from(
              json["questions"]!.map((x) => QuestionsModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "concern_id": concernId,
        "content": content,
        "audio_url": audioUrl,
        "duration": duration,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "questions": questions.map((x) => x?.toJson()).toList(),
      };
}

class QuestionsModel {
  QuestionsModel({
    required this.id,
    required this.thoughtId,
    required this.questionText,
    required this.button_yes_text,
    required this.button_no_text,
    required this.isPositiveRoute,
    required this.isShow,
    required this.yesRouteId,
    required this.noRouteId,
    required this.summary,
    required this.createdAt,
    required this.updatedAt,
    required this.questionType,
    required this.answers,
  });

  final int? id;
  final int? thoughtId;
  final String? questionText;
  final String? button_yes_text;
  final String? button_no_text;
  final int? isPositiveRoute;
  final int? isShow;
  final int? yesRouteId;
  final int? noRouteId;
  final String? summary;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? questionType;
  final List<dynamic> answers;

  factory QuestionsModel.fromJson(Map<String, dynamic> json) {
    return QuestionsModel(
      id: json["id"],
      thoughtId: json["thought_id"],
      questionText: json["question_text"],
      button_yes_text: json["button_yes_text"],
      button_no_text: json["button_no_text"],
      isPositiveRoute: json["is_positive_route"],
      isShow: json["is_show"],
      yesRouteId: json["yes_route_id"],
      noRouteId: json["no_route_id"],
      summary: json["summary"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      questionType: json["question_type"],
      answers: json["answers"] == null
          ? []
          : List<dynamic>.from(json["answers"]!.map((x) => x)),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "thought_id": thoughtId,
        "question_text": questionText,
        "button_yes_text": button_yes_text,
        "button_no_text": button_no_text,
        "is_positive_route": isPositiveRoute,
        "is_show": isShow,
        "yes_route_id": yesRouteId,
        "no_route_id": noRouteId,
        "summary": summary,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "question_type": questionType,
        "answers": answers.map((x) => x).toList(),
      };
}
