import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'concerns_services.g.dart';

@LazySingleton()
@RestApi(baseUrl: '')
abstract class ConcernsServices {
  @factoryMethod
  factory ConcernsServices(Dio dio, Configuration configuration) {
    return _ConcernsServices(dio, baseUrl: configuration.getApiUrl);
  }

// Services Method to fetch Concerns
  @GET('concerns')
  Future<List<ConcernModel>> fetchConcerns();

  @GET('concerns/{id}')
  Future<ConcernModel> getConcernsById({
    @Path('id') required int id,
  });

  // Services Method
  @GET('questions/thought/{id}')
  Future<List<QuestionsModel>> getQuestionsByThoughtId({
    @Path('id') required int id,
  });

  // Comparison history API methods
  @POST('comparison-history/{id}')
  Future<void> createComparisonHistory({
    @Path('id') required int id,
    @Body() required Map<String, dynamic> body,
  });

  @PUT('comparison-history/{id}/after-score-audio')
  Future<void> updateAfterScoreAudio({
    @Path('id') required int id,
    @Body() required Map<String, dynamic> body,
  });

  @PUT('comparison-history/{id}/after-score')
  Future<void> updateAfterScore({
    @Path('id') required int id,
    @Body() required Map<String, dynamic> body,
  });
}
