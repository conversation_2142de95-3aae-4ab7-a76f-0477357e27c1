import 'package:clean_arc/core/data/repositories/base_repository_impl.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/domain/services/remote/concerns_services.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

abstract class ConcernsRepository {
// Repository method for Concerns
  Future<Either<Failure, List<ConcernModel>>> fetchConcerns();

  Future<Either<Failure, ConcernModel>> getConcernsById({
    required int id,
  });

  // Repository
  Future<Either<Failure, List<QuestionsModel>>> getQuestionsByThoughtId({
    required int id,
  });

  // Comparison history repository methods
  Future<Either<Failure, void>> createComparisonHistory({
    required int id,
    required int score,
  });

  Future<Either<Failure, void>> updateAfterScoreAudio({
    required int id,
    required int afterScoreAudio,
  });

  Future<Either<Failure, void>> updateAfterScore({
    required int id,
    required int afterScore,
  });
}

@LazySingleton(as: ConcernsRepository)
class ConcernsRepositoryImpl extends BaseRepositoryImpl
    implements ConcernsRepository {
  final ConcernsServices _services;

  ConcernsRepositoryImpl(super.logger, this._services);

  // Repository Implementation for Concerns
  Future<Either<Failure, List<ConcernModel>>> fetchConcerns() {
    return request(() async {
      final result = await _services.fetchConcerns();
      return Right(result);
    });
  }

  Future<Either<Failure, ConcernModel>> getConcernsById({
    required int id,
  }) {
    return request(() async {
      final result = await _services.getConcernsById(id: id);
      return Right(result);
    });
  }

  // Repository Implementation
  Future<Either<Failure, List<QuestionsModel>>> getQuestionsByThoughtId({
    required int id,
  }) {
    return request(() async {
      final result = await _services.getQuestionsByThoughtId(id: id);
      return Right(result);
    });
  }

  // Comparison history repository implementations
  Future<Either<Failure, void>> createComparisonHistory({
    required int id,
    required int score,
  }) {
    return request(() async {
      final result = await _services.createComparisonHistory(
        id: id,
        body: {'before_score': score},
      );
      return Right(result);
    });
  }

  Future<Either<Failure, void>> updateAfterScoreAudio({
    required int id,
    required int afterScoreAudio,
  }) {
    return request(() async {
      final result = await _services.updateAfterScoreAudio(
        id: id,
        body: {'after_score_audio': afterScoreAudio},
      );
      return Right(result);
    });
  }

  Future<Either<Failure, void>> updateAfterScore({
    required int id,
    required int afterScore,
  }) {
    return request(() async {
      final result = await _services.updateAfterScore(
        id: id,
        body: {'after_score': afterScore},
      );
      return Right(result);
    });
  }
}
