import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';

class ConcernsItem extends StatelessWidget {
  String title;
  bool isSelected;

  ConcernsItem({super.key, required this.title, required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: context.color.whiteColor,
          boxShadow: [
            BoxShadow(
              color: context.color.grayColor!.withOpacity(.1),
              spreadRadius: 1,
              blurRadius: 1,
              offset: Offset(0, 1), // changes position of shadow
            ),
          ]),
      child: Row(
        children: [
          // AppImages.images.salimIcon.male.svg(),
          // SizedBox(
          //   width: 16,
          // ),
          Expanded(
            child: TextApp(
              text: title,
              style: context.textStyle
                  .copyWith(fontWeight: FontWeightHelper.medium),
            ),
          ),
          isSelected == false
              ? Icon(
                  Icons.radio_button_unchecked,
                  color: context.color.primaryColor,
                )
              : Icon(
                  Icons.radio_button_checked,
                  color: context.color.primaryColor,
                )
        ],
      ),
    );
  }
}

class ConcernsItem2 extends StatelessWidget {
  String title;
  String imagePath;
  bool isSelected;

  ConcernsItem2(
      {super.key,
      required this.title,
      required this.imagePath,
      required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: context.color.whiteColor,
          border: Border.all(
              color: isSelected == true
                  ? context.color.primaryColor!
                  : Colors.transparent),
          boxShadow: [
            BoxShadow(
              color: context.color.grayColor!.withOpacity(.1),
              spreadRadius: 1,
              blurRadius: 1,
              offset: Offset(0, 1), // changes position of shadow
            ),
          ]),
      child: Row(
        children: [
          // AppImages.images.salimIcon.male.svg(),
          // SizedBox(
          //   width: 16,
          // ),

          isSelected == false
              ? Icon(
                  Icons.radio_button_unchecked,
                  color: context.color.primaryColor,
                )
              : Icon(
                  Icons.radio_button_checked,
                  color: context.color.primaryColor,
                ),
          SizedBox(width: 10,),
          Expanded(
            child: TextApp(
              text: title,
              style: context.textStyle
                  .copyWith(fontWeight: FontWeightHelper.medium),
            ),
          ),
          Image.asset(
            imagePath,
            height: 74,
          )
        ],
      ),
    );
  }
}
