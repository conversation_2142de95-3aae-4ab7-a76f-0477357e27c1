import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';

@RoutePage()
class CategoryListView extends StatefulWidget {
  const CategoryListView({super.key});

  @override
  State<CategoryListView> createState() => _CategoryListViewState();
}

class _CategoryListViewState extends State<CategoryListView> {
  @override
  Widget build(BuildContext context) {
    List images = [
      AppImages.images.demo.c1.path,
      AppImages.images.demo.c2.path,
      AppImages.images.demo.c3.path,
      AppImages.images.demo.c4.path,
      AppImages.images.demo.c5.path,
      AppImages.images.demo.c6.path,
    ];
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          Stack(
            children: [
              Container(
                width: context.width,
                height: 5,
                color: context.color.primaryColor!.withOpacity(.3),
              ),
              LinearPercentIndicator(
                padding: EdgeInsets.zero,
                alignment: MainAxisAlignment.start,
                backgroundColor: context.color.primaryColor!.withOpacity(0.2),
                width: 100,
                animation: false,
                lineHeight: 5.0,
                animationDuration: 2500,
                percent: 1,
                linearStrokeCap: LinearStrokeCap.roundAll,
                progressColor: context.color.primaryColor,
              ),
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Column(
              children: [
                Row(
                  children: [
                    TextApp(
                      text: context.translate.chooseConcernsToEliminate,
                      style: context.textStyle.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    TextApp(
                      text: context.translate.chooseTheNumberYouWant,
                      style: context.textStyle.copyWith(
                          fontWeight: FontWeight.normal,
                          color: context.color.descriptionColor),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(
            height: 16,
          ),
          Expanded(
              child: GridView.builder(
            padding: EdgeInsets.symmetric(horizontal: 10),
            itemCount: images.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2, mainAxisSpacing: 15, crossAxisSpacing: 15),
            itemBuilder: (context, index) {
              return Container(
                child: Image.asset(images[index]),
              );
            },
          )),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: CustomButton(
                width: double.infinity,
                onPressed: () {
                  context.pushRoute(SubCategoryListViewRoute(id: 0));
                },
                child: TextApp(
                  text: context.translate.next,
                  style: context.textStyleButton,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
