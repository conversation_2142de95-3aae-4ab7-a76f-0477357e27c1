import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/SubCategotyModel.dart';
import 'package:clean_arc/features/concerns_feature/presentation/items/category_model.dart';
import 'package:clean_arc/features/meditation_feature/controller/meditations_cubit.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/domain/services/songs_data.dart';
import 'package:clean_arc/features/player_feature/persentation/view/singer_profile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class SolveProblemMethodView extends StatefulWidget {
  final int thoughtsId;

  const SolveProblemMethodView({super.key, required this.thoughtsId});

  @override
  State<SolveProblemMethodView> createState() => _SolveProblemMethodViewState();
}

class _SolveProblemMethodViewState extends State<SolveProblemMethodView> {
  List<SubCategoryModel> subCategoryList = [
    SubCategoryModel(name: 'أخاف أن أصاب بنوبة قلبية', id: 1, isSelected: true),
    SubCategoryModel(
        name: 'أخاف ان اتوقف عن التنفس واصاب بالاختناق',
        id: 2,
        isSelected: true),
    SubCategoryModel(
        name: 'أخاف ان افقد السيطرة على نفسي وعلى أفعالي',
        id: 3,
        isSelected: true),
    SubCategoryModel(name: 'أخاف أن أموت وتخرج روحي', id: 4, isSelected: false),
    SubCategoryModel(
        name: 'أخاف من عدم السيطرة على نوبة الهلع', id: 5, isSelected: false),
    SubCategoryModel(
        name: 'أخاف من إستمرار نوبة الهلع الى الأبد', id: 6, isSelected: true),
    SubCategoryModel(
        name: 'أخاف من إستمرار نوبة الهلع الى الأبد', id: 7, isSelected: false),
    SubCategoryModel(
        name: 'أخاف من الإصابة بجلطة دماغية', id: 8, isSelected: true),
    SubCategoryModel(
        name: 'أخاف من إرتفاع ضغط الدم عندي', id: 9, isSelected: false),
    SubCategoryModel(name: 'أخاف أن أفقد بصري', id: 10, isSelected: false),
  ];

  int selected = 0;

  @override
  Widget build(BuildContext context) {
    MeditationsCubit cubit = context.watch<MeditationsCubit>();
    final List<SongModel> allsongs =
        songs.map((e) => SongModel.fromJson(e)).toList();
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          // Stack(
          //   children: [
          //     Container(
          //       width: context.width,
          //       height: 5,
          //       color: context.color.primaryColor!.withOpacity(.3),
          //     ),
          //     LinearPercentIndicator(
          //       padding: EdgeInsets.zero,
          //       alignment: MainAxisAlignment.start,
          //       backgroundColor: context.color.primaryColor!.withOpacity(0.2),
          //       width: 250,
          //       animation: false,
          //       lineHeight: 5.0,
          //       animationDuration: 2500,
          //       percent: 1,
          //       linearStrokeCap: LinearStrokeCap.roundAll,
          //       progressColor: context.color.primaryColor,
          //     ),
          //   ],
          // ),
          SizedBox(
            height: 16,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    TextApp(
                      text: context.translate.howDoYouWantToEliminateYourFears,
                      style: context.textStyle
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(
            height: 16,
          ),
          Expanded(
              child: Column(
            children: [
              // IntroVideo(),
              InkWell(
                onTap: () {
                  setState(() {
                    selected = 0;
                  });
                },
                child: ConcernsItem2(
                  title: context.translate.byVoiceRecorder,
                  isSelected: selected == 0 ? true : false,
                  imagePath: AppImages.images.core.micc.path,
                ),
              ),
              InkWell(
                onTap: () {
                  setState(() {
                    selected = 1;
                  });
                },
                child: ConcernsItem2(
                  title: context.translate.destroyBadThinking,
                  isSelected: selected == 1 ? true : false,
                  imagePath: AppImages.images.core.vide.path,
                ),
              ),
            ],
          )),

          // Expanded(child: ArtistProfile(
          //         name: allsongs[0].artist!,
          //         controller: context.read<NowPlayingController>(),
          //       ),),

          // HorizontalListWidget(
          //     controller: context.read<NowPlayingController>(),
          //     allsongs: allsongs),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: CustomButton(
                isLoading: cubit.state.isLoadingGetsubMeditation,
                width: double.infinity,
                onPressed: () async {
                  if (selected == 0) {
                    await cubit.getsubMeditation(
                        id: widget.thoughtsId.toString());

                    if (cubit.state.successGetsubMeditation != null) {
                      if (cubit.state.successGetsubMeditation!.data
                              ?.isNotEmpty ??
                          false) {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ArtistProfile(
                                  title: cubit.state.successGetsubMeditation!
                                          .data![0].subTitle ??
                                      '',
                                  soung:
                                      cubit.state.successGetsubMeditation!.data!
                                          .map(
                                            (e) => SongModel(
                                                album: '',
                                                artist: cubit
                                                        .state
                                                        .successGetsubMeditation!
                                                        .data![0]
                                                        .subTitle ??
                                                    '',
                                                song: e.subTitle,
                                                url: getBaseUrl +
                                                    (e?.audioUrls ?? '')),
                                          )
                                          .toList()),
                            ));
                      }

                      context.pushRoute(VideoRecordedViewRoute(
                          thoughtsId: widget.thoughtsId));
                    }
                  }
                  if (selected == 1) {
                    context.pushRoute(
                        QuestionsViewRoute(thoughtId: widget.thoughtsId));
                    // context.pushRoute(
                    //     VideoRecordedViewRoute(thoughtsId: widget.thoughtsId));
                  }
                },
                child: TextApp(
                  text: context.translate.next,
                  style: context.textStyleButton,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
