import 'package:clean_arc/core/presentation/widget/custom_loading.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/presentation/widget/screen_utils.dart';
import 'package:clean_arc/core/presentation/widget/emoji_scale_dialog.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/presentation/items/category_model.dart';
import 'package:clean_arc/my_global_cubit/thought_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class SubCategoryListView extends StatefulWidget {
  final int id;

  const SubCategoryListView({super.key, required this.id});

  @override
  State<SubCategoryListView> createState() => _SubCategoryListViewState();
}

class _SubCategoryListViewState extends State<SubCategoryListView>
    with ScreenUtils {
  @override
  void initState() {
    context.read<ConcernsCubit>().getConcernsById(id: widget.id.toInt());
    super.initState();
  }

  int? selectedId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          // Stack(
          //   children: [
          //     Container(
          //       width: context.width,
          //       height: 5,
          //       color: context.color.primaryColor!.withOpacity(.3),
          //     ),
          //     LinearPercentIndicator(
          //       padding: EdgeInsets.zero,
          //       alignment: MainAxisAlignment.start,
          //       backgroundColor: context.color.primaryColor!.withOpacity(0.2),
          //       width: 250,
          //       animation: false,
          //       lineHeight: 5.0,
          //       animationDuration: 2500,
          //       percent: 1,
          //       linearStrokeCap: LinearStrokeCap.roundAll,
          //       progressColor: context.color.primaryColor,
          //     ),
          //   ],
          // ),
          SizedBox(
            height: 16,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    TextApp(
                      text: context.translate
                          .whatThoughtsDoYouExperienceDuringPanicAttack,
                      style: context.textStyle
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(
            height: 16,
          ),
          BlocBuilder<ConcernsCubit, ConcernsState>(
            builder: (context, state) {
              if (state.errorGetConcernById != null) {
                return CustomErrorWidget(
                  failure: state.errorGetConcernById,
                  onPressed: () {
                    context
                        .read<ConcernsCubit>()
                        .getConcernsById(id: widget.id);
                  },
                );
              } else if (state.isLoadingGetConcernById == true) {
                return Expanded(child: Center(child: CustomLoading()));
              }
              return Expanded(
                child: ListView.builder(
                  itemCount: state.successGetConcernById?.thoughts.length ?? 0,
                  itemBuilder: (context, index) {
                    List<Thought?>? thoughts =
                        state.successGetConcernById?.thoughts;
                    return InkWell(
                      onTap: () {
                        selectedId = thoughts?[index]?.id;
                        // Set the selected thought globally
                        if (thoughts?[index] != null) {
                          context
                              .read<ThoughtCubit>()
                              .setSelectedThought(thoughts![index]!);
                        }
                        setState(() {});
                      },
                      child: ConcernsItem(
                        title: thoughts?[index]?.content ?? '',
                        isSelected: thoughts?[index]?.id == selectedId,
                      ),
                    );
                  },
                ),
              );
            },
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: CustomButton(
                width: double.infinity,
                onPressed: selectedId == null
                    ? null
                    : () {
                        // Show emoji scale dialog first
                        showEmojiScaleDialog(
                          context: context,
                          title: 'تقييم مستوى القلق',
                          description: 'كيف تشعر الآن بخصوص هذا الخوف؟',
                          onSubmit: (int score) async {
                            // Make POST API call
                            await context
                                .read<ConcernsCubit>()
                                .createComparisonHistory(
                                  id: selectedId!,
                                  score: score,
                                );

                            // Navigate to next screen
                            context.pushRoute(SolveProblemMethodViewRoute(
                                thoughtsId: selectedId!));
                          },
                        );
                      },
                child: TextApp(
                  text: context.translate.next,
                  style: context.textStyleButton,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
