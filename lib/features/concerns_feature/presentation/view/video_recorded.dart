import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/presentation/componant/intro_video.dart';
import 'package:clean_arc/features/concerns_feature/presentation/view/video_meditation_detail_view.dart';
import 'package:clean_arc/my_global_cubit/thought_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class VideoRecordedView extends StatefulWidget {
  final int thoughtsId;

  const VideoRecordedView({super.key, required this.thoughtsId});

  @override
  State<VideoRecordedView> createState() => _VideoRecordedViewState();
}

class _VideoRecordedViewState extends State<VideoRecordedView> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThoughtCubit, Thought?>(
      builder: (context, selectedThought) {
        // Use the selected thought from ThoughtCubit, or create fallback if needed
        Thought? currentThought = selectedThought;

        // If no thought is selected globally, try to find it by ID (fallback)
        if (currentThought == null || currentThought.id != widget.thoughtsId) {
          // This is a fallback - in a real app you might want to fetch from API
          // For now, we'll show an error or loading state
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              backgroundColor: Colors.white,
              elevation: 0,
              leading: IconButton(
                icon: Icon(Icons.arrow_back, color: Colors.black),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  TextApp(
                    text: "لم يتم العثور على الفكر المحدد",
                    style: context.textStyle.copyWith(
                      fontSize: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 16),
                  CustomButton(
                    onPressed: () => Navigator.pop(context),
                    child: TextApp(
                      text: "العودة",
                      style: context.textStyleButton,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextApp(
                  text:
                      "شاهد هذا الفيديو المهم عن القلق ونوبات الهلع قبل الانتقال لطرق التخلص من الأفكار السلبية.",
                  style: context.textStyle.copyWith(
                      // color: context.color.whiteColor,
                      fontSize: 17,
                      fontWeight: FontWeightHelper.bold),
                  textAlign: TextAlign.start,
                ),
              ),
              Spacer(),

              // Top 3/4 section with video container
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(32),
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: AspectRatio(
                      aspectRatio: 16 / 9,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Stack(
                          children: [
                            // Video container with static repeating video
                            Container(
                              width: double.infinity,
                              height: double.infinity,
                              child: IntroVideo(
                                url:
                                    "https://arab-cbt.com/videos/1743247485745-zptm1nb.webm",
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              Spacer(),

              // Bottom 1/4 section with content and button
              Container(
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Continue button
                    Container(
                      width: double.infinity,
                      child: CustomButton(
                        onPressed: () {
                          // Navigate to meditation detail view with the selected thought data
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => VideoMeditationDetailView(
                                thought: currentThought,
                                parentImage: null,
                              ),
                            ),
                          );
                        },
                        child: TextApp(
                          text: context.translate.continue1,
                          style: context.textStyleButton,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
