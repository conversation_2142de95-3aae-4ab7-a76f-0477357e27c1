import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/presentation/widget/emoji_scale_dialog.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/player_feature/controller/now_playing_controller.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/domain/services/songs_data.dart'
    as songs_data;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player/video_player.dart';

class VideoMeditationDetailView extends StatefulWidget {
  final Thought thought;
  final String? parentImage;

  const VideoMeditationDetailView({
    Key? key,
    required this.thought,
    this.parentImage,
  }) : super(key: key);

  @override
  State<VideoMeditationDetailView> createState() =>
      _VideoMeditationDetailViewState();
}

class _VideoMeditationDetailViewState extends State<VideoMeditationDetailView> {
  late NowPlayingController controller;
  late VideoPlayerController videoPlayerController;

  @override
  void initState() {
    super.initState();
    controller = context.read<NowPlayingController>();
    videoPlayerController = VideoPlayerController.networkUrl(
      Uri.parse("https://arab-cbt.com/videos/1743229485745-zptm1nb.mp4"),
    )
      ..initialize().then((_) {
        videoPlayerController.play();
        setState(() {});
      })
      ..setLooping(true);
  }

  @override
  void dispose() {
    // Stop audio when leaving the page
    controller.player.stop();
    songs_data.currentSong = '';
    songs_data.currentPlayingUrl = '';
    super.dispose();
  }

  void _playPause() {
    if (widget.thought.audioUrl == null) return;

    final audioUrl = getBaseUrl + widget.thought.audioUrl!;

    if (controller.player.playing && songs_data.currentPlayingUrl == audioUrl) {
      // Just pause, don't stop - this preserves the position
      controller.player.pause();
    } else {
      // Check if this is the same song that was paused
      if (songs_data.currentPlayingUrl == audioUrl) {
        // Same song, just resume playing from current position
        controller.player.play();
      } else {
        // Different song, stop current and start new one from beginning
        controller.player.stop();
        songs_data.currentSong = widget.thought.content ?? '';
        songs_data.currentPlayingUrl = audioUrl;

        // Create playlist with single song
        controller.changetoSingerplaylist([
          SongModel(
            album: '',
            artist: 'Thought Audio',
            song: widget.thought.content ?? '',
            url: audioUrl,
          )
        ], 0);

        controller.player.seek(Duration.zero);
        controller.player.play();
      }
    }
    setState(() {});
  }

  void _seekForward() {
    final currentPosition = controller.player.position;
    final newPosition = currentPosition + Duration(seconds: 10);
    final duration = controller.player.duration ?? Duration.zero;

    if (newPosition < duration) {
      controller.player.seek(newPosition);
    } else {
      controller.player.seek(duration);
    }
  }

  void _seekBackward() {
    final currentPosition = controller.player.position;
    final newPosition = currentPosition - Duration(seconds: 10);

    if (newPosition > Duration.zero) {
      controller.player.seek(newPosition);
    } else {
      controller.player.seek(Duration.zero);
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          Spacer(),
          // Top 3/4 section with video
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(32),
            child: Center(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: VideoPlayer(
                      videoPlayerController,
                    ),
                  ),
                ),
              ),
            ),
          ),

          Spacer(flex: 3),

          // Bottom 1/4 section with controls
          Container(
            padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Thought content title
                Text(
                  widget.thought.content ?? '',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 16),

                // Progress bar (only show if audio is available)
                if (widget.thought.audioUrl != null)
                  StreamBuilder<Duration>(
                    stream: controller.player.positionStream,
                    builder: (context, snapshot) {
                      final position = snapshot.data ?? Duration.zero;
                      final duration =
                          controller.player.duration ?? Duration.zero;

                      return Column(
                        children: [
                          Slider(
                            value: duration.inMilliseconds > 0
                                ? position.inMilliseconds /
                                    duration.inMilliseconds
                                : 0.0,
                            onChanged: (value) {
                              final newPosition = Duration(
                                milliseconds:
                                    (value * duration.inMilliseconds).round(),
                              );
                              controller.player.seek(newPosition);
                            },
                            activeColor: Colors.black,
                            inactiveColor: Colors.grey[300],
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  _formatDuration(position),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                Text(
                                  _formatDuration(duration),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),

                SizedBox(height: 16),

                // Control buttons (only show if audio is available)
                if (widget.thought.audioUrl != null)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // 10 seconds backward
                      IconButton(
                        onPressed: _seekBackward,
                        icon: Icon(
                          Icons.replay_10,
                          color: Colors.black,
                          size: 32,
                        ),
                      ),

                      // Play/Pause button
                      StreamBuilder<bool>(
                        stream: controller.player.playingStream,
                        builder: (context, snapshot) {
                          final isPlaying = snapshot.data ?? false;
                          final isCurrentSong = songs_data.currentPlayingUrl ==
                              (getBaseUrl + (widget.thought.audioUrl ?? ''));

                          return Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.black,
                            ),
                            child: InkWell(
                              onTap: _playPause,
                              child: Icon(
                                (isPlaying && isCurrentSong)
                                    ? Icons.pause
                                    : Icons.play_arrow,
                                color: Colors.white,
                                size: 40,
                              ),
                            ),
                          );
                        },
                      ),

                      // 10 seconds forward
                      IconButton(
                        onPressed: _seekForward,
                        icon: Icon(
                          Icons.forward_10,
                          color: Colors.black,
                          size: 32,
                        ),
                      ),
                    ],
                  ),

                SizedBox(height: 24),
              ],
            ),
          ),
          Spacer(),

          // "انتهيت" button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            child: CustomButton(
              width: double.infinity,
              onPressed: () {
                showEmojiScaleDialog(
                  context: context,
                  title: 'تقييم مستوى القلق بعد التأمل',
                  description: 'كيف تشعر الآن بعد انتهاء جلسة التأمل؟',
                  onSubmit: (int score) async {
                    // Make PUT API call
                    await context.read<ConcernsCubit>().updateAfterScoreAudio(
                          id: widget.thought.id!,
                          afterScoreAudio: score,
                        );

                    // Navigate back
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  },
                );
              },
              child: Text(
                'انتهيت',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          Spacer(),
        ],
      ),
    );
  }
}
