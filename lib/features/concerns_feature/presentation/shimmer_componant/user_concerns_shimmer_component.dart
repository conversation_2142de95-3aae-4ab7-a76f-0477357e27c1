import 'package:cached_network_image/cached_network_image.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/presentation/widget/shimmer/shimar_widget.dart';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UserConcernsShimmerComponent extends StatefulWidget {
  const UserConcernsShimmerComponent({super.key});

  @override
  State<UserConcernsShimmerComponent> createState() =>
      _UserConcernsShimmerComponentState();
}

class _UserConcernsShimmerComponentState
    extends State<UserConcernsShimmerComponent> {
  @override
  void initState() {
    context.read<ConcernsCubit>().fetchConcerns();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            CustomShimmerWidget(
              child: TextApp(
                text: context.translate.chooseConcernsToEliminate,
                style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeLarge,
                  fontWeight: FontWeightHelper.bold,
                ),
              ),
            ),
          ],
        ),
        CustomShimmerWidget(
          child: SizedBox(
            height: 16,
          ),
        ),
        CustomShimmerWidget(
          child: Container(
            height: 150,
            child: ListView.separated(
              separatorBuilder: (context, index) => SizedBox(
                width: 10,
              ),
              scrollDirection: Axis.horizontal,
              itemCount: 10,
              itemBuilder: (context, index) => Stack(
                children: [
                  Container(
                    height: 150,
                    width: 130,
                    child: Center(
                        child: CachedNetworkImage(
                            imageUrl:
                                'https://api.arab-cbt.com/uploads/concerns/1731066905144-730578099.png')),
                  ),
                  Container(
                    height: 150,
                    width: 130,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          TextApp(
                            text: 'title',
                            style: context.textStyle.copyWith(
                              fontSize: AppDimensions.fontSizeDefault,
                              color: context.color.whiteColor,
                              fontWeight: FontWeightHelper.bold,
                            ),
                          ),
                        ],
                      )),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
