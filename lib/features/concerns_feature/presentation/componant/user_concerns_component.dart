import 'package:cached_network_image/cached_network_image.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/presentation/shimmer_componant/user_concerns_shimmer_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UserConcernsComponent extends StatefulWidget {
  const UserConcernsComponent({super.key});

  @override
  State<UserConcernsComponent> createState() => _UserConcernsComponentState();
}

class _UserConcernsComponentState extends State<UserConcernsComponent> {
  @override
  void initState() {
    context.read<ConcernsCubit>().fetchConcerns();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 16,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            TextApp(
              text: 'اداة التحرر من الخوف والقلق',
              style: context.textStyle.copyWith(
                fontSize: AppDimensions.fontSizeLarge,
                fontWeight: FontWeightHelper.bold,
              ),
            ),
            SizedBox(
              height: 5,
            ),
            TextApp(
              text: 'اختر خوفك وابدأ رحلتك الآن؟',
              style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeDefault,
                  fontWeight: FontWeightHelper.bold,
                  color: context.color.grayColor),
            ),
          ],
        ),
        SizedBox(
          height: 10,
        ),
        BlocBuilder<ConcernsCubit, ConcernsState>(builder: (context, state) {
          if (state.errorConcerns != null) {
            return CustomErrorWidget(
              failure: state.errorConcerns,
              onPressed: () {
                context.read<ConcernsCubit>().fetchConcerns();
              },
            );
          } else if (state.isLoadingConcerns == true) {
            return UserConcernsShimmerComponent();
          }
          return Container(
            height: 200,
            child: ListView.separated(
              separatorBuilder: (context, index) => SizedBox(
                width: 5,
              ),
              scrollDirection: Axis.horizontal,
              itemCount: state.successConcerns?.length ?? 0,
              itemBuilder: (context, index) {
                ConcernModel? concern = state.successConcerns?[index];

                return InkWell(
                  onTap: () {
                    context
                        .pushRoute(SubCategoryListViewRoute(id: concern!.id!));
                  },
                  child: Column(
                    children: [
                      Container(
                        height: 150,
                        width: 150,
                        child: Center(
                            child: CustomCachedNetworkImage(
                                borderRadius: 10,
                                imageUrl: concern?.image ?? '')),
                      ),
                      // Center(
                      //   child: Container(
                      //     decoration: BoxDecoration(
                      //         borderRadius: BorderRadius.circular(10),
                      //         color: context.color.textColor?.withOpacity(.5)),
                      //     height: 150,
                      //     width: 150,
                      //     child: Padding(
                      //       padding: const EdgeInsets.all(8.0),
                      //       child: Center(
                      //           child: Column(
                      //         crossAxisAlignment: CrossAxisAlignment.start,
                      //         mainAxisAlignment: MainAxisAlignment.end,
                      //         children: [
                      //           Padding(
                      //             padding: const EdgeInsets.all(8.0),
                      //             child: TextApp(
                      //               text: concern?.name ?? '',
                      //               style: context.textStyle.copyWith(
                      //                 fontSize: AppDimensions.fontSizeDefault,
                      //                 color: context.color.whiteColor,
                      //                 fontWeight: FontWeightHelper.bold,
                      //               ),
                      //               textAlign: TextAlign.center,
                      //             ),
                      //           ),
                      //         ],
                      //       )),
                      //     ),
                      //   ),
                      // )
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Container(
                          width: 150,

                          child: TextApp(
                            text: concern?.name ?? '',
                            style: context.textStyle.copyWith(
                              fontSize: AppDimensions.fontSizeDefault,
                              // color: context.color.whiteColor,
                              fontWeight: FontWeightHelper.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),  ],
                  ),
                );
              },
            ),
          );
        }),
      ],
    );
  }
}
