import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/domain/repository/concerns_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

part 'concerns_cubit.freezed.dart';
part 'concerns_state.dart';

@injectable
class ConcernsCubit extends Cubit<ConcernsState> {
  ConcernsCubit(this.repository) : super(ConcernsState.initial());

  final ConcernsRepository repository;

  Future<void> fetchConcerns() async {
    emit(state.copyWith(
      isLoadingConcerns: true,
      successConcerns: null,
      errorConcerns: null,
    ));
    final result = await repository.fetchConcerns();

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingConcerns: false,
          errorConcerns: failure,
          successConcerns: null,
        ));
      },
      (concerns) {
        emit(state.copyWith(
          isLoadingConcerns: false,
          errorConcerns: null,
          successConcerns: concerns,
        ));
      },
    );
  }

  Future<void> getConcernsById({
    required int id,
  }) async {
    emit(state.copyWith(
      isLoadingGetConcernById: true,
      successGetConcernById: null,
      errorGetConcernById: null,
    ));

    final result = await repository.getConcernsById(id: id);

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingGetConcernById: false,
          errorGetConcernById: failure,
          successGetConcernById: null,
        ));
      },
      (success) {
        emit(state.copyWith(
          isLoadingGetConcernById: false,
          errorGetConcernById: null,
          successGetConcernById: success,
        ));
      },
    );
  }

  Future<void> getQuestionsByThoughtId({
    required int id,
  }) async {
    emit(state.copyWith(
      isLoadingQuestions: true,
      successQuestions: null,
      errorQuestions: null,
    ));

    final result = await repository.getQuestionsByThoughtId(id: id);

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingQuestions: false,
          errorQuestions: failure,
          successQuestions: null,
        ));
      },
      (success) {
        emit(state.copyWith(
          isLoadingQuestions: false,
          errorQuestions: null,
          successQuestions: success,
        ));
      },
    );
  }

  // Comparison history methods
  Future<void> createComparisonHistory({
    required int id,
    required int score,
  }) async {
    final result = await repository.createComparisonHistory(
      id: id,
      score: score,
    );

    result.fold(
      (failure) {
        // Handle error if needed
        print('Error creating comparison history: $failure');
      },
      (success) {
        // Handle success if needed
        print('Comparison history created successfully');
      },
    );
  }

  Future<void> updateAfterScoreAudio({
    required int id,
    required int afterScoreAudio,
  }) async {
    final result = await repository.updateAfterScoreAudio(
      id: id,
      afterScoreAudio: afterScoreAudio,
    );

    result.fold(
      (failure) {
        // Handle error if needed
        print('Error updating after score audio: $failure');
      },
      (success) {
        // Handle success if needed
        print('After score audio updated successfully');
      },
    );
  }

  Future<void> updateAfterScore({
    required int id,
    required int afterScore,
  }) async {
    final result = await repository.updateAfterScore(
      id: id,
      afterScore: afterScore,
    );

    result.fold(
      (failure) {
        // Handle error if needed
        print('Error updating after score: $failure');
      },
      (success) {
        // Handle success if needed
        print('After score updated successfully');
      },
    );
  }
}
