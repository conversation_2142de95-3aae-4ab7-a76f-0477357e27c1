part of 'concerns_cubit.dart';

@freezed
class ConcernsState with _$ConcernsState {
  factory ConcernsState({
    ///============================================================

    required bool? isLoadingConcerns,
    required Failure? errorConcerns,
    required List<ConcernModel>? successConcerns,

    ///============================================================
    required bool? isLoadingGetConcernById,
    required Failure? errorGetConcernById,
    required ConcernModel? successGetConcernById,
    ///============================================================

    required bool? isLoadingQuestions,
    required Failure? errorQuestions,
    required List<QuestionsModel>? successQuestions,



    @Default(true) bool isChecked,
  }) = _$ConcernsStateImpl;

  factory ConcernsState.initial() => ConcernsState(
    errorConcerns: null,
    isLoadingConcerns: null,
    successConcerns: null,
    errorGetConcernById: null,
    isLoadingGetConcernById: null,
    successGetConcernById: null,
    isLoadingQuestions: null,
    successQuestions: null,
    errorQuestions: null,
      );
}
