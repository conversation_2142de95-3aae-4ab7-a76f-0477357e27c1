// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'concerns_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConcernsState {
  ///============================================================
  bool? get isLoadingConcerns => throw _privateConstructorUsedError;
  Failure? get errorConcerns => throw _privateConstructorUsedError;
  List<ConcernModel>? get successConcerns => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingGetConcernById => throw _privateConstructorUsedError;
  Failure? get errorGetConcernById => throw _privateConstructorUsedError;
  ConcernModel? get successGetConcernById => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingQuestions => throw _privateConstructorUsedError;
  Failure? get errorQuestions => throw _privateConstructorUsedError;
  List<QuestionsModel>? get successQuestions =>
      throw _privateConstructorUsedError;
  bool get isChecked => throw _privateConstructorUsedError;

  /// Create a copy of ConcernsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConcernsStateCopyWith<ConcernsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConcernsStateCopyWith<$Res> {
  factory $ConcernsStateCopyWith(
          ConcernsState value, $Res Function(ConcernsState) then) =
      _$ConcernsStateCopyWithImpl<$Res, ConcernsState>;
  @useResult
  $Res call(
      {bool? isLoadingConcerns,
      Failure? errorConcerns,
      List<ConcernModel>? successConcerns,
      bool? isLoadingGetConcernById,
      Failure? errorGetConcernById,
      ConcernModel? successGetConcernById,
      bool? isLoadingQuestions,
      Failure? errorQuestions,
      List<QuestionsModel>? successQuestions,
      bool isChecked});
}

/// @nodoc
class _$ConcernsStateCopyWithImpl<$Res, $Val extends ConcernsState>
    implements $ConcernsStateCopyWith<$Res> {
  _$ConcernsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConcernsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingConcerns = freezed,
    Object? errorConcerns = freezed,
    Object? successConcerns = freezed,
    Object? isLoadingGetConcernById = freezed,
    Object? errorGetConcernById = freezed,
    Object? successGetConcernById = freezed,
    Object? isLoadingQuestions = freezed,
    Object? errorQuestions = freezed,
    Object? successQuestions = freezed,
    Object? isChecked = null,
  }) {
    return _then(_value.copyWith(
      isLoadingConcerns: freezed == isLoadingConcerns
          ? _value.isLoadingConcerns
          : isLoadingConcerns // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorConcerns: freezed == errorConcerns
          ? _value.errorConcerns
          : errorConcerns // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successConcerns: freezed == successConcerns
          ? _value.successConcerns
          : successConcerns // ignore: cast_nullable_to_non_nullable
              as List<ConcernModel>?,
      isLoadingGetConcernById: freezed == isLoadingGetConcernById
          ? _value.isLoadingGetConcernById
          : isLoadingGetConcernById // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetConcernById: freezed == errorGetConcernById
          ? _value.errorGetConcernById
          : errorGetConcernById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetConcernById: freezed == successGetConcernById
          ? _value.successGetConcernById
          : successGetConcernById // ignore: cast_nullable_to_non_nullable
              as ConcernModel?,
      isLoadingQuestions: freezed == isLoadingQuestions
          ? _value.isLoadingQuestions
          : isLoadingQuestions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorQuestions: freezed == errorQuestions
          ? _value.errorQuestions
          : errorQuestions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successQuestions: freezed == successQuestions
          ? _value.successQuestions
          : successQuestions // ignore: cast_nullable_to_non_nullable
              as List<QuestionsModel>?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$$ConcernsStateImplImplCopyWith<$Res>
    implements $ConcernsStateCopyWith<$Res> {
  factory _$$$ConcernsStateImplImplCopyWith(_$$ConcernsStateImplImpl value,
          $Res Function(_$$ConcernsStateImplImpl) then) =
      __$$$ConcernsStateImplImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isLoadingConcerns,
      Failure? errorConcerns,
      List<ConcernModel>? successConcerns,
      bool? isLoadingGetConcernById,
      Failure? errorGetConcernById,
      ConcernModel? successGetConcernById,
      bool? isLoadingQuestions,
      Failure? errorQuestions,
      List<QuestionsModel>? successQuestions,
      bool isChecked});
}

/// @nodoc
class __$$$ConcernsStateImplImplCopyWithImpl<$Res>
    extends _$ConcernsStateCopyWithImpl<$Res, _$$ConcernsStateImplImpl>
    implements _$$$ConcernsStateImplImplCopyWith<$Res> {
  __$$$ConcernsStateImplImplCopyWithImpl(_$$ConcernsStateImplImpl _value,
      $Res Function(_$$ConcernsStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConcernsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingConcerns = freezed,
    Object? errorConcerns = freezed,
    Object? successConcerns = freezed,
    Object? isLoadingGetConcernById = freezed,
    Object? errorGetConcernById = freezed,
    Object? successGetConcernById = freezed,
    Object? isLoadingQuestions = freezed,
    Object? errorQuestions = freezed,
    Object? successQuestions = freezed,
    Object? isChecked = null,
  }) {
    return _then(_$$ConcernsStateImplImpl(
      isLoadingConcerns: freezed == isLoadingConcerns
          ? _value.isLoadingConcerns
          : isLoadingConcerns // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorConcerns: freezed == errorConcerns
          ? _value.errorConcerns
          : errorConcerns // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successConcerns: freezed == successConcerns
          ? _value._successConcerns
          : successConcerns // ignore: cast_nullable_to_non_nullable
              as List<ConcernModel>?,
      isLoadingGetConcernById: freezed == isLoadingGetConcernById
          ? _value.isLoadingGetConcernById
          : isLoadingGetConcernById // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetConcernById: freezed == errorGetConcernById
          ? _value.errorGetConcernById
          : errorGetConcernById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetConcernById: freezed == successGetConcernById
          ? _value.successGetConcernById
          : successGetConcernById // ignore: cast_nullable_to_non_nullable
              as ConcernModel?,
      isLoadingQuestions: freezed == isLoadingQuestions
          ? _value.isLoadingQuestions
          : isLoadingQuestions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorQuestions: freezed == errorQuestions
          ? _value.errorQuestions
          : errorQuestions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successQuestions: freezed == successQuestions
          ? _value._successQuestions
          : successQuestions // ignore: cast_nullable_to_non_nullable
              as List<QuestionsModel>?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$$ConcernsStateImplImpl implements _$ConcernsStateImpl {
  _$$ConcernsStateImplImpl(
      {required this.isLoadingConcerns,
      required this.errorConcerns,
      required final List<ConcernModel>? successConcerns,
      required this.isLoadingGetConcernById,
      required this.errorGetConcernById,
      required this.successGetConcernById,
      required this.isLoadingQuestions,
      required this.errorQuestions,
      required final List<QuestionsModel>? successQuestions,
      this.isChecked = true})
      : _successConcerns = successConcerns,
        _successQuestions = successQuestions;

  ///============================================================
  @override
  final bool? isLoadingConcerns;
  @override
  final Failure? errorConcerns;
  final List<ConcernModel>? _successConcerns;
  @override
  List<ConcernModel>? get successConcerns {
    final value = _successConcerns;
    if (value == null) return null;
    if (_successConcerns is EqualUnmodifiableListView) return _successConcerns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  ///============================================================
  @override
  final bool? isLoadingGetConcernById;
  @override
  final Failure? errorGetConcernById;
  @override
  final ConcernModel? successGetConcernById;

  ///============================================================
  @override
  final bool? isLoadingQuestions;
  @override
  final Failure? errorQuestions;
  final List<QuestionsModel>? _successQuestions;
  @override
  List<QuestionsModel>? get successQuestions {
    final value = _successQuestions;
    if (value == null) return null;
    if (_successQuestions is EqualUnmodifiableListView)
      return _successQuestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool isChecked;

  @override
  String toString() {
    return 'ConcernsState(isLoadingConcerns: $isLoadingConcerns, errorConcerns: $errorConcerns, successConcerns: $successConcerns, isLoadingGetConcernById: $isLoadingGetConcernById, errorGetConcernById: $errorGetConcernById, successGetConcernById: $successGetConcernById, isLoadingQuestions: $isLoadingQuestions, errorQuestions: $errorQuestions, successQuestions: $successQuestions, isChecked: $isChecked)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$$ConcernsStateImplImpl &&
            (identical(other.isLoadingConcerns, isLoadingConcerns) ||
                other.isLoadingConcerns == isLoadingConcerns) &&
            (identical(other.errorConcerns, errorConcerns) ||
                other.errorConcerns == errorConcerns) &&
            const DeepCollectionEquality()
                .equals(other._successConcerns, _successConcerns) &&
            (identical(
                    other.isLoadingGetConcernById, isLoadingGetConcernById) ||
                other.isLoadingGetConcernById == isLoadingGetConcernById) &&
            (identical(other.errorGetConcernById, errorGetConcernById) ||
                other.errorGetConcernById == errorGetConcernById) &&
            (identical(other.successGetConcernById, successGetConcernById) ||
                other.successGetConcernById == successGetConcernById) &&
            (identical(other.isLoadingQuestions, isLoadingQuestions) ||
                other.isLoadingQuestions == isLoadingQuestions) &&
            (identical(other.errorQuestions, errorQuestions) ||
                other.errorQuestions == errorQuestions) &&
            const DeepCollectionEquality()
                .equals(other._successQuestions, _successQuestions) &&
            (identical(other.isChecked, isChecked) ||
                other.isChecked == isChecked));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoadingConcerns,
      errorConcerns,
      const DeepCollectionEquality().hash(_successConcerns),
      isLoadingGetConcernById,
      errorGetConcernById,
      successGetConcernById,
      isLoadingQuestions,
      errorQuestions,
      const DeepCollectionEquality().hash(_successQuestions),
      isChecked);

  /// Create a copy of ConcernsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$$ConcernsStateImplImplCopyWith<_$$ConcernsStateImplImpl> get copyWith =>
      __$$$ConcernsStateImplImplCopyWithImpl<_$$ConcernsStateImplImpl>(
          this, _$identity);
}

abstract class _$ConcernsStateImpl implements ConcernsState {
  factory _$ConcernsStateImpl(
      {required final bool? isLoadingConcerns,
      required final Failure? errorConcerns,
      required final List<ConcernModel>? successConcerns,
      required final bool? isLoadingGetConcernById,
      required final Failure? errorGetConcernById,
      required final ConcernModel? successGetConcernById,
      required final bool? isLoadingQuestions,
      required final Failure? errorQuestions,
      required final List<QuestionsModel>? successQuestions,
      final bool isChecked}) = _$$ConcernsStateImplImpl;

  ///============================================================
  @override
  bool? get isLoadingConcerns;
  @override
  Failure? get errorConcerns;
  @override
  List<ConcernModel>? get successConcerns;

  ///============================================================
  @override
  bool? get isLoadingGetConcernById;
  @override
  Failure? get errorGetConcernById;
  @override
  ConcernModel? get successGetConcernById;

  ///============================================================
  @override
  bool? get isLoadingQuestions;
  @override
  Failure? get errorQuestions;
  @override
  List<QuestionsModel>? get successQuestions;
  @override
  bool get isChecked;

  /// Create a copy of ConcernsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$$ConcernsStateImplImplCopyWith<_$$ConcernsStateImplImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
