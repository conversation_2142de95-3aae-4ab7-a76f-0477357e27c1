part of 'lecture_cubit.dart';

@freezed
class LectureState with _$LectureState {
  factory LectureState({
    ///============================================================
    required bool? isLoadingLecture,
    required Failure? errorLecture,
    required List<LectureModel>? successLecture,
  }) = _$LectureStateImpl;

  factory LectureState.initial() => LectureState(
        isLoadingLecture: null,
        errorLecture: null,
        successLecture: null,
      );
}
