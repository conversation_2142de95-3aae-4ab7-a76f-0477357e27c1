import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/lecture_feature/domain/model/lecture_model.dart';
import 'package:clean_arc/features/lecture_feature/domain/repository/lecture_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

part 'lecture_cubit.freezed.dart';
part 'lecture_state.dart';

@injectable
class LectureCubit extends Cubit<LectureState> {
  LectureCubit(this.repository) : super(LectureState.initial());

  final LectureRepository repository;

  Future<void> fetchLectures() async {
    emit(state.copyWith(
      isLoadingLecture: true,
      errorLecture: null,
      successLecture: null,
    ));

    final result = await repository.getLectures();

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingLecture: false,
          errorLecture: failure,
          successLecture: null,
        ));
      },
      (lectures) {
        emit(state.copyWith(
          isLoadingLecture: false,
          errorLecture: null,
          successLecture: lectures,
        ));
      },
    );
  }
}
