// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lecture_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LectureState {
  ///============================================================
  bool? get isLoadingLecture => throw _privateConstructorUsedError;
  Failure? get errorLecture => throw _privateConstructorUsedError;
  List<LectureModel>? get successLecture => throw _privateConstructorUsedError;

  /// Create a copy of LectureState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LectureStateCopyWith<LectureState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LectureStateCopyWith<$Res> {
  factory $LectureStateCopyWith(
          LectureState value, $Res Function(LectureState) then) =
      _$LectureStateCopyWithImpl<$Res, LectureState>;
  @useResult
  $Res call(
      {bool? isLoadingLecture,
      Failure? errorLecture,
      List<LectureModel>? successLecture});
}

/// @nodoc
class _$LectureStateCopyWithImpl<$Res, $Val extends LectureState>
    implements $LectureStateCopyWith<$Res> {
  _$LectureStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LectureState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingLecture = freezed,
    Object? errorLecture = freezed,
    Object? successLecture = freezed,
  }) {
    return _then(_value.copyWith(
      isLoadingLecture: freezed == isLoadingLecture
          ? _value.isLoadingLecture
          : isLoadingLecture // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorLecture: freezed == errorLecture
          ? _value.errorLecture
          : errorLecture // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successLecture: freezed == successLecture
          ? _value.successLecture
          : successLecture // ignore: cast_nullable_to_non_nullable
              as List<LectureModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$$LectureStateImplImplCopyWith<$Res>
    implements $LectureStateCopyWith<$Res> {
  factory _$$$LectureStateImplImplCopyWith(_$$LectureStateImplImpl value,
          $Res Function(_$$LectureStateImplImpl) then) =
      __$$$LectureStateImplImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isLoadingLecture,
      Failure? errorLecture,
      List<LectureModel>? successLecture});
}

/// @nodoc
class __$$$LectureStateImplImplCopyWithImpl<$Res>
    extends _$LectureStateCopyWithImpl<$Res, _$$LectureStateImplImpl>
    implements _$$$LectureStateImplImplCopyWith<$Res> {
  __$$$LectureStateImplImplCopyWithImpl(_$$LectureStateImplImpl _value,
      $Res Function(_$$LectureStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of LectureState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingLecture = freezed,
    Object? errorLecture = freezed,
    Object? successLecture = freezed,
  }) {
    return _then(_$$LectureStateImplImpl(
      isLoadingLecture: freezed == isLoadingLecture
          ? _value.isLoadingLecture
          : isLoadingLecture // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorLecture: freezed == errorLecture
          ? _value.errorLecture
          : errorLecture // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successLecture: freezed == successLecture
          ? _value._successLecture
          : successLecture // ignore: cast_nullable_to_non_nullable
              as List<LectureModel>?,
    ));
  }
}

/// @nodoc

class _$$LectureStateImplImpl implements _$LectureStateImpl {
  _$$LectureStateImplImpl(
      {required this.isLoadingLecture,
      required this.errorLecture,
      required final List<LectureModel>? successLecture})
      : _successLecture = successLecture;

  ///============================================================
  @override
  final bool? isLoadingLecture;
  @override
  final Failure? errorLecture;
  final List<LectureModel>? _successLecture;
  @override
  List<LectureModel>? get successLecture {
    final value = _successLecture;
    if (value == null) return null;
    if (_successLecture is EqualUnmodifiableListView) return _successLecture;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LectureState(isLoadingLecture: $isLoadingLecture, errorLecture: $errorLecture, successLecture: $successLecture)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$$LectureStateImplImpl &&
            (identical(other.isLoadingLecture, isLoadingLecture) ||
                other.isLoadingLecture == isLoadingLecture) &&
            (identical(other.errorLecture, errorLecture) ||
                other.errorLecture == errorLecture) &&
            const DeepCollectionEquality()
                .equals(other._successLecture, _successLecture));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoadingLecture, errorLecture,
      const DeepCollectionEquality().hash(_successLecture));

  /// Create a copy of LectureState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$$LectureStateImplImplCopyWith<_$$LectureStateImplImpl> get copyWith =>
      __$$$LectureStateImplImplCopyWithImpl<_$$LectureStateImplImpl>(
          this, _$identity);
}

abstract class _$LectureStateImpl implements LectureState {
  factory _$LectureStateImpl(
          {required final bool? isLoadingLecture,
          required final Failure? errorLecture,
          required final List<LectureModel>? successLecture}) =
      _$$LectureStateImplImpl;

  ///============================================================
  @override
  bool? get isLoadingLecture;
  @override
  Failure? get errorLecture;
  @override
  List<LectureModel>? get successLecture;

  /// Create a copy of LectureState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$$LectureStateImplImplCopyWith<_$$LectureStateImplImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
