import 'package:clean_arc/core/data/repositories/base_repository_impl.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/lecture_feature/domain/model/lecture_model.dart';
import 'package:clean_arc/features/lecture_feature/domain/services/remote/lecture_services.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

abstract class LectureRepository {
  Future<Either<Failure, List<LectureModel>>> getLectures();
}

@LazySingleton(as: LectureRepository)
class LectureRepositoryImpl extends BaseRepositoryImpl implements LectureRepository {
  final LectureServices _services;

  LectureRepositoryImpl(super.logger, this._services);

  @override
  Future<Either<Failure, List<LectureModel>>> getLectures() {
    return request(() async {
      final result = await _services.getLectures();
      return Right(result.data ?? []);
    });
  }
}
