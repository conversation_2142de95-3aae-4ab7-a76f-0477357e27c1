import 'sub_lecture_model.dart';

class LectureModel {
  LectureModel({
    required this.id,
    required this.text,
    required this.description,
    required this.type,
    required this.image,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.subCategories,
  });

  final int? id;
  final String? text;
  final String? description;
  final String? type;
  final String? image;
  final int? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<SubLectureModel> subCategories;

  factory LectureModel.fromJson(Map<String, dynamic> json) {
    return LectureModel(
      id: json["id"],
      text: json["text"],
      description: json["description"],
      type: json["type"],
      image: json["image"],
      isActive: json["is_active"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      subCategories: json["sub_categories"] == null
          ? []
          : List<SubLectureModel>.from(
              json["sub_categories"]!.map((x) => SubLectureModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "text": text,
        "description": description,
        "type": type,
        "image": image,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "sub_categories": subCategories.map((x) => x.toJson()).toList(),
      };
}
