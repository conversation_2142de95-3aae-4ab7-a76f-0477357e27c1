class SubLectureModel {
  SubLectureModel({
    required this.subCategoryId,
    required this.subCategoryText,
    required this.subCategoryDescription,
    required this.subCategoryType,
    required this.subCategoryImage,
    required this.subCategoryCreatedAt,
    required this.subCategoryUpdatedAt,
  });

  final int? subCategoryId;
  final String? subCategoryText;
  final String? subCategoryDescription;
  final String? subCategoryType;
  final String? subCategoryImage;
  final DateTime? subCategoryCreatedAt;
  final DateTime? subCategoryUpdatedAt;

  factory SubLectureModel.fromJson(Map<String, dynamic> json) {
    return SubLectureModel(
      subCategoryId: json["sub_category_id"],
      subCategoryText: json["sub_category_text"],
      subCategoryDescription: json["sub_category_description"],
      subCategoryType: json["sub_category_type"],
      subCategoryImage: json["sub_category_image"],
      subCategoryCreatedAt: DateTime.tryParse(json["sub_category_created_at"] ?? ""),
      subCategoryUpdatedAt: DateTime.tryParse(json["sub_category_updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "sub_category_id": subCategoryId,
        "sub_category_text": subCategoryText,
        "sub_category_description": subCategoryDescription,
        "sub_category_type": subCategoryType,
        "sub_category_image": subCategoryImage,
        "sub_category_created_at": subCategoryCreatedAt?.toIso8601String(),
        "sub_category_updated_at": subCategoryUpdatedAt?.toIso8601String(),
      };
}
