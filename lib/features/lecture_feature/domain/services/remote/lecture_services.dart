import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/lecture_feature/domain/model/lecture_model.dart';
import 'package:injectable/injectable.dart';
import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:retrofit/retrofit.dart';

part 'lecture_services.g.dart';

@LazySingleton()
@RestApi(baseUrl: '')
abstract class LectureServices {
  @factoryMethod
  factory LectureServices(Dio dio, Configuration configuration) {
    return _LectureServices(dio, baseUrl: configuration.getApiUrl);
  }

  @GET('lectures')
  Future<BaseResponseModel<List<LectureModel>>> getLectures();
}
