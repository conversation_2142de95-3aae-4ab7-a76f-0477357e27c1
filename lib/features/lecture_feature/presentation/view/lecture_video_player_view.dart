import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';

import '../../../../core/services/orientation_service.dart';

@RoutePage()
class LectureVideoPlayerView extends StatefulWidget {
  final String title;
  final String description;
  final String videoUrl;

  const LectureVideoPlayerView({
    super.key,
    required this.title,
    required this.description,
    required this.videoUrl,
  });

  @override
  State<LectureVideoPlayerView> createState() => _LectureVideoPlayerViewState();
}

class _LectureVideoPlayerViewState extends State<LectureVideoPlayerView> {
  VideoPlayerController? _videoPlayerController;
  bool _isPlaying = false;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  bool _showControls = true;
  bool _isFullScreen = false;

  Future<void> requestStoragePermission() async {
    final status = await Permission.storage.request();

    if (status.isGranted) {
      print("Permission Granted");
    } else if (status.isDenied) {
      print("Permission Denied");
    } else if (status.isPermanentlyDenied) {
      print("Permission Permanently Denied");
      openAppSettings();
    }
  }

  String _getFullVideoUrl() {
    // Add base URL if the video URL is relative
    if (widget.videoUrl.startsWith('/')) {
      return 'https://api.arab-cbt.com' + widget.videoUrl;
    }
    return widget.videoUrl;
  }

  @override
  void initState() {
    super.initState();
    requestStoragePermission();
    _initializeVideo();
  }

  void _initializeVideo() {
    final fullUrl = _getFullVideoUrl();
    print("Initializing video with URL: $fullUrl");

    _videoPlayerController = VideoPlayerController.network(fullUrl)
      ..initialize().then((_) {
        setState(() {
          _isLoading = false;
          _hasError = false;
        });
        _videoPlayerController!.play();
        setState(() {
          _isPlaying = true;
        });
      }).catchError((error) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = error.toString();
        });
        print("Video initialization error: $error");
      });

    _videoPlayerController!.addListener(() {
      setState(() {
        _isPlaying = _videoPlayerController!.value.isPlaying;
      });
    });
  }

  void _togglePlayPause() {
    if (_videoPlayerController != null) {
      if (_videoPlayerController!.value.isPlaying) {
        _videoPlayerController!.pause();
      } else {
        _videoPlayerController!.play();
      }
    }
  }

  void _toggleFullScreen() async {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      // Enter fullscreen mode
      await OrientationService.enterFullscreen();

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => _FullScreenVideoPlayer(
            controller: _videoPlayerController!,
            title: widget.title,
            onExit: () async {
              // Exit fullscreen mode
              await OrientationService.exitFullscreen();
              setState(() {
                _isFullScreen = false;
              });
              Navigator.of(context).pop();
            },
          ),
        ),
      );
    }
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    // Auto-hide controls after 3 seconds
    if (_showControls) {
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && _videoPlayerController!.value.isPlaying) {
          setState(() {
            _showControls = false;
          });
        }
      });
    }
  }

  void _seekTo(Duration position) {
    _videoPlayerController?.seekTo(position);
  }

  void _skipForward() {
    final currentPosition =
        _videoPlayerController?.value.position ?? Duration.zero;
    final newPosition = currentPosition + Duration(seconds: 10);
    final duration = _videoPlayerController?.value.duration ?? Duration.zero;

    if (newPosition < duration) {
      _seekTo(newPosition);
    } else {
      _seekTo(duration);
    }
  }

  void _skipBackward() {
    final currentPosition =
        _videoPlayerController?.value.position ?? Duration.zero;
    final newPosition = currentPosition - Duration(seconds: 10);

    if (newPosition > Duration.zero) {
      _seekTo(newPosition);
    } else {
      _seekTo(Duration.zero);
    }
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        context,
        title: widget.title,
        centerTitle: true,
        showNotification: false,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Video Player Section
          Container(
            width: double.infinity,
            height: 250,
            color: Colors.black,
            child: _buildVideoPlayer(),
          ),

          // Content Section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  TextApp(
                    text: widget.title,
                    style: context.textStyle.copyWith(
                      fontSize: AppDimensions.fontSizeLarge,
                      fontWeight: FontWeightHelper.bold,
                    ),
                  ),
                  SizedBox(height: 8),

                  // Description
                  TextApp(
                    text: widget.description,
                    style: context.textStyle.copyWith(
                      fontSize: AppDimensions.fontSizeDefault,
                      color: context.color.grayColor,
                      fontWeight: FontWeightHelper.regular,
                    ),
                  ),
                  SizedBox(height: 16),

                  // Video Info
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: context.color.whiteColor,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: context.color.borderColor!,
                          spreadRadius: 1,
                        )
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextApp(
                          text: "معلومات الفيديو",
                          style: context.textStyle.copyWith(
                            fontSize: AppDimensions.fontSizeDefault,
                            fontWeight: FontWeightHelper.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        if (_videoPlayerController != null &&
                            _videoPlayerController!.value.isInitialized)
                          TextApp(
                            text:
                                "المدة: ${_formatDuration(_videoPlayerController!.value.duration)}",
                            style: context.textStyle.copyWith(
                              fontSize: AppDimensions.fontSizeSmall,
                              color: context.color.grayColor,
                            ),
                          ),
                        TextApp(
                          text: "نوع المحتوى: محاضرة فيديو",
                          style: context.textStyle.copyWith(
                            fontSize: AppDimensions.fontSizeSmall,
                            color: context.color.grayColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: context.color.primaryColor),
            SizedBox(height: 16),
            TextApp(
              text: "جاري تحميل الفيديو...",
              style: context.textStyle.copyWith(color: Colors.white),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, color: Colors.red, size: 48),
            SizedBox(height: 16),
            TextApp(
              text: "خطأ في تحميل الفيديو",
              style: context.textStyle.copyWith(color: Colors.white),
            ),
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: TextApp(
                  text: _errorMessage!,
                  style: context.textStyle.copyWith(
                    color: Colors.white70,
                    fontSize: AppDimensions.fontSizeSmall,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _hasError = false;
                  _errorMessage = null;
                });
                _initializeVideo();
              },
              child: TextApp(text: "إعادة المحاولة"),
            ),
          ],
        ),
      );
    }

    if (_videoPlayerController != null &&
        _videoPlayerController!.value.isInitialized) {
      return Directionality(
        textDirection: TextDirection.ltr,
        child: GestureDetector(
          onTap: _toggleControls,
          child: AspectRatio(
            aspectRatio: _videoPlayerController!.value.aspectRatio,
            child: Stack(
              children: [
                VideoPlayer(_videoPlayerController!),

                // Controls overlay
                if (_showControls) ...[
                  // Top controls
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Colors.black54, Colors.transparent],
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextApp(
                              text: widget.title,
                              style: context.textStyle.copyWith(
                                color: Colors.white,
                                fontSize: AppDimensions.fontSizeDefault,
                                fontWeight: FontWeightHelper.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          IconButton(
                            onPressed: _toggleFullScreen,
                            icon: Icon(
                              Icons.fullscreen,
                              color: Colors.white,
                              size: 28,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Center controls
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Skip backward
                        IconButton(
                          onPressed: _skipBackward,
                          icon: Icon(
                            Icons.replay_10,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                        SizedBox(width: 20),

                        // Play/Pause
                        GestureDetector(
                          onTap: _togglePlayPause,
                          child: Container(
                            width: 70,
                            height: 70,
                            decoration: BoxDecoration(
                              color: Colors.black54,
                              borderRadius: BorderRadius.circular(35),
                            ),
                            child: Icon(
                              _isPlaying ? Icons.pause : Icons.play_arrow,
                              color: Colors.white,
                              size: 40,
                            ),
                          ),
                        ),
                        SizedBox(width: 20),

                        // Skip forward
                        IconButton(
                          onPressed: _skipForward,
                          icon: Icon(
                            Icons.forward_10,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Bottom controls
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          colors: [Colors.black54, Colors.transparent],
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Progress bar
                          VideoProgressIndicator(
                            _videoPlayerController!,
                            allowScrubbing: true,
                            colors: VideoProgressColors(
                              playedColor: context.color.primaryColor!,
                              bufferedColor: Colors.grey,
                              backgroundColor: Colors.grey.withOpacity(0.3),
                            ),
                          ),

                          // Time and controls
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8),
                            child: Row(
                              children: [
                                // Current time
                                TextApp(
                                  text: _formatDuration(
                                      _videoPlayerController!.value.position),
                                  style: context.textStyle.copyWith(
                                    color: Colors.white,
                                    fontSize: AppDimensions.fontSizeSmall,
                                  ),
                                ),
                                Spacer(),

                                // Volume control
                                IconButton(
                                  onPressed: () {
                                    setState(() {
                                      _videoPlayerController!.setVolume(
                                        _videoPlayerController!.value.volume > 0
                                            ? 0.0
                                            : 1.0,
                                      );
                                    });
                                  },
                                  icon: Icon(
                                    _videoPlayerController!.value.volume > 0
                                        ? Icons.volume_up
                                        : Icons.volume_off,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ),

                                // Duration
                                TextApp(
                                  text: _formatDuration(
                                      _videoPlayerController!.value.duration),
                                  style: context.textStyle.copyWith(
                                    color: Colors.white,
                                    fontSize: AppDimensions.fontSizeSmall,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      );
    }

    return Center(
      child: TextApp(
        text: "فشل في تحميل الفيديو",
        style: context.textStyle.copyWith(color: Colors.white),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}

// Fullscreen video player widget
class _FullScreenVideoPlayer extends StatefulWidget {
  final VideoPlayerController controller;
  final String title;
  final VoidCallback onExit;

  const _FullScreenVideoPlayer({
    required this.controller,
    required this.title,
    required this.onExit,
  });

  @override
  State<_FullScreenVideoPlayer> createState() => _FullScreenVideoPlayerState();
}

class _FullScreenVideoPlayerState extends State<_FullScreenVideoPlayer> {
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    // Force landscape when entering fullscreen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      OrientationService.forceLandscape();
    });
  }

  @override
  void dispose() {
    // Restore portrait orientation when leaving fullscreen
    OrientationService.exitFullscreen();
    super.dispose();
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    // Auto-hide controls after 3 seconds
    if (_showControls) {
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && widget.controller.value.isPlaying) {
          setState(() {
            _showControls = false;
          });
        }
      });
    }
  }

  void _togglePlayPause() {
    if (widget.controller.value.isPlaying) {
      widget.controller.pause();
    } else {
      widget.controller.play();
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        await OrientationService.exitFullscreen();
        widget.onExit();
        return false; // We handle the navigation ourselves
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Directionality(
          textDirection: TextDirection.ltr,
          child: GestureDetector(
            onTap: _toggleControls,
            child: Stack(
              children: [
                Center(
                  child: AspectRatio(
                    aspectRatio: widget.controller.value.aspectRatio,
                    child: VideoPlayer(widget.controller),
                  ),
                ),
                if (_showControls) ...[
                  // Top controls
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: EdgeInsets.only(
                          top: MediaQuery.of(context).padding.top + 16,
                          left: 16,
                          right: 16,
                          bottom: 16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Colors.black54, Colors.transparent],
                        ),
                      ),
                      child: Row(
                        children: [
                          IconButton(
                            onPressed: () async {
                              await OrientationService.exitFullscreen();
                              widget.onExit();
                            },
                            icon: Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 28,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: TextApp(
                              text: widget.title,
                              style: context.textStyle.copyWith(
                                color: Colors.white,
                                fontSize: AppDimensions.fontSizeDefault,
                                fontWeight: FontWeightHelper.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Center controls
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Skip backward
                        IconButton(
                          onPressed: () {
                            final currentPosition =
                                widget.controller.value.position;
                            final newPosition =
                                currentPosition - Duration(seconds: 10);
                            widget.controller.seekTo(newPosition > Duration.zero
                                ? newPosition
                                : Duration.zero);
                          },
                          icon: Icon(
                            Icons.replay_10,
                            color: Colors.white,
                            size: 50,
                          ),
                        ),
                        SizedBox(width: 30),

                        // Play/Pause
                        GestureDetector(
                          onTap: _togglePlayPause,
                          child: Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Colors.black54,
                              borderRadius: BorderRadius.circular(40),
                            ),
                            child: Icon(
                              widget.controller.value.isPlaying
                                  ? Icons.pause
                                  : Icons.play_arrow,
                              color: Colors.white,
                              size: 50,
                            ),
                          ),
                        ),
                        SizedBox(width: 30),

                        // Skip forward
                        IconButton(
                          onPressed: () {
                            final currentPosition =
                                widget.controller.value.position;
                            final newPosition =
                                currentPosition + Duration(seconds: 10);
                            final duration = widget.controller.value.duration;
                            widget.controller.seekTo(newPosition < duration
                                ? newPosition
                                : duration);
                          },
                          icon: Icon(
                            Icons.forward_10,
                            color: Colors.white,
                            size: 50,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Bottom controls
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          colors: [Colors.black54, Colors.transparent],
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Progress bar
                          VideoProgressIndicator(
                            widget.controller,
                            allowScrubbing: true,
                            colors: VideoProgressColors(
                              playedColor: context.color.primaryColor!,
                              bufferedColor: Colors.grey,
                              backgroundColor: Colors.grey.withOpacity(0.3),
                            ),
                          ),
                          SizedBox(height: 8),

                          // Time display
                          Row(
                            children: [
                              TextApp(
                                text: _formatDuration(
                                    widget.controller.value.position),
                                style: context.textStyle.copyWith(
                                  color: Colors.white,
                                  fontSize: AppDimensions.fontSizeSmall,
                                ),
                              ),
                              Spacer(),
                              TextApp(
                                text: _formatDuration(
                                    widget.controller.value.duration),
                                style: context.textStyle.copyWith(
                                  color: Colors.white,
                                  fontSize: AppDimensions.fontSizeSmall,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}
