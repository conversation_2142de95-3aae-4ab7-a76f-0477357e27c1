import 'dart:ui';

import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/lecture_feature/domain/model/lecture_model.dart';
import 'package:flutter/material.dart';

@RoutePage()
class LectureSubcategoriesView extends StatefulWidget {
  final LectureModel lecture;

  const LectureSubcategoriesView({
    Key? key,
    required this.lecture,
  }) : super(key: key);

  @override
  _LectureSubcategoriesViewState createState() =>
      _LectureSubcategoriesViewState();
}

class _LectureSubcategoriesViewState extends State<LectureSubcategoriesView> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
      child: Scaffold(
        body: Container(
          width: size.width,
          height: size.height,
          // decoration: BoxDecoration(
          //   image: DecorationImage(
          //     fit: BoxFit.cover,
          //     image: AssetImage(
          //       AppImages.images.salimIcon.headerMusicIcon.path,
          //     ),
          //   ),
          // ),
          child: Container(
            child: Stack(
              children: [
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  height: size.height,
                  child: CustomScrollView(
                    slivers: [
                      SliverAppBar(
                        backgroundColor: Colors.black.withOpacity(.8),
                        expandedHeight: 200,
                        pinned: true,
                        stretch: true,
                        flexibleSpace: FlexibleSpaceBar(
                          centerTitle: true,
                          background: Container(
                            width: size.width,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              image: DecorationImage(
                                fit: BoxFit.cover,
                                image: widget.lecture.image != null
                                    ? NetworkImage('https://api.arab-cbt.com' +
                                        widget.lecture.image!)
                                    : AssetImage(AppImages.images.salimIcon
                                        .headerMusicIcon.path) as ImageProvider,
                              ),
                            ),
                            height: 250,
                            child: Center(
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 5),
                                child: Container(
                                  height: 250,
                                  decoration: BoxDecoration(
                                    color: Colors.black38,
                                    image: DecorationImage(
                                      fit: BoxFit.cover,
                                      image: widget.lecture.image != null
                                          ? NetworkImage(
                                              'https://api.arab-cbt.com' +
                                                  widget.lecture.image!)
                                          : AssetImage(AppImages
                                              .images
                                              .salimIcon
                                              .headerMusicIcon
                                              .path) as ImageProvider,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SliverToBoxAdapter(
                        child: SizedBox(height: 0),
                      ),
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextApp(
                                text: widget.lecture.text ?? "محاضرة",
                                style: context.textStyle.copyWith(
                                  fontSize: 20,
                                  fontWeight: FontWeightHelper.bold,
                                ),
                              ),
                              TextApp(
                                text: widget.lecture.description ??
                                    "وصف المحاضرة",
                                style: context.textStyle.copyWith(
                                  fontSize: 15,
                                  fontWeight: FontWeightHelper.regular,
                                  color: context.color.grayColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 5),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextApp(
                                text:
                                    "المحاضرات الفرعية (${widget.lecture.subCategories.length})",
                                style: context.textStyle.copyWith(
                                  fontSize: 20,
                                  color: context.color.primaryColor,
                                  fontWeight: FontWeightHelper.bold,
                                ),
                              ),
                              SizedBox(height: 5),
                              Container(
                                width: context.width / 4.5,
                                height: 2,
                                color: context.color.primaryColor,
                              )
                            ],
                          ),
                        ),
                      ),
                      widget.lecture.subCategories.isEmpty
                          ? SliverToBoxAdapter(
                              child: Padding(
                                padding: EdgeInsets.all(32.0),
                                child: Center(
                                  child: TextApp(
                                    text: "لا توجد محاضرات فرعية متاحة",
                                    style: context.textStyle.copyWith(
                                      fontSize: 16,
                                      color: context.color.grayColor,
                                    ),
                                  ),
                                ),
                              ),
                            )
                          : SliverList(
                              delegate: SliverChildBuilderDelegate(
                                (context, i) {
                                  final subLecture =
                                      widget.lecture.subCategories[i];
                                  return InkWell(
                                    onTap: () {
                                      // Navigate to lecture video player
                                      context.router
                                          .push(LectureVideoPlayerViewRoute(
                                        title: subLecture.subCategoryText ??
                                            "محاضرة فرعية",
                                        description:
                                            subLecture.subCategoryDescription ??
                                                "وصف المحاضرة",
                                        videoUrl:
                                            subLecture.subCategoryImage ?? "",
                                      ));
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16),
                                      child: Column(
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 6),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                Flexible(
                                                  child: Row(
                                                    children: [
                                                      // Play button
                                                      InkWell(
                                                        onTap: () {
                                                          // Navigate to lecture video player
                                                          context.router.push(
                                                              LectureVideoPlayerViewRoute(
                                                            title: subLecture
                                                                    .subCategoryText ??
                                                                "محاضرة فرعية",
                                                            description: subLecture
                                                                    .subCategoryDescription ??
                                                                "وصف المحاضرة",
                                                            videoUrl: subLecture
                                                                    .subCategoryImage ??
                                                                "",
                                                          ));
                                                        },
                                                        child: Container(
                                                          width: 50,
                                                          height: 50,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: context.color
                                                                .primaryColor,
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        25),
                                                          ),
                                                          child: Icon(
                                                            Icons.play_arrow,
                                                            color: Colors.white,
                                                            size: 30,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 12),
                                                      // Content
                                                      Flexible(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            TextApp(
                                                              text: subLecture
                                                                      .subCategoryText ??
                                                                  "محاضرة فرعية",
                                                              maxLines: 1,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              style: context
                                                                  .textStyle
                                                                  .copyWith(
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                            const SizedBox(
                                                                height: 5),
                                                            Row(
                                                              children: [
                                                                TextApp(
                                                                  text: subLecture
                                                                          .subCategoryDescription ??
                                                                      "وصف المحاضرة",
                                                                  style: context
                                                                      .textStyle
                                                                      .copyWith(
                                                                    fontSize:
                                                                        15,
                                                                    fontWeight:
                                                                        FontWeightHelper
                                                                            .medium,
                                                                    color: context
                                                                        .color
                                                                        .grayColor,
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                    width: 10),
                                                                Row(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Icon(
                                                                      Icons
                                                                          .circle,
                                                                      size: 10,
                                                                      color: context
                                                                          .color
                                                                          .grayColor,
                                                                    ),
                                                                    SizedBox(
                                                                        width:
                                                                            5),
                                                                    TextApp(
                                                                      text:
                                                                          "فيديو",
                                                                      style: context
                                                                          .textStyle
                                                                          .copyWith(
                                                                        fontSize:
                                                                            15,
                                                                        fontWeight:
                                                                            FontWeightHelper.medium,
                                                                        color: context
                                                                            .color
                                                                            .grayColor,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ],
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Divider(
                                            color: Colors.white.withOpacity(.2),
                                            thickness: 1.5,
                                          )
                                        ],
                                      ),
                                    ),
                                  );
                                },
                                childCount: widget.lecture.subCategories.length,
                              ),
                            ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
