import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/lecture_feature/controller/lecture_cubit.dart';
import 'package:clean_arc/features/lecture_feature/domain/model/lecture_model.dart';
import 'package:clean_arc/features/lecture_feature/presentation/shimmer_component/lecture_shimmer_component.dart';
import 'package:clean_arc/features/user_layout/lip_feature/controller/lib_controller.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/termination_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LectureComponent extends StatefulWidget {
  const LectureComponent({super.key});

  @override
  State<LectureComponent> createState() => _LectureComponentState();
}

class _LectureComponentState extends State<LectureComponent> {
  @override
  void initState() {
    context.read<LectureCubit>().fetchLectures();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            TextApp(
              text: 'مساحتك الآمنة للوعي النفسي',
              style: context.textStyle.copyWith(
                fontSize: AppDimensions.fontSizeLarge,
                fontWeight: FontWeightHelper.bold,
              ),
            ),
            SizedBox(height: 5),
            TextApp(
              text: 'محاضرات عميقة، بأسلوب مبسّط',
              style: context.textStyle.copyWith(
                fontSize: AppDimensions.fontSizeDefault,
                fontWeight: FontWeightHelper.bold,
                color: context.color.grayColor,
              ),
            ),
          ],
        ),
        SizedBox(height: 10),
        BlocBuilder<LectureCubit, LectureState>(
          builder: (context, state) {
            if (state.errorLecture != null) {
              return CustomErrorWidget(
                failure: state.errorLecture,
                onPressed: () {
                  context.read<LectureCubit>().fetchLectures();
                },
              );
            } else if (state.isLoadingLecture == true) {
              return LectureShimmerComponent();
            }

            return Container(
              height: 200,
              child: ListView.separated(
                separatorBuilder: (context, index) => SizedBox(width: 5),
                scrollDirection: Axis.horizontal,
                itemCount: state.successLecture?.length ?? 0,
                itemBuilder: (context, index) {
                  LectureModel? lecture = state.successLecture?[index];

                  return InkWell(
                    onTap: () {
                      // Navigate to lecture subcategories view
                      context.router.push(
                          LectureSubcategoriesViewRoute(lecture: lecture!));
                    },
                    child: Column(
                      children: [
                        Center(
                          child: Container(
                            height: 150,
                            width: 150,
                            child: Stack(
                              children: [
                                Center(
                                  child: CustomCachedNetworkImage(
                                    borderRadius: 10,
                                    imageUrl: lecture?.image ?? '',
                                  ),
                                ),
                                // Bookmark icon
                                Positioned(
                                  top: 8,
                                  right: 8,
                                  child: InkWell(
                                    onTap: () {
                                      BookManage.toggleLecture(
                                        LectureCashModel.fromJson(
                                            lecture?.toJson() ?? {}),
                                      );
                                    },
                                    child: ValueListenableBuilder<Set<int>>(
                                      valueListenable: BookManage.savedLectures,
                                      builder: (context, savedLectures, child) {
                                        return Container(
                                          padding: EdgeInsets.all(4),
                                          decoration: BoxDecoration(
                                            color: Colors.black54,
                                            borderRadius:
                                                BorderRadius.circular(15),
                                          ),
                                          child: Icon(
                                            savedLectures.contains(lecture?.id)
                                                ? Icons.bookmark
                                                : Icons.bookmark_border,
                                            color: context.color.yellowColor,
                                            size: 20,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Center(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: Container(
                                  width: 150,
                                  child: TextApp(
                                    text: lecture?.text ?? '',
                                    style: context.textStyle.copyWith(
                                      fontSize: AppDimensions.fontSizeDefault,
                                      fontWeight: FontWeightHelper.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }
}
