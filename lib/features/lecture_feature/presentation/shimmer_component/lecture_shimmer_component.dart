import 'package:clean_arc/core/presentation/widget/shimmer/shimar_widget.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';

class LectureShimmerComponent extends StatelessWidget {
  const LectureShimmerComponent({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      child: ListView.separated(
        separatorBuilder: (context, index) => SizedBox(width: 5),
        scrollDirection: Axis.horizontal,
        itemCount: 5,
        itemBuilder: (context, index) {
          return Column(
            children: [
              CustomShimmerWidget(
                child: Container(
                  height: 150,
                  width: 150,
                  decoration: BoxDecoration(
                    color: context.color.grayColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              SizedBox(height: 8),
              CustomShimmerWidget(
                child: Container(
                  height: 20,
                  width: 120,
                  decoration: BoxDecoration(
                    color: context.color.grayColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
