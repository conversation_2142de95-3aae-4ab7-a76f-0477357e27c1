import 'package:clean_arc/core/data/services/shared_prefs/shared_pref.dart';
import 'package:clean_arc/core/data/services/shared_prefs/shared_prefs_key.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:clean_arc/features/layout_feature/cubit_cubit/layout_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class ProfileView extends StatefulWidget {
  const ProfileView({super.key});

  @override
  State<ProfileView> createState() => _ProfileViewState();
}

class _ProfileViewState extends State<ProfileView> {
  final GlobalKey<FormState> profileFormKey = GlobalKey<FormState>();
  bool isEditing = false;

  @override
  void initState() {
    super.initState();
    // Populate controllers with current user data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthCubit>().populateProfileControllers();
    });
  }

  @override
  Widget build(BuildContext context) {
    print(currentUser?.value.toJson());
    return Scaffold(
      appBar: CustomAppBar(context,
          title: context.translate.profile,
          centerTitle: true,
          showNotification: false,
          additionIcon: IconButton(
            icon: Icon(
              isEditing ? Icons.close : Icons.edit_note_rounded,
              size: 30,
              color: isEditing
                  ? context.color.redColor
                  : context.color.primaryColor,
            ),
            onPressed: () {
              setState(() {
                isEditing = !isEditing;
                if (isEditing) {
                  context.read<AuthCubit>().populateProfileControllers();
                }
              });
            },
          )),
      body: BlocConsumer<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state.successUpdateProfile != null) {
            setState(() {
              isEditing = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('تم تحديث الملف الشخصي بنجاح')),
            );
          }
          if (state.isErrorUpdateProfile != null) {
            String errorMessage = 'Update failed';
            if (state.isErrorUpdateProfile is ServerFailure) {
              errorMessage =
                  (state.isErrorUpdateProfile as ServerFailure).message;
            }
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(errorMessage)),
            );
          }
          // if (state.successDeleteAccount != null) {
          //   context.read<LayoutCubit>().changePageIndex(0);
          //   context.router.replaceAll([UserTypeViewRoute()]);
          // }
          if (state.isErrorDeleteAccount != null) {
            String errorMessage = 'Delete failed';
            if (state.isErrorDeleteAccount is ServerFailure) {
              errorMessage =
                  (state.isErrorDeleteAccount as ServerFailure).message;
            }
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(errorMessage)),
            );
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: profileFormKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AppImages.images.salimIcon.userIcon
                      .image(width: 50, height: 50, fit: BoxFit.cover),
                  SizedBox(height: 10),
                  TextApp(
                    text: currentUser?.value.user?.name ?? '',
                    style: context.textStyle.copyWith(
                      fontSize: AppDimensions.fontSizeDefault,
                      fontWeight: FontWeightHelper.bold,
                    ),
                  ),
                  SizedBox(height: 16),
                  // Profile Form Container
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: context.color.whiteColor,
                      boxShadow: [
                        BoxShadow(
                          color: context.color.borderColor!,
                          spreadRadius: 1,
                        )
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              TextApp(
                                text: context.translate.personalInformation,
                                style: context.textStyle.copyWith(
                                  fontSize: AppDimensions.fontSizeDefault,
                                  fontWeight: FontWeightHelper.bold,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Divider(),
                          SizedBox(height: 16),
                          if (isEditing) ...[
                            // Editable form fields
                            CustomTextField(
                              titleText: context.translate.fullName,
                              hintText: context.translate.fullName,
                              prefixIcon: AppImages
                                  .images.salimIcon.profileCircle
                                  .svg(),
                              controller:
                                  context.read<AuthCubit>().fullNameController,
                              inputType: TextInputType.name,
                              onValidate: (value) {
                                if (value == null || value.isEmpty) {
                                  return context.translate.enterFullName;
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),

                            CustomTextField(
                              titleText: context.translate.surname,
                              hintText: context.translate.surname,
                              prefixIcon: AppImages
                                  .images.salimIcon.profileCircle
                                  .svg(),
                              controller:
                                  context.read<AuthCubit>().surnameController,
                              inputType: TextInputType.name,
                              onValidate: (value) {
                                if (value == null || value.isEmpty) {
                                  return context.translate.enterSurname;
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),

                            CustomTextField(
                              titleText: context.translate.email,
                              hintText: context.translate.email,
                              prefixIcon:
                                  AppImages.images.salimIcon.profileIcon.svg(),
                              controller:
                                  context.read<AuthCubit>().emailController,
                              inputType: TextInputType.emailAddress,
                              onValidate: (value) {
                                if (value == null || value.isEmpty) {
                                  return context.translate.enterEmail;
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),

                            CustomTextField(
                              titleText: context.translate.phoneNumber,
                              hintText: context.translate.phoneNumber,
                              prefixIcon: Icon(Icons.phone_android),
                              controller:
                                  context.read<AuthCubit>().phoneController,
                              inputType: TextInputType.phone,
                              onValidate: (value) {
                                if (value == null || value.isEmpty) {
                                  return context.translate.enterPhoneNumber;
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),

                            CustomTextField(
                              titleText: context.translate.password,
                              hintText: "********",
                              prefixIcon: AppImages.images.salimIcon.key.svg(),
                              controller:
                                  context.read<AuthCubit>().passwordController,
                              inputType: TextInputType.visiblePassword,
                              isPassword: true,
                              onValidate: (value) {
                                // Password is optional for updates
                                if (value != null &&
                                    value.isNotEmpty &&
                                    value.length < 8) {
                                  return context.translate.passwordTooShort;
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),

                            // Gender Selection
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextApp(
                                  text: context.translate.whatIsYourGender,
                                  style: context.textStyle.copyWith(
                                    fontWeight: FontWeight.bold,
                                    fontSize: AppDimensions.fontSizeDefault,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            context.read<AuthCubit>().gender =
                                                'male';
                                          });
                                        },
                                        child: Container(
                                          padding: EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                              color: context
                                                          .read<AuthCubit>()
                                                          .gender ==
                                                      'male'
                                                  ? context.color.primaryColor!
                                                  : context.color.grayColor!,
                                            ),
                                            color: context
                                                        .read<AuthCubit>()
                                                        .gender ==
                                                    'male'
                                                ? context.color.primaryColor!
                                                    .withOpacity(0.1)
                                                : Colors.transparent,
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              AppImages.images.salimIcon.male
                                                  .svg(),
                                              SizedBox(width: 8),
                                              TextApp(
                                                text: context.translate.male,
                                                style:
                                                    context.textStyle.copyWith(
                                                  fontWeight:
                                                      FontWeightHelper.regular,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 16),
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            context.read<AuthCubit>().gender =
                                                'female';
                                          });
                                        },
                                        child: Container(
                                          padding: EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                              color: context
                                                          .read<AuthCubit>()
                                                          .gender ==
                                                      'female'
                                                  ? context.color.primaryColor!
                                                  : context.color.grayColor!,
                                            ),
                                            color: context
                                                        .read<AuthCubit>()
                                                        .gender ==
                                                    'female'
                                                ? context.color.primaryColor!
                                                    .withOpacity(0.1)
                                                : Colors.transparent,
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              AppImages.images.salimIcon.womment
                                                  .svg(),
                                              SizedBox(width: 8),
                                              TextApp(
                                                text: context.translate.women,
                                                style:
                                                    context.textStyle.copyWith(
                                                  fontWeight:
                                                      FontWeightHelper.regular,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(height: 16),

                            // Age Selection
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextApp(
                                  text: "Age",
                                  style: context.textStyle.copyWith(
                                    fontWeight: FontWeight.bold,
                                    fontSize: AppDimensions.fontSizeDefault,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Container(
                                  width: double.infinity,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: context.color.grayColor!),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton<int>(
                                      value: context.read<AuthCubit>().age,
                                      hint: Text("Select Age"),
                                      items: List.generate(
                                              84, (index) => index + 16)
                                          .map((age) => DropdownMenuItem<int>(
                                                value: age,
                                                child: Text(age.toString()),
                                              ))
                                          .toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          context.read<AuthCubit>().age =
                                              value ?? 20;
                                        });
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 16),
                          ] else ...[
                            // Read-only view
                            _buildInfoRow(context.translate.username,
                                currentUser?.value.user?.name ?? ''),
                            _buildInfoRow(context.translate.email2,
                                currentUser?.value.user?.email ?? ''),
                            _buildInfoRow(context.translate.phoneNumber,
                                currentUser?.value.user?.phone ?? ''),
                            _buildInfoRow("Gender",
                                currentUser?.value.user?.gender ?? ''),
                            _buildInfoRow("Age",
                                currentUser?.value.user?.age?.toString() ?? ''),
                          ],
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  // Save Button (only show when editing)
                  if (isEditing) ...[
                    CustomButton(
                      isLoading: state.isLoadingUpdateProfile ?? false,
                      width: double.infinity,
                      onPressed: () {
                        if (profileFormKey.currentState?.validate() ?? false) {
                          context.read<AuthCubit>().updateProfile();
                        }
                      },
                      child: TextApp(
                        text: context.translate.saveChanges,
                        style: context.textStyleButton,
                      ),
                    ),
                    SizedBox(height: 16),
                  ],

                  // Actions Container
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: context.color.whiteColor,
                      boxShadow: [
                        BoxShadow(
                          color: context.color.borderColor!,
                          spreadRadius: 1,
                        )
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              TextApp(
                                text: context.translate.preferences,
                                style: context.textStyle.copyWith(
                                  fontSize: AppDimensions.fontSizeDefault,
                                  fontWeight: FontWeightHelper.bold,
                                ),
                              ),
                            ],
                          ),

                          SizedBox(height: 10),

                          Divider(),

                          SizedBox(height: 10),

                          // Delete Account Button
                          InkWell(
                            onTap: () =>
                                _showDeleteConfirmationDialog(context, state),
                            child: Row(
                              children: [
                                Icon(Icons.delete_forever,
                                    color: context.color.redColor),
                                SizedBox(width: 8),
                                TextApp(
                                  text: context.translate.deleteAccount,
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeDefault,
                                    color: context.color.redColor,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: 16),

                          // Logout Button
                          InkWell(
                            onTap: () async {
                              await SharedPref()
                                  .removePreference(PrefKeys.currentUser);
                              currentUser?.value.auth = false;
                              context
                                  .read<AuthCubit>()
                                  .resetRegisterControllers();
                              context.read<LayoutCubit>().changePageIndex(0);
                              context.router.replaceAll([UserTypeViewRoute()]);
                            },
                            child: Row(
                              children: [
                                Icon(Icons.logout,
                                    color: context.color.redColor),
                                SizedBox(width: 8),
                                TextApp(
                                  text: context.translate.logout,
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeDefault,
                                    color: context.color.redColor,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextApp(
            text: label,
            style: context.textStyle.copyWith(
              fontSize: AppDimensions.fontSizeDefault,
              fontWeight: FontWeightHelper.bold,
            ),
          ),
          Flexible(
            child: TextApp(
              text: value,
              style: context.textStyle.copyWith(
                fontSize: AppDimensions.fontSizeDefault,
                color: context.color.grayColor,
                fontWeight: FontWeightHelper.regular,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, AuthState state) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: TextApp(
            text: "حذف الحساب",
            style: context.textStyle.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: AppDimensions.fontSizeLarge,
            ),
          ),
          content: TextApp(
            text:
                "هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد أنك تريد حذف حسابك؟",
            style: context.textStyle.copyWith(
              fontSize: AppDimensions.fontSizeDefault,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: TextApp(
                text: "الغاء",
                style: context.textStyle.copyWith(
                  color: context.color.grayColor,
                ),
              ),
            ),
            TextButton(
              onPressed: state.isLoadingDeleteAccount == true
                  ? null
                  : () async {
                      Navigator.of(context).pop();
                      context.read<AuthCubit>().deleteAccount();
                      context.read<AuthCubit>().resetRegisterControllers();
                      context.read<LayoutCubit>().changePageIndex(0);
                      context.router.replaceAll([UserTypeViewRoute()]);
                    },
              child: state.isLoadingDeleteAccount == true
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : TextApp(
                      text: "تأكيد",
                      style: context.textStyle.copyWith(
                        color: context.color.redColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ],
        );
      },
    );
  }
}
