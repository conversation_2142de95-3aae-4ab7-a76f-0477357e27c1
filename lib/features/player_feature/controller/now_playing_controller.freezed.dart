// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'now_playing_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PlayerState {}

/// @nodoc
abstract class $PlayerStateCopyWith<$Res> {
  factory $PlayerStateCopyWith(
          PlayerState value, $Res Function(PlayerState) then) =
      _$PlayerStateCopyWithImpl<$Res, PlayerState>;
}

/// @nodoc
class _$PlayerStateCopyWithImpl<$Res, $Val extends PlayerState>
    implements $PlayerStateCopyWith<$Res> {
  _$PlayerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlayerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$$PlayerStateImplImplCopyWith<$Res> {
  factory _$$$PlayerStateImplImplCopyWith(_$$PlayerStateImplImpl value,
          $Res Function(_$$PlayerStateImplImpl) then) =
      __$$$PlayerStateImplImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$$PlayerStateImplImplCopyWithImpl<$Res>
    extends _$PlayerStateCopyWithImpl<$Res, _$$PlayerStateImplImpl>
    implements _$$$PlayerStateImplImplCopyWith<$Res> {
  __$$$PlayerStateImplImplCopyWithImpl(_$$PlayerStateImplImpl _value,
      $Res Function(_$$PlayerStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of PlayerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$$PlayerStateImplImpl implements _$PlayerStateImpl {
  _$$PlayerStateImplImpl();

  @override
  String toString() {
    return 'PlayerState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$$PlayerStateImplImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

abstract class _$PlayerStateImpl implements PlayerState {
  factory _$PlayerStateImpl() = _$$PlayerStateImplImpl;
}
