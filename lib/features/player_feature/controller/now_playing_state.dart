part of 'now_playing_controller.dart';

@freezed
class PlayerState with _$PlayerState {
  factory PlayerState(// {
      ///============================================================

//  /*  @Default(UserTypeEnum.user) UserTypeEnum userType,
//     ///============================================================
//     required bool? isLoadingLogin,
//     required Failure? isErrorLogin,
//     required UserModel? successLogin,
//
//     ///============================================================
//     required bool? isLoadingRegister,
//     required Failure? isErrorRegister,
//     required UserModel? successRegister,
//
//
//     ///============================================================
//     required bool? isLoadingVerificationCode,
//     required Failure? isErrorVerificationCode,
//     required MessageModel? successVerificationCode,
//
// */
      ///============================================================
      // @Default(true) bool isChecked,
      // }
      ) = _$PlayerStateImpl;

  factory PlayerState.initial() => PlayerState(
      // isErrorRegister: null,
      // isLoadingRegister: null,
      // successRegister: null,
      // userType: UserTypeEnum.user,
      // isLoadingLogin: null,
      // isErrorLogin: null,
      // successLogin: null,
      //
      // isLoadingVerificationCode: null,
      // isErrorVerificationCode: null,
      // successVerificationCode: null,
      );
}
