import 'package:clean_arc/core/presentation/animation/fade_animation.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/favorite_feature/controller/fav_manage_ment.dart';
import 'package:clean_arc/features/favorite_feature/domain/model/audio_model.dart';
import 'package:clean_arc/features/player_feature/controller/now_playing_controller.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/persentation/componant/play_button.dart';
import 'package:clean_arc/features/player_feature/persentation/componant/seekbar.dart';
import 'package:clean_arc/features/splash_feature/presentation/view/repitation_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:just_audio/just_audio.dart';

@RoutePage()
class SongView extends StatefulWidget {
  // NowPlayingController controller;

  SongView({
    Key? key,
    // required this.controller,
  }) : super(key: key);

  @override
  State<SongView> createState() => _SongViewState();
}

class _SongViewState extends State<SongView> {
  @override
  Widget build(BuildContext context) {
    // NowPlayingController controller=context.read<NowPlayingController>();
    NowPlayingController controller = context.read<NowPlayingController>();

    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
      child: Scaffold(
        appBar: AppBar(),
        body: StreamBuilder<SequenceState?>(
            stream: controller.player.sequenceStateStream,
            builder: (context, snapshot) {
              final state = snapshot.data;
              if (state?.sequence.isEmpty ?? true) return SizedBox();
              final metadata = state!.currentSource!.tag as SongModel;
              return Container(
                child: Container(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                    ),
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SlideAnimation(
                            Center(
                              child: RepetitionVideo(),
                            ),
                          ),
                          const SizedBox(
                            height: 30,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SlideAnimation(
                                  Text(
                                    metadata.song!,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      // color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          SlideAnimation(
                            StreamBuilder<PositionData>(
                              stream: controller.positionDataStream,
                              builder: (context, snapshot) {
                                final positionData = snapshot.data;
                                return SeekBar(
                                  duration:
                                      positionData?.duration ?? Duration.zero,
                                  position:
                                      positionData?.position ?? Duration.zero,
                                  bufferedPosition:
                                      positionData?.bufferedPosition ??
                                          Duration.zero,
                                  onChangeEnd: controller.player.seek,
                                );
                              },
                            ),
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          SlideAnimation(
                            Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white.withOpacity(.2),
                                ),
                              ),
                              child: Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      IconButton(
                                        icon: Icon(
                                          Icons.volume_up,
                                          // color:
                                          //     Colors.white.withOpacity(.6),
                                        ),
                                        onPressed: () {
                                          showSliderDialog(
                                            context: context,
                                            title: "Adjust volume",
                                            divisions: 10,
                                            min: 0.0,
                                            max: 1.0,
                                            value: controller.player.volume,
                                            stream:
                                                controller.player.volumeStream,
                                            onChanged:
                                                controller.player.setVolume,
                                          );
                                        },
                                      ),
                                      const SizedBox(
                                        width: 20,
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          if (controller.player.hasPrevious) {
                                            controller.player.seekToPrevious();
                                            controller.player.play();
                                          }
                                        },
                                        icon: const Icon(
                                          CupertinoIcons.backward_end,
                                          // color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 20,
                                      ),
                                      Container(
                                        decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: Colors.black,
                                            )),
                                        child: Center(
                                            child: PlayButton(
                                          player: controller.player,
                                        )),
                                      ),
                                      const SizedBox(
                                        width: 20,
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          if (controller.player.hasNext) {
                                            controller.player.seekToNext();
                                            controller.player.play();
                                          }
                                        },
                                        icon: const Icon(
                                          CupertinoIcons.forward_end,
                                          // color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 20,
                                      ),
                                      StreamBuilder<double>(
                                        stream: controller.player.speedStream,
                                        builder: (context, snapshot) =>
                                            IconButton(
                                          icon: Text(
                                            "${snapshot.data?.toStringAsFixed(1)}x",
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              // color: Colors.white
                                              //     .withOpacity(.6),
                                            ),
                                          ),
                                          onPressed: () {
                                            showSliderDialog(
                                              context: context,
                                              title: "Adjust speed",
                                              divisions: 10,
                                              min: 0.5,
                                              max: 1.5,
                                              value: controller.player.speed,
                                              stream:
                                                  controller.player.speedStream,
                                              onChanged:
                                                  controller.player.setSpeed,
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // StreamBuilder<bool>(
                                //   stream: controller.player
                                //       .shuffleModeEnabledStream,
                                //   builder: (context, snapshot) {
                                //     final shuffleModeEnabled =
                                //         snapshot.data ?? false;
                                //     return IconButton(
                                //       icon: shuffleModeEnabled
                                //           ? const Icon(Icons.shuffle,
                                //               color: Colors.orange)
                                //           : const Icon(Icons.shuffle,
                                //               color: Colors.grey),
                                //       onPressed: () async {
                                //         final enable = !shuffleModeEnabled;
                                //         if (enable) {
                                //           await controller.player
                                //               .shuffle();
                                //         }
                                //         await controller.player
                                //             .setShuffleModeEnabled(enable);
                                //       },
                                //     );
                                //   },
                                // ),
                                // ValueListenableBuilder<Box>(
                                //   valueListenable:
                                //   Hive.box("Favorite").listenable(),
                                //   builder: (context, box, child) {
                                //     bool isfavorite =
                                //     box.containsKey(metadata.song);
                                //     if (isfavorite) {
                                //       return IconButton(
                                //         onPressed: () {
                                //           final box = Hive.box("Favorite");
                                //           box.delete(metadata.song);
                                //         },
                                //         icon: Icon(
                                //           IconlyBold.heart,
                                //           color: context.color.redColor,
                                //         ),
                                //       );
                                //     } else {
                                //       return IconButton(
                                //         onPressed: () {
                                //           final box = Hive.box("Favorite");
                                //           box.put(metadata.song,
                                //               metadata.toJson());
                                //         },
                                //         icon: Icon(
                                //           IconlyLight.heart,
                                //           color: context.color.redColor,
                                //         ),
                                //       );
                                //     }
                                //   },
                                // ),
                                ValueListenableBuilder(
                                  valueListenable:
                                      Hive.box<AudioModel>('favorite_audios')
                                          .listenable(),
                                  builder: (context, Box<AudioModel> box, _) {
                                    final isFavorite =
                                        box.containsKey(metadata.url);

                                    return IconButton(
                                      onPressed: () async {
                                        if (isFavorite) {
                                          await FavoritesManager
                                              .removeAudioFromFavorites(
                                                  metadata.url ?? '');
                                        } else {
                                          await FavoritesManager
                                              .addAudioToFavorites(AudioModel(
                                            url: metadata.url ?? '',
                                            id: metadata.url ?? '',
                                            title: metadata.song ?? '',
                                          ));
                                        }
                                      },
                                      icon: Icon(
                                        isFavorite
                                            ? IconlyBold.heart
                                            : IconlyLight.heart,
                                        color: context.color.redColor,
                                      ),
                                    );
                                  },
                                ),

                                AppImages.images.salimIcon.share.svg()
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  context.translate.next,
                                  style: TextStyle(
                                    // color: Colors.white,
                                    fontSize: 18,
                                  ),
                                ),
                                StreamBuilder<LoopMode>(
                                  stream: controller.player.loopModeStream,
                                  builder: (context, snapshot) {
                                    final loopMode =
                                        snapshot.data ?? LoopMode.off;
                                    const icons = [
                                      Icon(Icons.repeat, color: Colors.grey),
                                      Icon(Icons.repeat, color: Colors.orange),
                                      Icon(Icons.repeat_one,
                                          color: Colors.orange),
                                    ];
                                    const cycleModes = [
                                      LoopMode.off,
                                      LoopMode.all,
                                      LoopMode.one,
                                    ];
                                    final index = cycleModes.indexOf(loopMode);
                                    return IconButton(
                                      icon: icons[index],
                                      onPressed: () {
                                        controller.player.setLoopMode(
                                            cycleModes[
                                                (cycleModes.indexOf(loopMode) +
                                                        1) %
                                                    cycleModes.length]);
                                      },
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          SlideAnimation(
                            Container(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 5.0),
                              child: StreamBuilder<SequenceState?>(
                                stream: controller.player.sequenceStateStream,
                                builder: (context, snapshot) {
                                  final state = snapshot.data;
                                  final sequence = state?.sequence ?? [];
                                  return Theme(
                                    data: ThemeData(
                                      canvasColor: Colors.transparent,
                                      shadowColor: Colors.transparent,
                                    ),
                                    child: ReorderableListView(
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      onReorder: (int oldIndex, int newIndex) {
                                        if (oldIndex < newIndex) newIndex--;
                                        controller.playlist
                                            .move(oldIndex, newIndex);
                                      },
                                      children: [
                                        for (var i = 0;
                                            i < sequence.length;
                                            i++)
                                          Padding(
                                            key: ValueKey(sequence[i]),
                                            padding: const EdgeInsets.all(4.0),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                child: Container(
                                                  // bgColor: Colors.black,
                                                  // blur: 10,
                                                  child: Dismissible(
                                                    key: ValueKey(sequence[i]),
                                                    background: Container(
                                                      color: Colors.redAccent,
                                                      alignment:
                                                          Alignment.centerRight,
                                                      child: const Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                                right: 8.0),
                                                        child: Icon(
                                                            Icons.delete,
                                                            color:
                                                                Colors.white),
                                                      ),
                                                    ),
                                                    onDismissed:
                                                        (dismissDirection) {
                                                      controller.playlist
                                                          .removeAt(i);
                                                    },
                                                    child: ListTile(
                                                      title: Text(
                                                        sequence[i].tag.song
                                                            as String,
                                                        maxLines: 2,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: TextStyle(
                                                          color: i ==
                                                                  state!
                                                                      .currentIndex
                                                              ? Colors.black
                                                              : context.color
                                                                  .grayColor,
                                                        ),
                                                      ),
                                                      subtitle: Text(
                                                        sequence[i].tag.artist
                                                            as String,
                                                        style: TextStyle(
                                                          color: context
                                                              .color.grayColor,
                                                        ),
                                                      ),
                                                      // trailing: const Icon(
                                                      //     Icons
                                                      //         .dehaze_rounded,
                                                      //     color:
                                                      //         Colors.white,
                                                      //     size: 20),
                                                      onTap: () {
                                                        controller.player.seek(
                                                            Duration.zero,
                                                            index: i);
                                                        controller.player
                                                            .play();
                                                      },
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }),
      ),
    );
  }
}
