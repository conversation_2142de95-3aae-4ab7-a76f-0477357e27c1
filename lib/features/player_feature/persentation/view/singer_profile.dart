import 'dart:ui';

import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/player_feature/controller/now_playing_controller.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/domain/services/songs_data.dart'
    as songs_data;
import 'package:clean_arc/features/player_feature/persentation/componant/global_player_footer.dart';
import 'package:clean_arc/features/player_feature/persentation/view/player_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ArtistProfile extends StatefulWidget {
  final String title;
  final List<SongModel> soung;
  final String? parentImage;

  const ArtistProfile({
    Key? key,
    required this.title,
    required this.soung,
    this.parentImage,
  }) : super(key: key);

  @override
  _ArtistProfileState createState() => _ArtistProfileState();
}

class _ArtistProfileState extends State<ArtistProfile> {
  @override
  void dispose() {
    // Stop audio when leaving the page
    final controller = context.read<NowPlayingController>();
    controller.player.stop();
    songs_data.currentSong = '';
    songs_data.currentPlayingUrl = '';
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    NowPlayingController controller = context.read<NowPlayingController>();
    final size = MediaQuery.of(context).size;
    // final List<SongModel> allsongs =
    //     songs.map((e) => SongModel.fromJson(e)).toList();

    List<SongModel> allResults = widget.soung;

    // allsongs.where((element) {
    //   return element.artist!.toLowerCase() == widget.name.toLowerCase();
    // }).toList();

    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
      child: Scaffold(
        body: Column(
          children: [
            Expanded(
              child: Container(
                width: size.width,
                color: Colors.white,
                child: Container(
                  // bgColor: Colors.black.withOpacity(.8),
                  // blur: 40,
                  child: Stack(
                    children: [
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        height: size.height,
                        child: CustomScrollView(
                          slivers: [
                            SliverAppBar(
                              backgroundColor: Colors.black.withOpacity(.8),
                              expandedHeight: 200,
                              pinned: true,
                              stretch: true,
                              flexibleSpace: FlexibleSpaceBar(

                                  // title: Text(
                                  //   widget.name,
                                  //   style: const TextStyle(
                                  //     fontWeight: FontWeight.bold,
                                  //   ),
                                  // ),
                                  centerTitle: true,
                                  background: Container(
                                    width: size.width,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      image: DecorationImage(
                                        fit: BoxFit.cover,
                                        image: widget.parentImage != null
                                            ? NetworkImage(getBaseUrl +
                                                widget.parentImage!)
                                            : AssetImage(AppImages
                                                .images
                                                .salimIcon
                                                .headerMusicIcon
                                                .path) as ImageProvider,
                                      ),
                                    ),
                                    height: 250,
                                    child: Center(
                                      child: BackdropFilter(
                                        filter: ImageFilter.blur(
                                            sigmaX: 10, sigmaY: 5),
                                        child: Container(
                                          height: 250,
                                          decoration: BoxDecoration(
                                            color: Colors.black38,
                                            image: DecorationImage(
                                              fit: BoxFit.cover,
                                              image: widget.parentImage != null
                                                  ? NetworkImage(getBaseUrl +
                                                      widget.parentImage!)
                                                  : AssetImage(AppImages
                                                      .images
                                                      .salimIcon
                                                      .headerMusicIcon
                                                      .path) as ImageProvider,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  )),
                            ),
                            const SliverToBoxAdapter(
                              child: SizedBox(height: 20),
                            ),
                            SliverToBoxAdapter(
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.0, vertical: 16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    TextApp(
                                        text: "${widget.title} ",
                                        style: context.textStyle.copyWith(
                                            fontSize: 20,
                                            fontWeight: FontWeightHelper.bold)),
                                    TextApp(
                                        text: "18 دقيقة",
                                        style: context.textStyle.copyWith(
                                            fontSize: 15,
                                            fontWeight:
                                                FontWeightHelper.regular,
                                            color: context.color.grayColor)),
                                    TextApp(
                                        text:
                                            "اختناق, جلطة قلبية, طلوع الروح, الطيران",
                                        style: context.textStyle.copyWith(
                                            fontSize: 15,
                                            fontWeight:
                                                FontWeightHelper.regular,
                                            color: context.color.grayColor)),
                                  ],
                                ),
                              ),
                            ),
                            SliverToBoxAdapter(
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.0, vertical: 5),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    TextApp(
                                        text: context.translate.recordings,
                                        style: context.textStyle.copyWith(
                                            fontSize: 20,
                                            color: context.color.primaryColor,
                                            fontWeight: FontWeightHelper.bold)),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    Container(
                                      width: context.width / 4.5,
                                      height: 2,
                                      color: context.color.primaryColor,
                                    )
                                  ],
                                ),
                              ),
                            ),
                            SliverList(
                              delegate: SliverChildBuilderDelegate(
                                (context, i) {
                                  final song = allResults[i];
                                  return InkWell(
                                    onTap: () async {
                                      // Stop current song completely and start new one from beginning
                                      controller.player.stop();
                                      songs_data.currentSong = song.song ?? '';
                                      songs_data.currentPlayingUrl =
                                          song.url ?? '';

                                      setState(() {});
                                      if (!controller.isSingerPlaylist) {
                                        controller.changetoSingerplaylist(
                                            allResults, i);
                                      }
                                      controller.player
                                          .seek(Duration.zero, index: i);
                                      controller.player.play();

                                      // Trigger footer update
                                      setState(() {});
                                      controller.notifyStateChange();
                                      Future.delayed(
                                          Duration(milliseconds: 100), () {
                                        if (mounted) setState(() {});
                                      });

                                      await Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => SongView(
                                              // controller:  controller,

                                              ),
                                        ),
                                      );

                                      setState(() {});
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                      ),
                                      child: Column(
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 6,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                Flexible(
                                                  child: Row(
                                                    children: [
                                                      InkWell(
                                                        onTap: () {
                                                          if (controller?.player
                                                                      .playing ==
                                                                  true &&
                                                              songs_data
                                                                      .currentPlayingUrl ==
                                                                  song.url) {
                                                            controller?.player
                                                                .pause();
                                                          } else {
                                                            // Stop current song completely and start new one from beginning
                                                            controller.player
                                                                .stop();
                                                            songs_data
                                                                    .currentSong =
                                                                song.song ?? '';
                                                            songs_data
                                                                    .currentPlayingUrl =
                                                                song.url ?? '';
                                                            if (!controller
                                                                .isSingerPlaylist) {
                                                              controller
                                                                  .changetoSingerplaylist(
                                                                      allResults,
                                                                      i);
                                                            }
                                                            controller.player
                                                                .seek(
                                                                    Duration
                                                                        .zero,
                                                                    index: i);
                                                            controller?.player
                                                                .play();
                                                          }
                                                          setState(() {});
                                                          // Trigger a rebuild of the footer
                                                          controller
                                                              .notifyStateChange();
                                                          Future.delayed(
                                                              Duration(
                                                                  milliseconds:
                                                                      100), () {
                                                            if (mounted)
                                                              setState(() {});
                                                          });
                                                        },
                                                        child: Column(
                                                          children: [
                                                            ((controller.player
                                                                            .playing ==
                                                                        true &&
                                                                    songs_data
                                                                            .currentPlayingUrl ==
                                                                        song
                                                                            .url))
                                                                ? AppImages
                                                                    .images
                                                                    .salimIcon
                                                                    .puse
                                                                    .image(
                                                                        height:
                                                                            40)
                                                                : AppImages
                                                                    .images
                                                                    .salimIcon
                                                                    .playIcon
                                                                    .svg(),
                                                          ],
                                                        ),
                                                      ),
                                                      // CachedNetworkImage(
                                                      //   imageUrl: song.album!,
                                                      //   height: 50,
                                                      //   width: 50,
                                                      //   fit: BoxFit.cover,
                                                      // ),

                                                      Flexible(
                                                        child: Container(
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(12.0),
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                TextApp(
                                                                  text: song
                                                                      .song!,
                                                                  maxLines: 1,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .clip,
                                                                  style: context
                                                                      .textStyle
                                                                      .copyWith(
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                                ),
                                                                const SizedBox(
                                                                  height: 5,
                                                                ),
                                                                Row(
                                                                  children: [
                                                                    TextApp(
                                                                        text: song
                                                                            .artist!,
                                                                        style: context.textStyle.copyWith(
                                                                            fontSize:
                                                                                15,
                                                                            fontWeight:
                                                                                FontWeightHelper.medium,
                                                                            color: context.color.grayColor)),
                                                                    // SizedBox(
                                                                    //   width: 10,
                                                                    // ),
                                                                    // Row(
                                                                    //   crossAxisAlignment:
                                                                    //       CrossAxisAlignment
                                                                    //           .start,
                                                                    //   children: [
                                                                    //     Icon(
                                                                    //       Icons
                                                                    //           .circle,
                                                                    //       size:
                                                                    //           10,
                                                                    //       color: context
                                                                    //           .color
                                                                    //           .grayColor,
                                                                    //     ),
                                                                    //     SizedBox(
                                                                    //       width:
                                                                    //           5,
                                                                    //     ),
                                                                    //     TextApp(
                                                                    //         text:
                                                                    //             '10 دقيقة '!,
                                                                    //         style: context.textStyle.copyWith(
                                                                    //             fontSize: 15,
                                                                    //             fontWeight: FontWeightHelper.medium,
                                                                    //             color: context.color.grayColor)),
                                                                    //   ],
                                                                    // ),

                                                                    // Text(
                                                                  ],
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Divider(
                                            color: Colors.white.withOpacity(.2),
                                            thickness: 1.5,
                                          )
                                        ],
                                      ),
                                    ),
                                  );
                                },
                                childCount: allResults.length > 5
                                    ? 5
                                    : allResults.length,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // AnimatedPositioned(
                      //   duration: const Duration(microseconds: 500),
                      //   bottom: 0,
                      //   left: 0,
                      //   right: 0,
                      //   height: 95,
                      //   child: InkWell(
                      //       onTap: () {
                      //         Navigator.push(
                      //           context,
                      //           MaterialPageRoute(
                      //             builder: (context) => SongView(
                      //                 // controller:  controller,
                      //                 ),
                      //           ),
                      //         );
                      //       },
                      //       child: SongBanner(
                      //         player: controller.player,
                      //         controller: controller,
                      //       )),
                      // ),
                    ],
                  ),
                ),
              ),
            ),
            // Add footer here - wrapped in BlocBuilder for real-time updates
            BlocBuilder<NowPlayingController, PlayerState>(
              builder: (context, state) {
                return const GlobalPlayerFooter();
              },
            ),
          ],
        ),
      ),
    );
  }
}
