import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/player_feature/controller/now_playing_controller.dart';
import 'package:clean_arc/features/player_feature/domain/services/songs_data.dart'
    as songs_data;
import 'package:clean_arc/features/player_feature/persentation/view/player_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GlobalPlayerFooter extends StatefulWidget {
  const GlobalPlayerFooter({Key? key}) : super(key: key);

  @override
  State<GlobalPlayerFooter> createState() => _GlobalPlayerFooterState();
}

class _GlobalPlayerFooterState extends State<GlobalPlayerFooter> {
  @override
  Widget build(BuildContext context) {
    final controller = context.read<NowPlayingController>();

    return StreamBuilder<bool>(
      stream: controller.player.playingStream,
      builder: (context, playingSnapshot) {
        return StreamBuilder<Duration>(
          stream: controller.player.positionStream,
          builder: (context, positionSnapshot) {
            // Only show footer if there's a current song playing
            if (songs_data.currentSong.isEmpty) {
              return SizedBox.shrink();
            }

        return Container(
          height: 80,
          decoration: BoxDecoration(
            color: context.color.whiteColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              children: [
                // Song info
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SongView(),
                        ),
                      );
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          songs_data.currentSong,
                          style: context.textStyle.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4),
                        StreamBuilder<Duration>(
                          stream: controller.player.positionStream,
                          builder: (context, snapshot) {
                            final position = snapshot.data ?? Duration.zero;
                            final duration =
                                controller.player.duration ?? Duration.zero;

                            return Row(
                              children: [
                                Text(
                                  _formatDuration(position),
                                  style: context.textStyle.copyWith(
                                    fontSize: 12,
                                    color: context.color.grayColor,
                                  ),
                                ),
                                Text(
                                  ' / ',
                                  style: context.textStyle.copyWith(
                                    fontSize: 12,
                                    color: context.color.grayColor,
                                  ),
                                ),
                                Text(
                                  _formatDuration(duration),
                                  style: context.textStyle.copyWith(
                                    fontSize: 12,
                                    color: context.color.grayColor,
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                // Progress slider
                Expanded(
                  flex: 2,
                  child: StreamBuilder<Duration>(
                    stream: controller.player.positionStream,
                    builder: (context, snapshot) {
                      final position = snapshot.data ?? Duration.zero;
                      final duration =
                          controller.player.duration ?? Duration.zero;

                      return Slider(
                        value: duration.inMilliseconds > 0
                            ? position.inMilliseconds / duration.inMilliseconds
                            : 0.0,
                        onChanged: (value) {
                          final newPosition = Duration(
                            milliseconds:
                                (value * duration.inMilliseconds).round(),
                          );
                          controller.player.seek(newPosition);
                        },
                        activeColor: context.color.primaryColor,
                        inactiveColor:
                            context.color.grayColor?.withOpacity(0.3),
                      );
                    },
                  ),
                ),

                // Play/Pause button
                IconButton(
                  onPressed: () {
                    if (controller.player.playing) {
                      controller.player.pause();
                    } else {
                      controller.player.play();
                    }
                    setState(() {});
                  },
                  icon: Icon(
                    (playingSnapshot.data ?? false) ? Icons.pause : Icons.play_arrow,
                    color: context.color.primaryColor,
                    size: 30,
                  ),
                ),
              ],
            ),
          ),
        );
          },
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
