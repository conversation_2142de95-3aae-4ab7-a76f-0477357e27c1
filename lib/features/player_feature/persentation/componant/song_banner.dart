import 'package:cached_network_image/cached_network_image.dart';
import 'package:clean_arc/app.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/player_feature/controller/now_playing_controller.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/persentation/componant/play_button.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';

String currentSong = '';

class SongBanner extends StatefulWidget {
  final AudioPlayer player;
  final NowPlayingController controller;

  const SongBanner({
    Key? key,
    required this.player,
    required this.controller,
  }) : super(key: key);

  @override
  State<SongBanner> createState() => _SongBannerState();
}

class _SongBannerState extends State<SongBanner> {
  @override
  Widget build(BuildContext context) {
    return Container(
      // bgColor: Colors.black12,
      // blur: 50,
      color: context.color.borderColor,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 5,
          horizontal: 20,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Flexible(
              child: Container(
                child: StreamBuilder<SequenceState?>(
                    stream: widget.player.sequenceStateStream,
                    builder: (context, snapshot) {
                      final state = snapshot.data;
                      if (state?.sequence.isEmpty ?? true) {
                        return const SizedBox();
                      }
                      final metadata = state!.currentSource!.tag as SongModel;
                      currentSong = metadata.song!;
                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Hero(
                          //   tag: metadata.album!,
                          //   child: CachedNetworkImage(
                          //     imageUrl: metadata.album!,
                          //     height: 50,
                          //     width: 50,
                          //     fit: BoxFit.cover,
                          //   ),
                          // ),
                          Flexible(
                            flex: 2,
                            child: Container(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      metadata.song!,
                                      maxLines: 1,
                                      overflow: TextOverflow.clip,
                                      style: const TextStyle(
                                         fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                    Text(
                                      metadata.artist!,
                                      style: TextStyle(
                                        color: context
                                            .color
                                            .grayColor,                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    }),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.black,
                  )),
              child: Center(child: PlayButton(player: widget.player)),
            ),
          ],
        ),
      ),
    );
  }
}
