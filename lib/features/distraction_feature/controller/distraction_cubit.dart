import 'dart:convert';
import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/features/distraction_feature/domain/repository/distraction_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/distraction_feature/domain/model/distraction_model.dart';

part 'distraction_cubit.freezed.dart';
part 'distraction_state.dart';
@injectable
class DistractionCubit extends Cubit<DistractionState> {
  DistractionCubit(this.repository) : super(DistractionState.initial());

  final DistractionRepository repository;

  Future<void> fetchDistraction() async {
    emit(state.copyWith(
      isLoadingDistraction: true,
      successDistraction: null,
      errorDistraction: null,
    ));
    final result = await repository.fetchDistraction();

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingDistraction: false,
          errorDistraction: failure,
          successDistraction: null,
        ));
      },
      (concerns) {
        print('distraction/getAlldistraction/getAlldistraction/getAll ${concerns}');
        emit(state.copyWith(
          isLoadingDistraction: false,
          errorDistraction: null,
          successDistraction: concerns,
        ));
      },
    );
  }

  Future<void> getDistractionById({
    required int id,
  }) async {
    emit(state.copyWith(
      isLoadingGetDistractionById: true,
      successGetDistractionById: null,
      errorGetDistractionById: null,
    ));

    final result = await repository.getDistractionById(id: id);

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingGetDistractionById: false,
          errorGetDistractionById: failure,
          successGetDistractionById: null,
        ));
      },
      (success) {
        emit(state.copyWith(
          isLoadingGetDistractionById: false,
          errorGetDistractionById: null,
          successGetDistractionById: success,
        ));
      },
    );
  }

  Future<void> getQuestionsByThoughtId({
    required int id,
  }) async {
    emit(state.copyWith(
      isLoadingQuestions: true,
      successQuestions: null,
      errorQuestions: null,
    ));

    final result = await repository.getQuestionsByThoughtId(id: id);

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingQuestions: false,
          errorQuestions: failure,
          successQuestions: null,
        ));
      },
      (success) {
        emit(state.copyWith(
          isLoadingQuestions: false,
          errorQuestions: null,
          successQuestions: success,
        ));
      },
    );
  }
}
