part of 'distraction_cubit.dart';

@freezed
class DistractionState with _$DistractionState {
  factory DistractionState({
    ///============================================================

    required bool? isLoadingDistraction,
    required Failure? errorDistraction,
    required BaseResponseModel<List<DistractionModel>>? successDistraction,

    ///============================================================
    required bool? isLoadingGetDistractionById,
    required Failure? errorGetDistractionById,
    required DistractionModel? successGetDistractionById,

    ///============================================================

    required bool? isLoadingQuestions, 
    required Failure? errorQuestions,
    required List<QuestionsModel>? successQuestions,
    @Default(true) bool isChecked,
  }) = _$DistractionStateImpl;

  factory DistractionState.initial() => DistractionState(
        errorDistraction: null,
        isLoadingDistraction: null,
        successDistraction: null,
        errorGetDistractionById: null,
        isLoadingGetDistractionById: null,
        successGetDistractionById: null,
        isLoadingQuestions: null,
        successQuestions: null,
        errorQuestions: null,
      );
}
