// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'distraction_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DistractionState {
  ///============================================================
  bool? get isLoadingDistraction => throw _privateConstructorUsedError;
  Failure? get errorDistraction => throw _privateConstructorUsedError;
  BaseResponseModel<List<DistractionModel>>? get successDistraction =>
      throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingGetDistractionById => throw _privateConstructorUsedError;
  Failure? get errorGetDistractionById => throw _privateConstructorUsedError;
  DistractionModel? get successGetDistractionById =>
      throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingQuestions => throw _privateConstructorUsedError;
  Failure? get errorQuestions => throw _privateConstructorUsedError;
  List<QuestionsModel>? get successQuestions =>
      throw _privateConstructorUsedError;
  bool get isChecked => throw _privateConstructorUsedError;

  /// Create a copy of DistractionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DistractionStateCopyWith<DistractionState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DistractionStateCopyWith<$Res> {
  factory $DistractionStateCopyWith(
          DistractionState value, $Res Function(DistractionState) then) =
      _$DistractionStateCopyWithImpl<$Res, DistractionState>;
  @useResult
  $Res call(
      {bool? isLoadingDistraction,
      Failure? errorDistraction,
      BaseResponseModel<List<DistractionModel>>? successDistraction,
      bool? isLoadingGetDistractionById,
      Failure? errorGetDistractionById,
      DistractionModel? successGetDistractionById,
      bool? isLoadingQuestions,
      Failure? errorQuestions,
      List<QuestionsModel>? successQuestions,
      bool isChecked});
}

/// @nodoc
class _$DistractionStateCopyWithImpl<$Res, $Val extends DistractionState>
    implements $DistractionStateCopyWith<$Res> {
  _$DistractionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DistractionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingDistraction = freezed,
    Object? errorDistraction = freezed,
    Object? successDistraction = freezed,
    Object? isLoadingGetDistractionById = freezed,
    Object? errorGetDistractionById = freezed,
    Object? successGetDistractionById = freezed,
    Object? isLoadingQuestions = freezed,
    Object? errorQuestions = freezed,
    Object? successQuestions = freezed,
    Object? isChecked = null,
  }) {
    return _then(_value.copyWith(
      isLoadingDistraction: freezed == isLoadingDistraction
          ? _value.isLoadingDistraction
          : isLoadingDistraction // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorDistraction: freezed == errorDistraction
          ? _value.errorDistraction
          : errorDistraction // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successDistraction: freezed == successDistraction
          ? _value.successDistraction
          : successDistraction // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<List<DistractionModel>>?,
      isLoadingGetDistractionById: freezed == isLoadingGetDistractionById
          ? _value.isLoadingGetDistractionById
          : isLoadingGetDistractionById // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetDistractionById: freezed == errorGetDistractionById
          ? _value.errorGetDistractionById
          : errorGetDistractionById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetDistractionById: freezed == successGetDistractionById
          ? _value.successGetDistractionById
          : successGetDistractionById // ignore: cast_nullable_to_non_nullable
              as DistractionModel?,
      isLoadingQuestions: freezed == isLoadingQuestions
          ? _value.isLoadingQuestions
          : isLoadingQuestions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorQuestions: freezed == errorQuestions
          ? _value.errorQuestions
          : errorQuestions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successQuestions: freezed == successQuestions
          ? _value.successQuestions
          : successQuestions // ignore: cast_nullable_to_non_nullable
              as List<QuestionsModel>?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$$DistractionStateImplImplCopyWith<$Res>
    implements $DistractionStateCopyWith<$Res> {
  factory _$$$DistractionStateImplImplCopyWith(
          _$$DistractionStateImplImpl value,
          $Res Function(_$$DistractionStateImplImpl) then) =
      __$$$DistractionStateImplImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isLoadingDistraction,
      Failure? errorDistraction,
      BaseResponseModel<List<DistractionModel>>? successDistraction,
      bool? isLoadingGetDistractionById,
      Failure? errorGetDistractionById,
      DistractionModel? successGetDistractionById,
      bool? isLoadingQuestions,
      Failure? errorQuestions,
      List<QuestionsModel>? successQuestions,
      bool isChecked});
}

/// @nodoc
class __$$$DistractionStateImplImplCopyWithImpl<$Res>
    extends _$DistractionStateCopyWithImpl<$Res, _$$DistractionStateImplImpl>
    implements _$$$DistractionStateImplImplCopyWith<$Res> {
  __$$$DistractionStateImplImplCopyWithImpl(_$$DistractionStateImplImpl _value,
      $Res Function(_$$DistractionStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of DistractionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingDistraction = freezed,
    Object? errorDistraction = freezed,
    Object? successDistraction = freezed,
    Object? isLoadingGetDistractionById = freezed,
    Object? errorGetDistractionById = freezed,
    Object? successGetDistractionById = freezed,
    Object? isLoadingQuestions = freezed,
    Object? errorQuestions = freezed,
    Object? successQuestions = freezed,
    Object? isChecked = null,
  }) {
    return _then(_$$DistractionStateImplImpl(
      isLoadingDistraction: freezed == isLoadingDistraction
          ? _value.isLoadingDistraction
          : isLoadingDistraction // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorDistraction: freezed == errorDistraction
          ? _value.errorDistraction
          : errorDistraction // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successDistraction: freezed == successDistraction
          ? _value.successDistraction
          : successDistraction // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<List<DistractionModel>>?,
      isLoadingGetDistractionById: freezed == isLoadingGetDistractionById
          ? _value.isLoadingGetDistractionById
          : isLoadingGetDistractionById // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetDistractionById: freezed == errorGetDistractionById
          ? _value.errorGetDistractionById
          : errorGetDistractionById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetDistractionById: freezed == successGetDistractionById
          ? _value.successGetDistractionById
          : successGetDistractionById // ignore: cast_nullable_to_non_nullable
              as DistractionModel?,
      isLoadingQuestions: freezed == isLoadingQuestions
          ? _value.isLoadingQuestions
          : isLoadingQuestions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorQuestions: freezed == errorQuestions
          ? _value.errorQuestions
          : errorQuestions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successQuestions: freezed == successQuestions
          ? _value._successQuestions
          : successQuestions // ignore: cast_nullable_to_non_nullable
              as List<QuestionsModel>?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$$DistractionStateImplImpl implements _$DistractionStateImpl {
  _$$DistractionStateImplImpl(
      {required this.isLoadingDistraction,
      required this.errorDistraction,
      required this.successDistraction,
      required this.isLoadingGetDistractionById,
      required this.errorGetDistractionById,
      required this.successGetDistractionById,
      required this.isLoadingQuestions,
      required this.errorQuestions,
      required final List<QuestionsModel>? successQuestions,
      this.isChecked = true})
      : _successQuestions = successQuestions;

  ///============================================================
  @override
  final bool? isLoadingDistraction;
  @override
  final Failure? errorDistraction;
  @override
  final BaseResponseModel<List<DistractionModel>>? successDistraction;

  ///============================================================
  @override
  final bool? isLoadingGetDistractionById;
  @override
  final Failure? errorGetDistractionById;
  @override
  final DistractionModel? successGetDistractionById;

  ///============================================================
  @override
  final bool? isLoadingQuestions;
  @override
  final Failure? errorQuestions;
  final List<QuestionsModel>? _successQuestions;
  @override
  List<QuestionsModel>? get successQuestions {
    final value = _successQuestions;
    if (value == null) return null;
    if (_successQuestions is EqualUnmodifiableListView)
      return _successQuestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool isChecked;

  @override
  String toString() {
    return 'DistractionState(isLoadingDistraction: $isLoadingDistraction, errorDistraction: $errorDistraction, successDistraction: $successDistraction, isLoadingGetDistractionById: $isLoadingGetDistractionById, errorGetDistractionById: $errorGetDistractionById, successGetDistractionById: $successGetDistractionById, isLoadingQuestions: $isLoadingQuestions, errorQuestions: $errorQuestions, successQuestions: $successQuestions, isChecked: $isChecked)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$$DistractionStateImplImpl &&
            (identical(other.isLoadingDistraction, isLoadingDistraction) ||
                other.isLoadingDistraction == isLoadingDistraction) &&
            (identical(other.errorDistraction, errorDistraction) ||
                other.errorDistraction == errorDistraction) &&
            (identical(other.successDistraction, successDistraction) ||
                other.successDistraction == successDistraction) &&
            (identical(other.isLoadingGetDistractionById,
                    isLoadingGetDistractionById) ||
                other.isLoadingGetDistractionById ==
                    isLoadingGetDistractionById) &&
            (identical(
                    other.errorGetDistractionById, errorGetDistractionById) ||
                other.errorGetDistractionById == errorGetDistractionById) &&
            (identical(other.successGetDistractionById,
                    successGetDistractionById) ||
                other.successGetDistractionById == successGetDistractionById) &&
            (identical(other.isLoadingQuestions, isLoadingQuestions) ||
                other.isLoadingQuestions == isLoadingQuestions) &&
            (identical(other.errorQuestions, errorQuestions) ||
                other.errorQuestions == errorQuestions) &&
            const DeepCollectionEquality()
                .equals(other._successQuestions, _successQuestions) &&
            (identical(other.isChecked, isChecked) ||
                other.isChecked == isChecked));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoadingDistraction,
      errorDistraction,
      successDistraction,
      isLoadingGetDistractionById,
      errorGetDistractionById,
      successGetDistractionById,
      isLoadingQuestions,
      errorQuestions,
      const DeepCollectionEquality().hash(_successQuestions),
      isChecked);

  /// Create a copy of DistractionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$$DistractionStateImplImplCopyWith<_$$DistractionStateImplImpl>
      get copyWith => __$$$DistractionStateImplImplCopyWithImpl<
          _$$DistractionStateImplImpl>(this, _$identity);
}

abstract class _$DistractionStateImpl implements DistractionState {
  factory _$DistractionStateImpl(
      {required final bool? isLoadingDistraction,
      required final Failure? errorDistraction,
      required final BaseResponseModel<List<DistractionModel>>?
          successDistraction,
      required final bool? isLoadingGetDistractionById,
      required final Failure? errorGetDistractionById,
      required final DistractionModel? successGetDistractionById,
      required final bool? isLoadingQuestions,
      required final Failure? errorQuestions,
      required final List<QuestionsModel>? successQuestions,
      final bool isChecked}) = _$$DistractionStateImplImpl;

  ///============================================================
  @override
  bool? get isLoadingDistraction;
  @override
  Failure? get errorDistraction;
  @override
  BaseResponseModel<List<DistractionModel>>? get successDistraction;

  ///============================================================
  @override
  bool? get isLoadingGetDistractionById;
  @override
  Failure? get errorGetDistractionById;
  @override
  DistractionModel? get successGetDistractionById;

  ///============================================================
  @override
  bool? get isLoadingQuestions;
  @override
  Failure? get errorQuestions;
  @override
  List<QuestionsModel>? get successQuestions;
  @override
  bool get isChecked;

  /// Create a copy of DistractionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$$DistractionStateImplImplCopyWith<_$$DistractionStateImplImpl>
      get copyWith => throw _privateConstructorUsedError;
}
