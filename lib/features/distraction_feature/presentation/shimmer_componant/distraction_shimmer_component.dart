import 'package:cached_network_image/cached_network_image.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/presentation/widget/shimmer/shimar_widget.dart';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DistractionShimmerComponent extends StatefulWidget {
  const DistractionShimmerComponent({super.key});

  @override
  State<DistractionShimmerComponent> createState() =>
      _DistractionShimmerComponentState();
}

class _DistractionShimmerComponentState
    extends State<DistractionShimmerComponent> {
  @override
  void initState() {
    context.read<ConcernsCubit>().fetchConcerns();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            CustomShimmerWidget(
              child: TextApp(
                text: context.translate.chooseConcernsToEliminate,
                style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeLarge,
                  fontWeight: FontWeightHelper.bold,
                ),
              ),
            ),
          ],
        ),
        CustomShimmerWidget(
          child: SizedBox(
            height: 16,
          ),
        ),
        CustomShimmerWidget(
          width: MediaQuery.of(context).size.width - 50,
          child: Container(
            height: 150,
            color: context.color.grayColor!.withOpacity(.5),
            width: MediaQuery.of(context).size.width - 50,
          ),
        ),
      ],
    );
  }
}
