import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/distraction_feature/controller/distraction_cubit.dart';
import 'package:clean_arc/features/distraction_feature/domain/model/distraction_model.dart';
import 'package:clean_arc/features/distraction_feature/presentation/shimmer_componant/distraction_shimmer_component.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/persentation/view/singer_profile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DistractionComponent extends StatefulWidget {
  const DistractionComponent({super.key});

  @override
  State<DistractionComponent> createState() => _DistractionComponentState();
}

class _DistractionComponentState extends State<DistractionComponent> {
  @override
  void initState() {
    context.read<DistractionCubit>().fetchDistraction();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DistractionCubit, DistractionState>(
        builder: (context, state) {
      if (state.errorDistraction != null) {
        CustomErrorWidget(
          failure: state.errorDistraction,
          onPressed: () {
            context.read<DistractionCubit>().fetchDistraction();
          },
        );
      } else if (state.isLoadingDistraction == true) {
        return DistractionShimmerComponent();
      } else if (state.successDistraction != null) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                TextApp(
                  text: state.successDistraction?.data?.first.description ?? '',
                  style: context.textStyle.copyWith(
                    fontSize: AppDimensions.fontSizeLarge,
                    fontWeight: FontWeightHelper.bold,
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
              ],
            ),
            SizedBox(
              height: 0,
            ),
            state.successDistraction?.data?.length == 1
                ? InkWell(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ArtistProfile(
                                title: state.successDistraction!.data!.first!
                                        .title ??
                                    '',
                                parentImage:
                                    state.successDistraction!.data![0].image,
                                soung: state
                                    .successDistraction!.data![0].audioUrl
                                    .map(
                                      (e) => SongModel(
                                          album: '',
                                          artist: state.successDistraction!
                                              .data![0]!.title,
                                          song: e.title,
                                          url:
                                              getBaseUrl + (e?.audioUrl ?? '')),
                                    )
                                    .toList()),
                          ));
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        image: DecorationImage(
                            fit: BoxFit.fill,
                            image: NetworkImage(getBaseUrl +
                                state.successDistraction!.data![0].image!)
                            // AssetImage('assets/images/demo/Background (4).png')

                            ),
                      ),
                      child: Container(
                        height: 150,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          // color: context.color.textColor?.withOpacity(.5)
                        ),
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            TextApp(
                              text:
                                  state.successDistraction?.data?.first.title ??
                                      '',
                              style: context.textStyle.copyWith(
                                fontSize: AppDimensions.fontSizeLarge,
                                color: context.color.whiteColor,
                                fontWeight: FontWeightHelper.bold,
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            CustomButton(
                                width: 110,
                                height: 40,
                                onPressed: () {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => ArtistProfile(
                                            title: state.successDistraction!
                                                    .data![0]!.title ??
                                                '',
                                            parentImage: state
                                                .successDistraction!
                                                .data![0]
                                                .image,
                                            soung: state.successDistraction!
                                                .data![0].audioUrl
                                                .map(
                                                  (e) => SongModel(
                                                      album: '',
                                                      artist: state
                                                          .successDistraction!
                                                          .data![0]!
                                                          .title,
                                                      song: e.title,
                                                      url: getBaseUrl +
                                                          (e?.audioUrl ?? '')),
                                                )
                                                .toList()),
                                      ));
                                },
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    TextApp(
                                      text: context.translate.startNow,
                                      style: context.textStyleButton,
                                    ),
                                    Icon(
                                      Icons.arrow_forward,
                                      color: context.color.whiteColor,
                                    ),
                                  ],
                                ))
                          ],
                        ),
                      ),
                    ),
                  )
                : Container(
                    height: 150,
                    child: ListView.separated(
                      separatorBuilder: (context, index) => SizedBox(
                        width: 5,
                      ),
                      scrollDirection: Axis.horizontal,
                      itemCount: state.successDistraction?.data?.length ?? 0,
                      itemBuilder: (context, index) {
                        DistractionModel? meditation =
                            state.successDistraction?.data?[index];
                        return InkWell(
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ArtistProfile(
                                      title: meditation?.title ?? '',
                                      parentImage: meditation?.image,
                                      soung: (meditation?.audioUrl ?? [])
                                          .map(
                                            (e) => SongModel(
                                                album: '',
                                                artist: meditation?.title,
                                                song: e.title,
                                                url: getBaseUrl +
                                                    (e?.audioUrl ?? '')),
                                          )
                                          .toList()),
                                ));
                          },
                          child: Stack(
                            children: [
                              Center(
                                child: Container(
                                  height: 150,
                                  width: 130,
                                  child: Center(
                                      child:
                                          // Image.asset(medحبيبي يامنش بستخدمه مع اندرويد استوديو ومشاء الله كويس itation?.image ?? '')
                                          //     .cornerRadiusWithClipRRect(10),
                                          CustomCachedNetworkImage(
                                              borderRadius: 10,
                                              imageUrl:
                                                  meditation?.image ?? '')),
                                ),
                              ),
                              Center(
                                child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: context.color.textColor
                                          ?.withOpacity(.5)),
                                  height: 130,
                                  width: 130,
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Center(
                                        child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: TextApp(
                                            text: meditation?.title ?? '',
                                            style: context.textStyle.copyWith(
                                              fontSize:
                                                  AppDimensions.fontSizeDefault,
                                              color: context.color.whiteColor,
                                              fontWeight: FontWeightHelper.bold,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    )),
                                  ),
                                ),
                              )
                            ],
                          ),
                        );
                      },
                    ),
                  ),
          ],
        );
      }
      return SizedBox();
    });
  }
}
