class DistractionModel {
  DistractionModel({
    required this.id,
    required this.title,
    required this.description,
    required this.image,
    required this.type,
    required this.isActive,
    required this.audioUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final String? title;
  final String? description;
  final String? image;
  final String? type;
  final num? isActive;
  final List<AudioUrl> audioUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory DistractionModel.fromJson(Map<String, dynamic> json){
    return DistractionModel(
      id: json["id"],
      title: json["title"],
      description: json["description"],
      image: json["image"],
      type: json["type"],
      isActive: json["is_active"],
      audioUrl: json["audio_url"] == null ? [] : List<AudioUrl>.from(json["audio_url"]!.map((x) => AudioUrl.fromJson(x))),
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "title": title,
    "description": description,
    "image": image,
    "type": type,
    "is_active": isActive,
    "audio_url": audioUrl.map((x) => x?.toJson()).toList(),
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };

}

class AudioUrl {
  AudioUrl({
    required this.id,
    required this.distractionId,
    required this.title,
    required this.type,
    required this.audioUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final int? distractionId;
  final String? title;
  final String? type;
  final String? audioUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory AudioUrl.fromJson(Map<String, dynamic> json){
    return AudioUrl(
      id: json["id"],
      distractionId: json["distraction_id"],
      title: json["title"],
      type: json["type"],
      audioUrl: json["audio_url"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "distraction_id": distractionId,
    "title": title,
    "type": type,
    "audio_url": audioUrl,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };

}
