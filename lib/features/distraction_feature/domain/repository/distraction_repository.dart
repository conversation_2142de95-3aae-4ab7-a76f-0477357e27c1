import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/data/repositories/base_repository_impl.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/domain/services/remote/concerns_services.dart';
import 'package:clean_arc/features/distraction_feature/domain/model/distraction_model.dart';
import 'package:clean_arc/features/distraction_feature/domain/services/remote/distraction_services.dart';
 import 'package:clean_arc/features/meditation_feature/domain/model/meditations_model.dart';
import 'package:clean_arc/features/meditation_feature/domain/services/remote/meditations_services.dart';
 import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
abstract class DistractionRepository {
  Future<Either<Failure,BaseResponseModel<List<DistractionModel>>>> fetchDistraction();
  Future<Either<Failure, DistractionModel?>> getDistractionById({
    required int id,
  });


  // Repository
  Future<Either<Failure, List<QuestionsModel>>> getQuestionsByThoughtId({
    required int id,
  });

}

@LazySingleton(as: DistractionRepository)
class DistractionRepositoryImpl extends BaseRepositoryImpl
    implements DistractionRepository {
  final DistractionServices _services;

  DistractionRepositoryImpl(super.logger, this._services);

  // Repository Implementation for Distraction
  Future<Either<Failure, BaseResponseModel<List<DistractionModel>>>> fetchDistraction() {
    return request(() async {
      final result = await _services.fetchDistraction();
      return Right(result);
    });
  }

  Future<Either<Failure, DistractionModel>> getDistractionById({
    required int id,
  }) {
    return request(() async {
      final result = await _services.getDistractionById(id: id);
      return Right(result);
    });
  }


  // Repository Implementation
  Future<Either<Failure, List<QuestionsModel>>> getQuestionsByThoughtId({
    required int id,
  }) {
    return request(() async {
      final result = await _services.getQuestionsByThoughtId(id: id);
      return Right(result);
    });
  }
}
