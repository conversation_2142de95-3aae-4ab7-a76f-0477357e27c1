import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/distraction_feature/domain/model/distraction_model.dart';
import 'package:clean_arc/features/meditation_feature/domain/model/meditations_model.dart';
import 'package:injectable/injectable.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:retrofit/retrofit.dart';
part 'distraction_services.g.dart';

@LazySingleton()
@RestApi(baseUrl: '')
abstract class DistractionServices {
  @factoryMethod
  factory DistractionServices(Dio dio, Configuration configuration) {
    return _DistractionServices(dio, baseUrl: configuration.getApiUrl);
  }

// Services Method to fetch Distraction
//   @GET('distraction/getAll')
  @GET('distraction')
  Future<BaseResponseModel<List<DistractionModel>>> fetchDistraction();

  @GET('concerns/{id}')
  Future<DistractionModel> getDistractionById({
    @Path('id') required int id,
  });


  // Services Method
  @GET('questions/thought/{id}')
  Future<List<QuestionsModel>> getQuestionsByThoughtId({
    @Path('id') required int id,
  });




}
