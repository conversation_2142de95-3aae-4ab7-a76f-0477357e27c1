import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/data/repositories/base_repository_impl.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/domain/services/remote/concerns_services.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/treatment_model.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/sub_treatment_model.dart';
import 'package:clean_arc/features/treatment_feature/domain/services/remote/treatment_services.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

abstract class TreatmentRepository {
// Repository method for Treatment
  Future<Either<Failure, List<TreatmentModel>>> fetchTreatment();

  Future<Either<Failure,  BaseResponseModel<List<TreatmentVideoModel>>>> getTreatmentVideoById({
    required int id,
  });


  // Repository
  Future<Either<Failure,  BaseResponseModel<List<TreatmentVideoModel>>>> getQuestionsByThoughtId({
    required int id,
  });

}

@LazySingleton(as: TreatmentRepository)
class TreatmentRepositoryImpl extends BaseRepositoryImpl
    implements TreatmentRepository {
  final TreatmentServices _services;

  TreatmentRepositoryImpl(super.logger, this._services);

  // Repository Implementation for Treatment
  Future<Either<Failure, List<TreatmentModel>>> fetchTreatment() {
    return request(() async {
      final result = await _services.fetchTreatment();
      return Right(result);
    });
  }

  Future<Either<Failure, BaseResponseModel<List<TreatmentVideoModel>>>> getTreatmentVideoById({
    required int id,
  }) {
    return request(() async {
      final result = await _services.getTreatmentVideoById(id: id);
      return Right(result);
    });
  }


  // Repository Implementation
  Future<Either<Failure,  BaseResponseModel<List<TreatmentVideoModel>>>> getQuestionsByThoughtId({
    required int id,
  }) {
    return request(() async {
      final result = await _services.getTreatmentVideoById(id: id);
      return Right(result);
    });
  }
}
