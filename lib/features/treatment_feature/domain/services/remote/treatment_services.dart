import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/treatment_model.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/sub_treatment_model.dart';
import 'package:injectable/injectable.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:retrofit/retrofit.dart';

part 'treatment_services.g.dart';

@LazySingleton()
@RestApi(baseUrl: '')
abstract class TreatmentServices {
  @factoryMethod
  factory TreatmentServices(Dio dio, Configuration configuration) {
    return _TreatmentServices(dio, baseUrl: configuration.getApiUrl);
  }

// Services Method to fetch Treatment
  @GET('videos')
  Future<List<TreatmentModel>> fetchTreatment();

  @GET('videos/treatment-video/{id}')
  Future<TreatmentVideoModel> getTreatmentVideoById1({
    @Path('id') required int id,
  });


  // Services Method
  @GET('videos/treatment-video/{id}')
  Future<BaseResponseModel<List<TreatmentVideoModel>>> getTreatmentVideoById({
    @Path('id') required int id,
  });

}
