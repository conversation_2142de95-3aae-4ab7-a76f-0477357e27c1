class TreatmentVideoModel {
  TreatmentVideoModel({
    required this.id,
    required this.text,
    required this.description,
    required this.image,
    required this.isActive,
    required this.videoId,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final String? text;
  final dynamic description;
  final String? image;
  final int? isActive;
  final int? videoId;
  final String? type;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory TreatmentVideoModel.fromJson(Map<String, dynamic> json) {
    return TreatmentVideoModel(
      id: json["id"],
      text: json["text"],
      description: json["description"],
      image: json["image"],
      isActive: json["is_active"],
      videoId: json["video_id"],
      type: json["type"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "text": text,
        "description": description,
        "image": image,
        "is_active": isActive,
        "video_id": videoId,
        "type": type,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
