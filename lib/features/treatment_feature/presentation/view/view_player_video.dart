import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerView extends StatefulWidget {
  final String title;
  final String? url;

  VideoPlayerView({super.key, required this.title, this.url});

  @override
  State<VideoPlayerView> createState() => _VideoPlayerViewState();
}

class _VideoPlayerViewState extends State<VideoPlayerView> {
  VideoPlayerController? _videoPlayerController;

  Future<void> requestStoragePermission() async {
    final status = await Permission.storage.request();

    if (status.isGranted) {
      print("Permission Granted");
    } else if (status.isDenied) {
      print("Permission Denied");
    } else if (status.isPermanentlyDenied) {
      print("Permission Permanently Denied");
      openAppSettings();
    }
  }

  @override
  void initState() {
    super.initState();
    requestStoragePermission();

    _videoPlayerController = VideoPlayerController.network(widget.url!)
      ..initialize().then((_) {
        setState(() {});
        _videoPlayerController!.play();
      });
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        context,
        title: widget.title,
        showNotification: false,
      ),
      body: _videoPlayerController != null && _videoPlayerController!.value.isInitialized
          ? CustomControlsWidget(controller: _videoPlayerController!)
          : Center(child: CircularProgressIndicator()),
    );
  }
}

class CustomControlsWidget extends StatefulWidget {
  final VideoPlayerController controller;

  const CustomControlsWidget({Key? key, required this.controller}) : super(key: key);

  @override
  _CustomControlsWidgetState createState() => _CustomControlsWidgetState();
}

class _CustomControlsWidgetState extends State<CustomControlsWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        AspectRatio(
          aspectRatio: widget.controller.value.aspectRatio,
          child: VideoPlayer(widget.controller),
        ),
        Positioned(
          bottom: 20,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: Icon(Icons.fast_rewind, color: Colors.white),
                onPressed: () {
                  final currentPosition = widget.controller.value.position;
                  final rewindPosition = currentPosition - Duration(seconds: 5);
                  widget.controller.seekTo(rewindPosition > Duration.zero
                      ? rewindPosition
                      : Duration.zero);
                },
              ),
              IconButton(
                icon: Icon(
                  widget.controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    if (widget.controller.value.isPlaying) {
                      widget.controller.pause();
                    } else {
                      widget.controller.play();
                    }
                  });
                },
              ),
              IconButton(
                icon: Icon(Icons.fast_forward, color: Colors.white),
                onPressed: () {
                  final currentPosition = widget.controller.value.position;
                  final forwardPosition = currentPosition + Duration(seconds: 5);
                  if (forwardPosition < widget.controller.value.duration) {
                    widget.controller.seekTo(forwardPosition);
                  } else {
                    widget.controller.seekTo(widget.controller.value.duration);
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
