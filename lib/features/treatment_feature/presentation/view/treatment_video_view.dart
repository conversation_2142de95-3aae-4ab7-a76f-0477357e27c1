import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/presentation/widget/custom_loading.dart';
import 'package:clean_arc/core/presentation/widget/empty_widget.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/treatment_feature/controller/treatment_cubit.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/sub_treatment_model.dart';
import 'package:clean_arc/features/treatment_feature/presentation/view/treatment_video_items_view.dart';
import 'package:clean_arc/features/treatment_feature/presentation/view/view_player_video.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail_imageview/video_thumbnail_imageview.dart';

@RoutePage()
class TreatmentVideoView extends StatefulWidget {
  final int id;
  final String? parentImage;

  const TreatmentVideoView({super.key, required this.id, this.parentImage});

  @override
  State<TreatmentVideoView> createState() => _TreatmentVideoViewState();
}

class _TreatmentVideoViewState extends State<TreatmentVideoView> {
  @override
  void initState() {
    context.read<TreatmentCubit>().getTreatmentVideoById(id: widget.id);

    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    TreatmentCubit treatmentCubit = context.watch<TreatmentCubit>();
    List images = [
      AppImages.images.demo.c1.path,
      AppImages.images.demo.c2.path,
      AppImages.images.demo.c3.path,
      AppImages.images.demo.c4.path,
      AppImages.images.demo.c5.path,
      AppImages.images.demo.c6.path,
    ];
    return Scaffold(
        // appBar:

        appBar: CustomAppBar(context,
            title: 'العلاج النفسي الإلكتروني',
            centerTitle: true,
            showNotification: false),
        // AppBar(
        //   title: TextApp(text: 'العلاج النفسي الإلكتروني'),
        // ),
        body: BlocBuilder<TreatmentCubit, TreatmentState>(
          builder: (context, state) {
            if (treatmentCubit.state.isLoadingGetTreatmentVideoById == true) {
              return Center(
                child: CustomLoading(),
              );
            } else if (treatmentCubit.state.errorGetTreatmentVideoById !=
                null) {
              return Center(
                  child: CustomErrorWidget(
                failure: treatmentCubit.state.errorGetTreatmentVideoById,
                onPressed: () {
                  treatmentCubit.getTreatmentVideoById(id: widget.id);
                },
              ));
            } else if (treatmentCubit.state.successGetTreatmentVideoById !=
                null) {
              List<TreatmentVideoModel>? treatments =
                  treatmentCubit.state.successGetTreatmentVideoById?.data;

              if (treatments?.isEmpty ?? true) {
                return Center(
                  child: CustomEmptyWidget(title: 'لا يوجد علاجات'),
                );
              }

              return ListView.separated(
                itemCount: treatments?.length ?? 0,
                separatorBuilder: (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Divider(),
                ),
                itemBuilder: (context, index) {
                  return InkWell(
                    onTap: () async {
                      // context.router.push(
                      //   TreatmentVideoItemsViewRoute(
                      //     treatmentId: widget.id,
                      //     treatmentModel: treatments![index],
                      //   ),
                      // );
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => TreatmentVideoItemsView(
                                    treatmentId: widget.id,
                                    treatmentModel: treatments![index],
                                    parentImage: widget.parentImage ?? '',
                                  )));
                      //     VideoPlayerView(
                      //   url:
                      //       getBaseUrl + (treatments?[index].image ?? ''),
                      //   title: treatments?[index].text ?? '',
                      // ),
                      // ));
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CircleAvatar(
                            radius: 8,
                            backgroundColor: context.color.primaryColor,
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                TextApp(
                                  text: treatments?[index].text ?? '',
                                  style: TextStyle(
                                    fontSize: 15,
                                    // color: context.color,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                                if (treatments?[index].description != null &&
                                    (treatments?[index]
                                            .description!
                                            .isNotEmpty ??
                                        false))
                                  TextApp(
                                    text: treatments?[index].description ?? '',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeightHelper.bold,
                                    ),
                                  ),
                              ],
                            ),
                          ),

                          SizedBox(width: 10),

                          // arrow icon
                          Icon(
                            Icons.arrow_forward_ios,
                            color: context.color.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            }
            return SizedBox();
          },
        ));
  }
}

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;

  VideoPlayerWidget({required this.videoUrl});

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.videoUrl)
      ..initialize().then((_) {
        setState(() {}); // Refresh UI after initialization
      })
      ..setLooping(true)
      ..play(); // Auto-play the video
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print(widget.videoUrl ?? '');

    return _controller.value.isInitialized
        ? AspectRatio(
            aspectRatio: _controller.value.aspectRatio,
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                VideoPlayer(_controller),
                _ControlsOverlay(controller: _controller),
                VideoProgressIndicator(_controller, allowScrubbing: true),
              ],
            ),
          )
        : CircularProgressIndicator();
  }
}

class _ControlsOverlay extends StatelessWidget {
  final VideoPlayerController controller;

  _ControlsOverlay({required this.controller});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        controller.value.isPlaying ? controller.pause() : controller.play();
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (!controller.value.isPlaying)
            Icon(
              Icons.play_circle_filled,
              size: 80.0,
              color: Colors.white70,
            ),
        ],
      ),
    );
  }
}
