import 'dart:developer';
import 'dart:ui';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/treatment_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dio/dio.dart';

import '../../domain/model/sub_treatment_model.dart';

@RoutePage()
class TreatmentVideoItemsView extends StatefulWidget {
  final int treatmentId;
  final TreatmentVideoModel treatmentModel;
  final String parentImage;

  const TreatmentVideoItemsView({
    Key? key,
    required this.treatmentId,
    required this.parentImage,
    required this.treatmentModel,
  }) : super(key: key);

  @override
  State<TreatmentVideoItemsView> createState() =>
      _TreatmentVideoItemsViewState();
}

class _TreatmentVideoItemsViewState extends State<TreatmentVideoItemsView> {
  List<TreatmentVideoItem> videoItems = [];
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _fetchVideoItems();
  }

  Future<void> _fetchVideoItems() async {
    try {
      final dio = Dio();
      final response = await dio.get(
          'https://api.arab-cbt.com/videos/treatment-item/item/${widget.treatmentModel.id}');
      log('URLL ${'https://api.arab-cbt.com/videos/treatment-item/item/${widget.treatmentModel.id}'}');

      if (response.statusCode == 200) {
        final data = response.data['data'] as List;
        setState(() {
          videoItems =
              data.map((item) => TreatmentVideoItem.fromJson(item)).toList();
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  bool get isPurchased {
    final user = context.read<AuthCubit>().state.successLogin?.user;
    return user?.purchasedVideos
            ?.any((video) => video.videoId == widget.treatmentId) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: size.width,
        height: size.height,
        child: Stack(
          children: [
            CustomScrollView(
              slivers: [
                SliverAppBar(
                  backgroundColor: Colors.black.withOpacity(.8),
                  expandedHeight: 200,
                  pinned: true,
                  stretch: true,
                  flexibleSpace: FlexibleSpaceBar(
                    centerTitle: true,
                    background: Container(
                      width: size.width,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          image: DecorationImage(
                            fit: BoxFit.cover,
                            image: NetworkImage('${widget.parentImage!}'),
                          )),
                      height: 250,
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextApp(
                          text: widget.treatmentModel.text ?? "علاج نفسي",
                          style: context.textStyle.copyWith(
                            fontSize: 20,
                            fontWeight: FontWeightHelper.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.0, vertical: 5),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextApp(
                          text: "جلسات العلاج (${videoItems.length})",
                          style: context.textStyle.copyWith(
                            fontSize: 20,
                            color: context.color.primaryColor,
                            fontWeight: FontWeightHelper.bold,
                          ),
                        ),
                        SizedBox(height: 5),
                        Container(
                          width: context.width / 4.5,
                          height: 2,
                          color: context.color.primaryColor,
                        )
                      ],
                    ),
                  ),
                ),
                if (isLoading)
                  SliverToBoxAdapter(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.all(32.0),
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  )
                else if (error != null)
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: Center(
                        child: TextApp(
                          text: "حدث خطأ في تحميل الجلسات",
                          style: context.textStyle.copyWith(
                            fontSize: 16,
                            color: context.color.grayColor,
                          ),
                        ),
                      ),
                    ),
                  )
                else if (videoItems.isEmpty)
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: Center(
                        child: TextApp(
                          text: "لا توجد جلسات متاحة",
                          style: context.textStyle.copyWith(
                            fontSize: 16,
                            color: context.color.grayColor,
                          ),
                        ),
                      ),
                    ),
                  )
                else
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, i) {
                        final videoItem = videoItems[i];
                        final isFirstVideo = i == 0;
                        final canWatch = isPurchased || isFirstVideo;

                        return InkWell(
                          onTap: canWatch
                              ? () {
                                  // Navigate to video player
                                  context.router
                                      .push(LectureVideoPlayerViewRoute(
                                    title: videoItem.text ?? "جلسة علاج",
                                    videoUrl: videoItem.image ?? "",
                                    description:
                                        widget.treatmentModel.text ?? "",
                                  ));
                                }
                              : null,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                              children: [
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 6),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Flexible(
                                        child: Row(
                                          children: [
                                            InkWell(
                                              onTap: canWatch
                                                  ? () {
                                                      context.router.push(
                                                          TreatmentVideoPlayerViewRoute(
                                                        title: videoItem.text ??
                                                            "جلسة علاج",
                                                        videoUrl:
                                                            videoItem.image ??
                                                                "",
                                                        treatmentTitle: widget
                                                                .treatmentModel
                                                                .text ??
                                                            "",
                                                      ));
                                                    }
                                                  : null,
                                              child: Container(
                                                width: 50,
                                                height: 50,
                                                decoration: BoxDecoration(
                                                  color: canWatch
                                                      ? context
                                                          .color.primaryColor
                                                      : context.color.grayColor,
                                                  borderRadius:
                                                      BorderRadius.circular(25),
                                                ),
                                                child: Icon(
                                                  canWatch
                                                      ? Icons.play_arrow
                                                      : Icons.lock,
                                                  color: Colors.white,
                                                  size: 30,
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 12),
                                            Flexible(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Flexible(
                                                        child: TextApp(
                                                          text:
                                                              videoItem.text ??
                                                                  "جلسة علاج",
                                                          maxLines: 1,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          style: context
                                                              .textStyle
                                                              .copyWith(
                                                            fontSize: 16,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color: canWatch
                                                                ? null
                                                                : context.color
                                                                    .grayColor,
                                                          ),
                                                        ),
                                                      ),
                                                      if (isFirstVideo &&
                                                          !isPurchased)
                                                        Container(
                                                          margin:
                                                              EdgeInsets.only(
                                                                  right: 8),
                                                          padding: EdgeInsets
                                                              .symmetric(
                                                                  horizontal: 6,
                                                                  vertical: 2),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors.green,
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: TextApp(
                                                            text: "مجاني",
                                                            style: context
                                                                .textStyle
                                                                .copyWith(
                                                              fontSize: 10,
                                                              color:
                                                                  Colors.white,
                                                              fontWeight:
                                                                  FontWeightHelper
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 5),
                                                  Row(
                                                    children: [
                                                      TextApp(
                                                        text: canWatch
                                                            ? "متاح للمشاهدة"
                                                            : "مقفل",
                                                        style: context.textStyle
                                                            .copyWith(
                                                          fontSize: 15,
                                                          fontWeight:
                                                              FontWeightHelper
                                                                  .medium,
                                                          color: canWatch
                                                              ? Colors.green
                                                              : context.color
                                                                  .grayColor,
                                                        ),
                                                      ),
                                                      SizedBox(width: 10),
                                                      Row(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Icon(
                                                            Icons.circle,
                                                            size: 10,
                                                            color: context.color
                                                                .grayColor,
                                                          ),
                                                          SizedBox(width: 5),
                                                          TextApp(
                                                            text: "فيديو",
                                                            style: context
                                                                .textStyle
                                                                .copyWith(
                                                              fontSize: 15,
                                                              fontWeight:
                                                                  FontWeightHelper
                                                                      .medium,
                                                              color: context
                                                                  .color
                                                                  .grayColor,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  )
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Divider(
                                  color: Colors.white.withOpacity(.2),
                                  thickness: 1.5,
                                )
                              ],
                            ),
                          ),
                        );
                      },
                      childCount: videoItems.length,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class TreatmentVideoItem {
  final int? id;
  final String? text;
  final String? image;
  final String? type;
  final int? videoId;

  TreatmentVideoItem({
    this.id,
    this.text,
    this.image,
    this.type,
    this.videoId,
  });

  factory TreatmentVideoItem.fromJson(Map<String, dynamic> json) {
    return TreatmentVideoItem(
      id: json['id'],
      text: json['text'],
      image: json['image'],
      type: json['type'],
      videoId: json['video_id'],
    );
  }
}
