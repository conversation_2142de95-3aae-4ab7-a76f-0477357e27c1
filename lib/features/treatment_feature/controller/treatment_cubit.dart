import 'dart:convert';
import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/concerns_feature/domain/repository/concerns_repository.dart';
import 'package:clean_arc/features/auth_feature/domain/enum/user_type_enum.dart';
import 'package:clean_arc/features/auth_feature/domain/model/message_model/response_message_model.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/treatment_model.dart';
import 'package:clean_arc/features/treatment_feature/domain/model/sub_treatment_model.dart';
import 'package:clean_arc/features/treatment_feature/domain/repository/treatment_repository.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../../core/data/services/shared_prefs/shared_pref.dart';
import '../../../core/data/services/shared_prefs/shared_prefs_key.dart';

part 'treatment_cubit.freezed.dart';

part 'treatment_state.dart';

@injectable
class TreatmentCubit extends Cubit<TreatmentState> {
  TreatmentCubit(this.repository) : super(TreatmentState.initial());

  final TreatmentRepository repository;

  Future<void> fetchTreatment() async {
    emit(state.copyWith(
      isLoadingTreatment: true,
      successTreatment: null,
      errorTreatment: null,
    ));
    final result = await repository.fetchTreatment();

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingTreatment: false,
          errorTreatment: failure,
          successTreatment: null,
        ));
      },
      (concerns) {
        emit(state.copyWith(
          isLoadingTreatment: false,
          errorTreatment: null,
          successTreatment: concerns,
        ));
      },
    );
  }

  Future<void> getTreatmentVideoById({
    required int id,
  }) async {
    emit(state.copyWith(
      isLoadingGetTreatmentVideoById: true,
      successGetTreatmentVideoById: null,
      errorGetTreatmentVideoById: null,
    ));

    final result = await repository.getTreatmentVideoById(id: id);

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingGetTreatmentVideoById: false,
          errorGetTreatmentVideoById: failure,
          successGetTreatmentVideoById: null,
        ));
      },
      (success) {
        emit(state.copyWith(
          isLoadingGetTreatmentVideoById: false,
          errorGetTreatmentVideoById: null,
          successGetTreatmentVideoById: success,
        ));
      },
    );
  }
  //
  // Future<void> getQuestionsByThoughtId({
  //   required int id,
  // }) async
  // {
  //   emit(state.copyWith(
  //     isLoadingQuestions: true,
  //     successQuestions: null,
  //     errorQuestions: null,
  //   ));
  //
  //   final result = await repository.getQuestionsByThoughtId(id: id);
  //
  //   result.fold(
  //     (failure) {
  //       emit(state.copyWith(
  //         isLoadingQuestions: false,
  //         errorQuestions: failure,
  //         successQuestions: null,
  //       ));
  //     },
  //     (success) {
  //       emit(state.copyWith(
  //         isLoadingQuestions: false,
  //         errorQuestions: null,
  //         successQuestions: success,
  //       ));
  //     },
  //   );
  // }
}
