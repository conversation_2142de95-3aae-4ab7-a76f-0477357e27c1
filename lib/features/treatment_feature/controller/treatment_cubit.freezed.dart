// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'treatment_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TreatmentState {
  ///============================================================
  bool? get isLoadingTreatment => throw _privateConstructorUsedError;
  Failure? get errorTreatment => throw _privateConstructorUsedError;
  List<TreatmentModel>? get successTreatment =>
      throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingGetTreatmentVideoById =>
      throw _privateConstructorUsedError;
  Failure? get errorGetTreatmentVideoById => throw _privateConstructorUsedError;
  BaseResponseModel<List<TreatmentVideoModel>>?
      get successGetTreatmentVideoById => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingQuestions => throw _privateConstructorUsedError;
  Failure? get errorQuestions => throw _privateConstructorUsedError;
  List<QuestionsModel>? get successQuestions =>
      throw _privateConstructorUsedError;
  bool get isChecked => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentStateCopyWith<TreatmentState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentStateCopyWith<$Res> {
  factory $TreatmentStateCopyWith(
          TreatmentState value, $Res Function(TreatmentState) then) =
      _$TreatmentStateCopyWithImpl<$Res, TreatmentState>;
  @useResult
  $Res call(
      {bool? isLoadingTreatment,
      Failure? errorTreatment,
      List<TreatmentModel>? successTreatment,
      bool? isLoadingGetTreatmentVideoById,
      Failure? errorGetTreatmentVideoById,
      BaseResponseModel<List<TreatmentVideoModel>>?
          successGetTreatmentVideoById,
      bool? isLoadingQuestions,
      Failure? errorQuestions,
      List<QuestionsModel>? successQuestions,
      bool isChecked});
}

/// @nodoc
class _$TreatmentStateCopyWithImpl<$Res, $Val extends TreatmentState>
    implements $TreatmentStateCopyWith<$Res> {
  _$TreatmentStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingTreatment = freezed,
    Object? errorTreatment = freezed,
    Object? successTreatment = freezed,
    Object? isLoadingGetTreatmentVideoById = freezed,
    Object? errorGetTreatmentVideoById = freezed,
    Object? successGetTreatmentVideoById = freezed,
    Object? isLoadingQuestions = freezed,
    Object? errorQuestions = freezed,
    Object? successQuestions = freezed,
    Object? isChecked = null,
  }) {
    return _then(_value.copyWith(
      isLoadingTreatment: freezed == isLoadingTreatment
          ? _value.isLoadingTreatment
          : isLoadingTreatment // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorTreatment: freezed == errorTreatment
          ? _value.errorTreatment
          : errorTreatment // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successTreatment: freezed == successTreatment
          ? _value.successTreatment
          : successTreatment // ignore: cast_nullable_to_non_nullable
              as List<TreatmentModel>?,
      isLoadingGetTreatmentVideoById: freezed == isLoadingGetTreatmentVideoById
          ? _value.isLoadingGetTreatmentVideoById
          : isLoadingGetTreatmentVideoById // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetTreatmentVideoById: freezed == errorGetTreatmentVideoById
          ? _value.errorGetTreatmentVideoById
          : errorGetTreatmentVideoById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetTreatmentVideoById: freezed == successGetTreatmentVideoById
          ? _value.successGetTreatmentVideoById
          : successGetTreatmentVideoById // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<List<TreatmentVideoModel>>?,
      isLoadingQuestions: freezed == isLoadingQuestions
          ? _value.isLoadingQuestions
          : isLoadingQuestions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorQuestions: freezed == errorQuestions
          ? _value.errorQuestions
          : errorQuestions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successQuestions: freezed == successQuestions
          ? _value.successQuestions
          : successQuestions // ignore: cast_nullable_to_non_nullable
              as List<QuestionsModel>?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$$TreatmentStateImplImplCopyWith<$Res>
    implements $TreatmentStateCopyWith<$Res> {
  factory _$$$TreatmentStateImplImplCopyWith(_$$TreatmentStateImplImpl value,
          $Res Function(_$$TreatmentStateImplImpl) then) =
      __$$$TreatmentStateImplImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isLoadingTreatment,
      Failure? errorTreatment,
      List<TreatmentModel>? successTreatment,
      bool? isLoadingGetTreatmentVideoById,
      Failure? errorGetTreatmentVideoById,
      BaseResponseModel<List<TreatmentVideoModel>>?
          successGetTreatmentVideoById,
      bool? isLoadingQuestions,
      Failure? errorQuestions,
      List<QuestionsModel>? successQuestions,
      bool isChecked});
}

/// @nodoc
class __$$$TreatmentStateImplImplCopyWithImpl<$Res>
    extends _$TreatmentStateCopyWithImpl<$Res, _$$TreatmentStateImplImpl>
    implements _$$$TreatmentStateImplImplCopyWith<$Res> {
  __$$$TreatmentStateImplImplCopyWithImpl(_$$TreatmentStateImplImpl _value,
      $Res Function(_$$TreatmentStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingTreatment = freezed,
    Object? errorTreatment = freezed,
    Object? successTreatment = freezed,
    Object? isLoadingGetTreatmentVideoById = freezed,
    Object? errorGetTreatmentVideoById = freezed,
    Object? successGetTreatmentVideoById = freezed,
    Object? isLoadingQuestions = freezed,
    Object? errorQuestions = freezed,
    Object? successQuestions = freezed,
    Object? isChecked = null,
  }) {
    return _then(_$$TreatmentStateImplImpl(
      isLoadingTreatment: freezed == isLoadingTreatment
          ? _value.isLoadingTreatment
          : isLoadingTreatment // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorTreatment: freezed == errorTreatment
          ? _value.errorTreatment
          : errorTreatment // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successTreatment: freezed == successTreatment
          ? _value._successTreatment
          : successTreatment // ignore: cast_nullable_to_non_nullable
              as List<TreatmentModel>?,
      isLoadingGetTreatmentVideoById: freezed == isLoadingGetTreatmentVideoById
          ? _value.isLoadingGetTreatmentVideoById
          : isLoadingGetTreatmentVideoById // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetTreatmentVideoById: freezed == errorGetTreatmentVideoById
          ? _value.errorGetTreatmentVideoById
          : errorGetTreatmentVideoById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetTreatmentVideoById: freezed == successGetTreatmentVideoById
          ? _value.successGetTreatmentVideoById
          : successGetTreatmentVideoById // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<List<TreatmentVideoModel>>?,
      isLoadingQuestions: freezed == isLoadingQuestions
          ? _value.isLoadingQuestions
          : isLoadingQuestions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorQuestions: freezed == errorQuestions
          ? _value.errorQuestions
          : errorQuestions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successQuestions: freezed == successQuestions
          ? _value._successQuestions
          : successQuestions // ignore: cast_nullable_to_non_nullable
              as List<QuestionsModel>?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$$TreatmentStateImplImpl implements _$TreatmentStateImpl {
  _$$TreatmentStateImplImpl(
      {required this.isLoadingTreatment,
      required this.errorTreatment,
      required final List<TreatmentModel>? successTreatment,
      required this.isLoadingGetTreatmentVideoById,
      required this.errorGetTreatmentVideoById,
      required this.successGetTreatmentVideoById,
      required this.isLoadingQuestions,
      required this.errorQuestions,
      required final List<QuestionsModel>? successQuestions,
      this.isChecked = true})
      : _successTreatment = successTreatment,
        _successQuestions = successQuestions;

  ///============================================================
  @override
  final bool? isLoadingTreatment;
  @override
  final Failure? errorTreatment;
  final List<TreatmentModel>? _successTreatment;
  @override
  List<TreatmentModel>? get successTreatment {
    final value = _successTreatment;
    if (value == null) return null;
    if (_successTreatment is EqualUnmodifiableListView)
      return _successTreatment;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  ///============================================================
  @override
  final bool? isLoadingGetTreatmentVideoById;
  @override
  final Failure? errorGetTreatmentVideoById;
  @override
  final BaseResponseModel<List<TreatmentVideoModel>>?
      successGetTreatmentVideoById;

  ///============================================================
  @override
  final bool? isLoadingQuestions;
  @override
  final Failure? errorQuestions;
  final List<QuestionsModel>? _successQuestions;
  @override
  List<QuestionsModel>? get successQuestions {
    final value = _successQuestions;
    if (value == null) return null;
    if (_successQuestions is EqualUnmodifiableListView)
      return _successQuestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool isChecked;

  @override
  String toString() {
    return 'TreatmentState(isLoadingTreatment: $isLoadingTreatment, errorTreatment: $errorTreatment, successTreatment: $successTreatment, isLoadingGetTreatmentVideoById: $isLoadingGetTreatmentVideoById, errorGetTreatmentVideoById: $errorGetTreatmentVideoById, successGetTreatmentVideoById: $successGetTreatmentVideoById, isLoadingQuestions: $isLoadingQuestions, errorQuestions: $errorQuestions, successQuestions: $successQuestions, isChecked: $isChecked)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$$TreatmentStateImplImpl &&
            (identical(other.isLoadingTreatment, isLoadingTreatment) ||
                other.isLoadingTreatment == isLoadingTreatment) &&
            (identical(other.errorTreatment, errorTreatment) ||
                other.errorTreatment == errorTreatment) &&
            const DeepCollectionEquality()
                .equals(other._successTreatment, _successTreatment) &&
            (identical(other.isLoadingGetTreatmentVideoById,
                    isLoadingGetTreatmentVideoById) ||
                other.isLoadingGetTreatmentVideoById ==
                    isLoadingGetTreatmentVideoById) &&
            (identical(other.errorGetTreatmentVideoById,
                    errorGetTreatmentVideoById) ||
                other.errorGetTreatmentVideoById ==
                    errorGetTreatmentVideoById) &&
            (identical(other.successGetTreatmentVideoById,
                    successGetTreatmentVideoById) ||
                other.successGetTreatmentVideoById ==
                    successGetTreatmentVideoById) &&
            (identical(other.isLoadingQuestions, isLoadingQuestions) ||
                other.isLoadingQuestions == isLoadingQuestions) &&
            (identical(other.errorQuestions, errorQuestions) ||
                other.errorQuestions == errorQuestions) &&
            const DeepCollectionEquality()
                .equals(other._successQuestions, _successQuestions) &&
            (identical(other.isChecked, isChecked) ||
                other.isChecked == isChecked));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoadingTreatment,
      errorTreatment,
      const DeepCollectionEquality().hash(_successTreatment),
      isLoadingGetTreatmentVideoById,
      errorGetTreatmentVideoById,
      successGetTreatmentVideoById,
      isLoadingQuestions,
      errorQuestions,
      const DeepCollectionEquality().hash(_successQuestions),
      isChecked);

  /// Create a copy of TreatmentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$$TreatmentStateImplImplCopyWith<_$$TreatmentStateImplImpl> get copyWith =>
      __$$$TreatmentStateImplImplCopyWithImpl<_$$TreatmentStateImplImpl>(
          this, _$identity);
}

abstract class _$TreatmentStateImpl implements TreatmentState {
  factory _$TreatmentStateImpl(
      {required final bool? isLoadingTreatment,
      required final Failure? errorTreatment,
      required final List<TreatmentModel>? successTreatment,
      required final bool? isLoadingGetTreatmentVideoById,
      required final Failure? errorGetTreatmentVideoById,
      required final BaseResponseModel<List<TreatmentVideoModel>>?
          successGetTreatmentVideoById,
      required final bool? isLoadingQuestions,
      required final Failure? errorQuestions,
      required final List<QuestionsModel>? successQuestions,
      final bool isChecked}) = _$$TreatmentStateImplImpl;

  ///============================================================
  @override
  bool? get isLoadingTreatment;
  @override
  Failure? get errorTreatment;
  @override
  List<TreatmentModel>? get successTreatment;

  ///============================================================
  @override
  bool? get isLoadingGetTreatmentVideoById;
  @override
  Failure? get errorGetTreatmentVideoById;
  @override
  BaseResponseModel<List<TreatmentVideoModel>>?
      get successGetTreatmentVideoById;

  ///============================================================
  @override
  bool? get isLoadingQuestions;
  @override
  Failure? get errorQuestions;
  @override
  List<QuestionsModel>? get successQuestions;
  @override
  bool get isChecked;

  /// Create a copy of TreatmentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$$TreatmentStateImplImplCopyWith<_$$TreatmentStateImplImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
