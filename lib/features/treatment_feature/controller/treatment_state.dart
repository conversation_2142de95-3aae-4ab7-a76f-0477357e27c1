part of 'treatment_cubit.dart';

@freezed
class TreatmentState with _$TreatmentState {
  factory TreatmentState({
    ///============================================================

    required bool? isLoadingTreatment,
    required Failure? errorTreatment,
    required List<TreatmentModel>? successTreatment,

    ///============================================================
    required bool? isLoadingGetTreatmentVideoById,
    required Failure? errorGetTreatmentVideoById,
    required  BaseResponseModel<List<TreatmentVideoModel>>? successGetTreatmentVideoById,
    ///============================================================

    required bool? isLoadingQuestions,
    required Failure? errorQuestions,
    required List<QuestionsModel>? successQuestions,



    @Default(true) bool isChecked,
  }) = _$TreatmentStateImpl;

  factory TreatmentState.initial() => TreatmentState(
    errorTreatment: null,
    isLoadingTreatment: null,
    successTreatment: null,
    errorGetTreatmentVideoById: null,
    isLoadingGetTreatmentVideoById: null,
    successGetTreatmentVideoById: null,
    isLoadingQuestions: null,
    successQuestions: null,
    errorQuestions: null,
      );
}
