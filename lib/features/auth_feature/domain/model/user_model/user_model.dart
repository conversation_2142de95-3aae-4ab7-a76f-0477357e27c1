class UserModel {
  UserModel({
    this.user,
    this.token,
    this.expiresAt,
    this.message,
  });

  bool? auth;
  final User? user;
  final String? token;
  final DateTime? expiresAt;
  final String? message;

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      user: json["data"] == null ? null : User.fromJson(json["data"]),
      token: json["token"],
      expiresAt: DateTime.tryParse(json["expiresAt"] ?? ""),
      message: json["message"],
    );
  }

  Map<String, dynamic> toJson() => {
        "data": user?.toJson(),
        "token": token,
        "expiresAt": expiresAt?.toIso8601String(),
        "message": message,
      };
}

class User {
  User({
    required this.id,
    required this.name,
    required this.email,
    required this.roles,
    required this.phone,
    required this.age,
    required this.gender,
    required this.nickname,
    required this.topics,
    required this.createdAt,
  });

  final int? id;
  final String? name;
  final String? email;
  final List<String> roles;
  final String? phone;
  final int? age;
  final String? gender;
  final String? nickname;
  final List<dynamic> topics;
  final DateTime? createdAt;

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json["id"],
      name: json["name"],
      email: json["email"],
      roles: json["roles"] == null
          ? []
          : List<String>.from(json["roles"]!.map((x) => x)),
      phone: json["phone"],
      age: json["age"],
      gender: json["gender"],
      nickname: json["nickname"],
      topics: json["topics"] == null
          ? []
          : List<dynamic>.from(json["topics"]!.map((x) => x)),
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "roles": roles.map((x) => x).toList(),
        "phone": phone,
        "age": age,
        "gender": gender,
        "nickname": nickname,
        "topics": topics.map((x) => x).toList(),
        "createdAt": createdAt?.toIso8601String(),
      };
}
