class AppVersionModel {
  final int? id;
  final String ?platform;
  final String ?version;
  final String ?versionUrl;
  final String ?changeLog;
  final int ?isForceUpdate;
  final DateTime ?createdAt;
  final DateTime ?updatedAt;

  AppVersionModel({
      this.id,
      this.platform,
      this.versionUrl,
      this.version,
      this.changeLog,
      this.isForceUpdate,
      this.createdAt,
      this.updatedAt,
  });

  factory AppVersionModel.fromJson(Map<String, dynamic> json) {
    return AppVersionModel(
      id: json['id'],
      platform: json['platform'],
      version: json['version'],
      versionUrl: json['versionUrl'],
      changeLog: json['change_log'],
      isForceUpdate: json['is_force_update'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'platform': platform,
      'version': version,
      'versionUrl': versionUrl,
      'change_log': changeLog,
      'is_force_update': isForceUpdate,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}