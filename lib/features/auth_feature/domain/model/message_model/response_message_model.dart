import 'package:json_annotation/json_annotation.dart';

part 'response_message_model.g.dart';

@JsonSerializable()
class MessageModel {
  final bool success;
  final String? message;
  final String? error;
  final String? detail;

  MessageModel(this.message, this.error, this.detail, this.success);

  factory MessageModel.fromJson(Map<String, dynamic> json) =>
      _$MessageModelFromJson(json);

  Map<String, dynamic> toJson() => _$MessageModelToJson(this);
}
