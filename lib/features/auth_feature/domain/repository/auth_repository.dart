import 'dart:io';

import 'package:clean_arc/core/data/repositories/base_repository_impl.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/auth_feature/domain/model/app_version.dart';
import 'package:clean_arc/features/auth_feature/domain/model/message_model/response_message_model.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:clean_arc/features/auth_feature/domain/services/remote/login_remote_data_source.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

abstract class AuthRepository {
  Future<Either<Failure, UserModel>> login({
    required String email,
    required String password,
  });

  Future<Either<Failure, UserModel>> register({
    required String name,
    required String nickname,
    required String password,
    required String email,
    required String phone,
    required String gender,
    required int age,
  });

  Future<Either<Failure, MessageModel>> verificationCode({
    required code,
  });

  Future<Either<Failure, UserModel>> updateProfile({
    required int id,
    required String name,
    required String nickname,
    required String email,
    required String phone,
    required String gender,
    required int age,
    String? password,
  });

  Future<Either<Failure, MessageModel>> deleteAccount({
    required int id,
  });

  Future<Either<Failure, AppVersionModel>> getAppVersions();
}

@LazySingleton(as: AuthRepository)
class AuthRepositoryImpl extends BaseRepositoryImpl implements AuthRepository {
  final AuthServices _services;

  AuthRepositoryImpl(super.logger, this._services);

  @override
  Future<Either<Failure, UserModel>> login({
    required String email,
    required String password,
  }) {
    return request(() async {
      final result = await _services.login(
        email: email,
        password: password,
      );
      return Right(result);
    });
  }

  @override
  Future<Either<Failure, MessageModel>> verificationCode({
    required code,
  }) {
    return request(() async {
      late final result;
      result = await _services.verificationCode(
        code: code,
      );

      return Right(result);
    });
  }

  @override
  Future<Either<Failure, UserModel>> register({
    required String name,
    required String nickname,
    required String password,
    required String email,
    required String phone,
    required String gender,
    required int age,
  }) {
    return request(() async {
      final result = await _services.register(
        name: name,
        nickname: nickname,
        password: password,
        email: email,
        phone: phone,
        gender: gender,
        age: age,
      );
      return Right(result);
    });
  }

  @override
  Future<Either<Failure, UserModel>> updateProfile({
    required int id,
    required String name,
    required String nickname,
    required String email,
    required String phone,
    required String gender,
    required int age,
    String? password,
  }) {
    return request(() async {
      final result = await _services.updateProfile(
        id: id,
        name: name,
        nickname: nickname,
        email: email,
        phone: phone,
        gender: gender,
        age: age,
        password: password,
      );
      return Right(result);
    });
  }

  @override
  Future<Either<Failure, MessageModel>> deleteAccount({
    required int id,
  }) {
    return request(() async {
      final result = await _services.deleteAccount(id: id);
      return Right(result);
    });
  }

  @override
  Future<Either<Failure, AppVersionModel>> getAppVersions() {
    return request(() async {
      final result = await _services.getAppVersions();

      if (Platform.isAndroid) {
        return Right((result.data ?? []).firstWhere(
            (element) => element.platform == "android",
            orElse: () => AppVersionModel()));
      } else {
        return Right((result.data ?? []).firstWhere(
            (element) => element.platform == "ios",
            orElse: () => AppVersionModel())); // فقط الـ List<AppVersionModel>
      }
    });
  }
}
