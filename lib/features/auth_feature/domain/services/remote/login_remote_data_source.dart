import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/domain/model/app_version.dart';
import 'package:clean_arc/features/auth_feature/domain/model/message_model/response_message_model.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'login_remote_data_source.g.dart';

@LazySingleton()
@RestApi(baseUrl: '')
abstract class AuthServices {
  @factoryMethod
  factory AuthServices(Dio dio, Configuration configuration) {
    return _AuthServices(dio, baseUrl: configuration.getApiUrl);
  }

  @POST("auth/login")
  Future<UserModel> login({
    @Field("email") required String email,
    @Field("password") required String password,
  });
  @POST('auth/register')
  Future<UserModel> register({
    @Field("name") required String name,
    @Field("nickname") required String nickname,
    @Field("password") required String password,
    @Field("email") required String email,
    @Field("phone") required String phone,
    @Field("gender") required String gender,
    @Field("age") required int age,
  });

  @POST('auth/verify')
  Future<MessageModel> verificationCode({
    @Field('code') required String code,
  });

  @PUT('auth/update/{id}')
  Future<UserModel> updateProfile({
    @Path('id') required int id,
    @Field("name") required String name,
    @Field("nickname") required String nickname,
    @Field("email") required String email,
    @Field("phone") required String phone,
    @Field("gender") required String gender,
    @Field("age") required int age,
    @Field("password") String? password,
  });

  @DELETE('auth/delete/{id}')
  Future<MessageModel> deleteAccount({
    @Path('id') required int id,
  });

  @GET('app-version/')
  Future<BaseResponseModel<List<AppVersionModel>>> getAppVersions();
}
