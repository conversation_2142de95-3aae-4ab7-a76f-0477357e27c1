import 'package:clean_arc/app.dart';
import 'package:clean_arc/core/presentation/extintions/context_extintions.dart';
import 'package:flutter/material.dart';

class TermsConditionComponent extends StatelessWidget {
  const TermsConditionComponent({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: Column(
            children: [
              new RichText(
                text: new TextSpan(
                  text: context.translate.byUsingOurServicesYouAreAgreeingToOur,
                  style: DefaultTextStyle.of(context).style,
                ),
              ),
              new RichText(
                text: new TextSpan(
                  style: DefaultTextStyle.of(context).style,
                  children: <TextSpan>[
                    new TextSpan(
                        text: context.translate.terms,
                        style: new TextStyle(
                            fontWeight: FontWeight.bold,
                            color: context.color.primaryColor)),
                    new TextSpan(text: ' ' + context.translate.and + ' '),
                    new TextSpan(
                        text: context.translate.privacyPolicy,
                        style: new TextStyle(
                            fontWeight: FontWeight.bold,
                            color: context.color.primaryColor)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
