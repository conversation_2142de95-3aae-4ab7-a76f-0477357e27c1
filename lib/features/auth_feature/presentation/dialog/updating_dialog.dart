import 'dart:io';

import 'package:clean_arc/app.dart';
import 'package:clean_arc/core/presentation/widget/custom_button_button.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

updateDialog(BuildContext context,
    {required String androidUrl,
    required String iosUrl}) {
  return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (xxx) => WillPopScope(
        onWillPop: () async=> false,
        child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8.0))),
              child: Container(
                height: 250,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppImages.images.core.developmentAndProgress.image(
                            height: 75,
                            width: 75,
                            color: context.color.primaryColor),
                        SizedBox(height: 10),
                        Text(context.translate.thereIsAnewVersion,
                            style: TextStyle(fontSize: 18)),
                        SizedBox(height: 10),
                        CustomButton(
                          width: double.infinity,
                          onPressed: () async {
                            if (Platform.isIOS) {
                              await launchSocial(iosUrl, iosUrl);
                            }  else {
                                await launchSocial(androidUrl, androidUrl);
                              }

                            Navigator.pop(context);
                          },
                          child: TextApp(text: context.translate.updateApp,style: context.textStyleButton,),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                      ]),
                ),
              ),
            ),
      ));
}

Future launchSocial(String protocolUrl, String fallbackUrl) async {
  // Don't use canLaunch because of fbProtocolUrl (fb://)
  try {
    bool launched =
        // await launch(url, forceSafariVC: false, forceWebView: false);
        await launchUrl(Uri.parse(fallbackUrl),
            mode: LaunchMode.externalApplication);
    if (!launched) {
      // await launch(fallbackUrl, forceSafariVC: false, forceWebView: false);
      await launchUrl(
          Uri.parse(
            fallbackUrl,
          ),
          mode: LaunchMode.externalApplication);
    }
  } catch (e) {
    // CustomTrace(StackTrace.current, message:"${ url.path.toString()}--- ${e.toString()} ").toString();

    // await launch(fallbackUrl, forceSafariVC: false, forceWebView: false);
    await await launchUrl(Uri.parse(fallbackUrl),
        mode: LaunchMode.platformDefault);
  }
}
