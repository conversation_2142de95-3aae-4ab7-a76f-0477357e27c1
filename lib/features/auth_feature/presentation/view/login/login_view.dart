import 'package:clean_arc/core/presentation/util/app_constant.dart';
import 'package:clean_arc/core/presentation/widget/screen_utils.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class LoginView extends StatefulWidget {
  const LoginView({super.key});

  static const path = '/LoginView';

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> with ScreenUtils {
  String? _countryDialCode;
  final TextEditingController emailController =
      TextEditingController(text: kDebugMode ? '<EMAIL>' : '');
  final TextEditingController passwordController =
      TextEditingController(text: kDebugMode ? 'password' : '');

  @override
  void initState() {
    _countryDialCode =
        CountryCode.fromCountryCode(AppConstants.countryCode).dialCode;
    super.initState();
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  GlobalKey<FormState> _loginFormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    AuthCubit authCubit = context.read<AuthCubit>();

    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: BlocConsumer<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state.isErrorLogin != null) {
            showError(failure: state.isErrorLogin!);
          }
          if (state.successLogin != null) {
            // FreeTryingViewRoute();
            //   context.router.push(FreeTryingViewRoute());
            context.pushRoute(UserLayoutViewRoute());

            // if (state.successLogin?.exist == true) {
            // } else {
            //   // context.router.push(RegisterViewRoute());
            // }
          }
        },
        builder: (context, state) {
          return Scaffold(
            body: SingleChildScrollView(
              child: SizedBox(
                height: context.height,
                width: context.width,
                child: Stack(
                  children: [
                    Container(
                      height: context.height / 2,
                      color: context.color.primaryColor,
                    ),
                    Column(
                      children: [
                        Expanded(
                          child: SafeArea(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 16),
                              child: Form(
                                key: _loginFormKey,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    CircleAvatar(
                                      backgroundColor:
                                          context.color.primaryColor,
                                      radius: 35,
                                      child: AppImages
                                          .images.salimIcon.cloudIcon
                                          .svg(),
                                    ),
                                    TextApp(
                                      text:
                                          context.translate.logInToYourAccount,
                                      style: context.textStyle.copyWith(
                                        color: context.color.whiteColor,
                                        fontSize:
                                            AppDimensions.fontSizeOverLarge,
                                        fontWeight: FontWeightHelper.bold,
                                      ),
                                    ),
                                    TextApp(
                                      text: context.translate
                                          .enterYourEmailAndPasswordToLogIn,
                                      style: context.textStyle.copyWith(
                                        color: context.color.whiteColor,
                                        fontSize: AppDimensions.fontSizeSmall,
                                        fontWeight: FontWeightHelper.regular,
                                      ),
                                    ),
                                    Spacer(flex: 3),
                                    Container(
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        boxShadow: [
                                          BoxShadow(
                                            color: context.color.borderColor!,
                                            spreadRadius: 1,
                                          ),
                                        ],
                                        color: context.color.whiteColor,
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Column(
                                        children: [
                                          // CustomButton(
                                          //   bgColor: Colors.transparent,
                                          //   withBorderOnly: true,
                                          //   width: double.infinity,
                                          //   onPressed: () {
                                          //     // context.router.push(FreeTryingViewRoute());
                                          //     context.pushRoute(UserLayoutViewRoute());
                                          //
                                          //   },
                                          //   child: Row(
                                          //     crossAxisAlignment:
                                          //         CrossAxisAlignment.center,
                                          //     mainAxisAlignment:
                                          //         MainAxisAlignment.center,
                                          //     children: [
                                          //       AppImages
                                          //           .images.salimIcon.google
                                          //           .svg(),
                                          //       SizedBox(width: 16),
                                          //       TextApp(
                                          //         text: context.translate
                                          //             .continueWithGoogle,
                                          //         style: context.textStyle
                                          //             .copyWith(
                                          //           color:
                                          //               context.color.textColor,
                                          //           fontSize: AppDimensions
                                          //               .fontSizeDefault,
                                          //           fontWeight:
                                          //               FontWeightHelper.medium,
                                          //         ),
                                          //       ),
                                          //     ],
                                          //   ),
                                          // ),
                                          // SizedBox(height: 16),
                                          // Row(
                                          //   children: [
                                          //     Expanded(
                                          //       child: Container(
                                          //         height: 0.5,
                                          //         color: context.color
                                          //             .greyCounterBorderColor,
                                          //       ),
                                          //     ),
                                          //     SizedBox(width: 10),
                                          //     TextApp(
                                          //       text: context
                                          //           .translate.orLogInUsing,
                                          //       style:
                                          //           context.textStyle.copyWith(
                                          //         color: context
                                          //             .color.descriptionColor,
                                          //         fontSize: AppDimensions
                                          //             .fontSizeDefault,
                                          //         fontWeight:
                                          //             FontWeightHelper.medium,
                                          //       ),
                                          //     ),
                                          //     SizedBox(width: 10),
                                          //     Expanded(
                                          //       child: Container(
                                          //         height: 0.5,
                                          //         color: context.color
                                          //             .greyCounterBorderColor,
                                          //       ),
                                          //     ),
                                          //   ],
                                          // ),
                                          SizedBox(height: 16),
                                          CustomTextField(
                                            titleText: context.translate.email,
                                            hintText: context.translate.email,
                                            prefixIcon: AppImages
                                                .images.salimIcon.profileIcon
                                                .svg(),
                                            controller: emailController,
                                            inputType:
                                                TextInputType.emailAddress,
                                            onValidate: (p0) => p0 == null ||
                                                    p0.isEmpty
                                                ? context.translate.enterEmail
                                                : null,
                                          ),
                                          SizedBox(height: 16),
                                          CustomTextField(
                                            titleText:
                                                context.translate.password,
                                            hintText:
                                                context.translate.password,
                                            prefixIcon: AppImages
                                                .images.salimIcon.key
                                                .svg(),
                                            controller: passwordController,
                                            inputType:
                                                TextInputType.visiblePassword,
                                            isPassword: true,
                                            onValidate: (p0) =>
                                                p0 == null || p0.isEmpty
                                                    ? context
                                                        .translate.enterPassword
                                                    : null,
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                children: [
                                                  Checkbox(
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              4.0),
                                                    ),
                                                    value: authCubit.rememberMe,
                                                    onChanged: (value) {
                                                      setState(() {
                                                        authCubit.rememberMe =
                                                            value!;
                                                      });
                                                    },
                                                  ),
                                                  TextApp(
                                                    text: context
                                                        .translate.rememberMe,
                                                    style: context.textStyle
                                                        .copyWith(
                                                      color: context.color
                                                          .descriptionColor,
                                                      fontSize: AppDimensions
                                                          .fontSizeDefault,
                                                      fontWeight:
                                                          FontWeightHelper
                                                              .medium,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              TextApp(
                                                text: context
                                                    .translate.forgotPassword,
                                                style:
                                                    context.textStyle.copyWith(
                                                  color: context
                                                      .color.primaryColor,
                                                  fontSize: AppDimensions
                                                      .fontSizeDefault,
                                                  fontWeight:
                                                      FontWeightHelper.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                          CustomButton(
                                            isLoading: state.isLoadingLogin,
                                            width: double.infinity,
                                            onPressed: () async {
                                              // context.router
                                              //     .push(UserLayoutViewRoute());

                                              if (_loginFormKey.currentState
                                                      ?.validate() ??
                                                  false) {
                                                await authCubit.login(
                                                  email: emailController.text,
                                                  password:
                                                      passwordController.text,
                                                );
                                              }
                                            },
                                            child: TextApp(
                                              text: context.translate.logIn2,
                                              style: context.textStyleButton,
                                            ),
                                          ),
                                          SizedBox(height: 16),
                                          InkWell(
                                            onTap: () {
                                              context.router
                                                  .push(RegisterViewRoute());
                                            },
                                            child: TextApp(
                                              text: context
                                                  .translate.dontHaveAnAccount,
                                              style: context.textStyle.copyWith(
                                                color: context
                                                    .color.descriptionColor,
                                                fontSize:
                                                    AppDimensions.fontSizeSmall,
                                                fontWeight:
                                                    FontWeightHelper.regular,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Spacer(flex: 5),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
