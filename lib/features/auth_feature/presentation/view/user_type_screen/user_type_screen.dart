import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class UserTypeView extends StatelessWidget {
  const UserTypeView({super.key});

  @override
  Widget build(BuildContext context) {
    AuthCubit authCubit = context.watch<AuthCubit>();

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(),
                  CircleAvatar(
                    backgroundColor: context.color.primaryColor,
                    radius: 35,
                    child: AppImages.images.salimIcon.cloudIcon.svg(),
                  ),
                  SizedBox(
                    height: 24,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextApp(
                        text: context.translate.onBoard4,
                        style: context.textStyle.copyWith(
                          fontSize: AppDimensions.fontSizeOverLarge,
                          fontWeight: FontWeightHelper.bold,
                          height: 1,
                          color: context.color.primaryColor,
                        ),
                      ),
                      TextApp(
                        text: context.translate.onBoard4color2,
                        style: context.textStyle.copyWith(
                            fontSize: AppDimensions.fontSizeOverLarge,
                            fontWeight: FontWeightHelper.bold,
                            color: context.color.primaryColor,
                            height: 1),
                      ),
                      TextApp(
                        text: context.translate.onBoard4color1,
                        style: context.textStyle.copyWith(
                          fontSize: AppDimensions.fontSizeOverLarge,
                          fontWeight: FontWeightHelper.bold,
                          height: 1,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 5,
                  ),
                  TextApp(
                    text: context.translate.onBoard4AfterColor,
                    style: context.textStyle.copyWith(
                        height: 1.5,
                        fontWeight: FontWeightHelper.regular,
                        fontSize: AppDimensions.fontSizeExtraLarge,
                        color: context.color.primaryColor),
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
              Column(
                children: [
                  TextApp(
                    text: context.translate.yourPerfectMentalHealthCompanion,
                    style: context.textStyle.copyWith(
                      height: 1.5,
                      fontWeight: FontWeightHelper.regular,
                      fontSize: AppDimensions.fontSizeDefault,
                    ),
                  ),
                  TextApp(
                    text: context.translate.anytimeAnywhere,
                    style: context.textStyle.copyWith(
                      height: 1.5,
                      fontWeight: FontWeightHelper.regular,
                      fontSize: AppDimensions.fontSizeDefault,
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 30,
              ),
              Spacer(),
              AppImages.images.core.onboarding.onboarding4.image(),
              Spacer(),
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                height: 50,
                constraints: BoxConstraints(minWidth: 140),
                decoration: BoxDecoration(
                    color: context.color.primaryColor,
                    borderRadius: BorderRadius.circular(40)),
                // foregroundColor: context.color.whiteColor,
                child: InkWell(
                    onTap: () {
                      context.pushRoute(LoginViewRoute());
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextApp(
                          text: context.translate.startNow,
                          style: TextStyle(
                              color: context.color.whiteColor,
                              fontWeight: FontWeight.bold,
                              fontSize: AppDimensions.fontSizeDefault),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Icon(
                          Icons.arrow_forward_ios_outlined,
                          color: context.color.whiteColor,
                        ),
                      ],
                    )),
              ),
              Spacer(),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextApp(
                    text: context.translate.doYouAlreadyHaveAnAccount,
                    style: context.textStyle.copyWith(
                      fontSize: AppDimensions.fontSizeDefault,
                      fontWeight: FontWeightHelper.bold,
                      height: 1,
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      context.pushRoute(LoginViewRoute());
                    },
                    child: TextApp(
                      text: ' ' + context.translate.logIn,
                      style: context.textStyle.copyWith(
                        fontSize: AppDimensions.fontSizeDefault,
                        fontWeight: FontWeightHelper.bold,
                        height: 1,
                        color: context.color.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}
