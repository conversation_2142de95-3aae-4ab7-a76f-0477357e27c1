import 'package:clean_arc/core/presentation/widget/screen_utils.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:clean_arc/features/auth_feature/presentation/view/register/select_best_object_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:percent_indicator/percent_indicator.dart';

@RoutePage()
class SelectAgeView extends StatefulWidget {
  SelectAgeView({super.key});

  @override
  State<SelectAgeView> createState() => _SelectAgeViewState();
}

class _SelectAgeViewState extends State<SelectAgeView> with ScreenUtils {
  final List<int> age = [
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
  ];

  // int selectedAge = 20;

  late FixedExtentScrollController _scrollController;

  @override
  void initState() {
    _scrollController = FixedExtentScrollController(initialItem: 4);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    AuthCubit authCubit = context.read<AuthCubit>();

    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          Stack(
            children: [
              Container(
                width: context.width,
                height: 5,
                color: context.color.primaryColor!.withOpacity(.3),
              ),
              LinearPercentIndicator(
                padding: EdgeInsets.zero,
                alignment: MainAxisAlignment.start,
                backgroundColor: context.color.primaryColor!.withOpacity(0.2),
                width: 250,
                animation: false,
                lineHeight: 5.0,
                animationDuration: 2500,
                percent: 1,
                linearStrokeCap: LinearStrokeCap.roundAll,
                progressColor: context.color.primaryColor,
              ),
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Row(
            children: [
              TextApp(
                text: context.translate.whatIsYourGender,
                style: context.textStyle.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Spacer(),
          Container(
            height: context.height * .5,
            child: ListWheelScrollView.useDelegate(
              controller: _scrollController,
              // Attach the controller
              itemExtent: 50,
              onSelectedItemChanged: (index) {
                setState(() {
                  authCubit.age =
                      index + 16; // Update selected age starting from 16
                });
              },
              physics: FixedExtentScrollPhysics(),
              childDelegate: ListWheelChildBuilderDelegate(
                builder: (context, index) {
                  final isSelected = index + 16 == authCubit.age;
                  return CircleAvatar(
                    radius: 30,
                    backgroundColor: isSelected
                        ? context.color.primaryColor
                        : Colors.transparent,
                    child: Center(
                      child: Text(
                        '${index + 16}', // List starts from 16
                        style: TextStyle(
                          fontSize: isSelected ? 28 : 24,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                          color: isSelected ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  );
                },
                childCount: 85, // To set the list from 16 to 100
              ),
            ),
          ),
          Spacer(),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: BlocBuilder<AuthCubit, AuthState>(
                builder: (context, state) {
                  return CustomButton(
                    isLoading: authCubit.state.isLoadingRegister,
                    width: double.infinity,
                    onPressed: () async {
                      await authCubit.register();
                      if (state.isErrorRegister != null) {
                        showError(failure: state.isErrorRegister!);
                      }
                      if (state.successRegister != null) {
                        context.router
                            .replaceAll([SelectBestObjectViewRoute()]);
                      }
                    },
                    child: TextApp(
                      text: context.translate.continue1,
                      style: context.textStyleButton,
                    ),
                  );
                },
              ),
            ),
          )
        ],
      ),
    );
  }
}
