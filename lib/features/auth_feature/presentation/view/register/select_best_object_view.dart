import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';

@RoutePage()
class SelectBestObjectView extends StatefulWidget {
  const SelectBestObjectView({super.key});

  @override
  State<SelectBestObjectView> createState() => _SelectBestObjectViewState();
}

class _SelectBestObjectViewState extends State<SelectBestObjectView> {


  @override
  Widget build(BuildContext context) {
    List images = [
      AppImages.images.demo.i1.path,
      AppImages.images.demo.i2.path,
      AppImages.images.demo.i3.path,
      AppImages.images.demo.i4.path,
      AppImages.images.demo.i5.path,
      AppImages.images.demo.i6.path,
    ];
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          Stack(
            children: [
              Container(
                width: context.width,
                height: 5,
                color: context.color.primaryColor!.withOpacity(.3),
              ),
              LinearPercentIndicator(
                padding: EdgeInsets.zero,
                alignment: MainAxisAlignment.start,
                backgroundColor: context.color.primaryColor!.withOpacity(0.2),
                width: 250,
                animation: false,
                lineHeight: 5.0,
                animationDuration: 2500,
                percent: 1,
                linearStrokeCap: LinearStrokeCap.roundAll,
                progressColor: context.color.primaryColor,
              ),
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Row(
            children: [
              TextApp(
                text: context.translate.chooseTheMostImportant,
                style: context.textStyle.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          SizedBox(
            height: 10,
          ),
          Row(
            children: [
              TextApp(
                text: context.translate.chooseTheNumberYouWant,
                style: context.textStyle.copyWith(
                    fontWeight: FontWeight.normal,
                    color: context.color.descriptionColor),
              ),
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Expanded(
              child: GridView.builder(
            itemCount: images.length,
            gridDelegate:
                SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 2),
            itemBuilder: (context, index) {
              return Container(
                child: Image.asset(images[index]),
              );
            },
          )),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: CustomButton(
                width: double.infinity,
                onPressed: () {
                  context.pushRoute(UserLayoutViewRoute());
                },
                child: TextApp(
                  text: context.translate.finished,
                  style: context.textStyleButton,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
