import 'package:clean_arc/core/presentation/functions/validation_utils.dart';
import 'package:clean_arc/core/presentation/widget/screen_utils.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class RegisterView extends StatefulWidget {
  const RegisterView({super.key});

  static const path = '/RegisterView';

  @override
  State<RegisterView> createState() => _RegisterViewState();
}

class _RegisterViewState extends State<RegisterView> with ScreenUtils {
  final GlobalKey<FormState> registerFormKey2 = GlobalKey<FormState>();


  @override
  void dispose() {
    // fullNameController.dispose();
    // surnameController.dispose();
    // emailController.dispose();
    // passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AuthCubit authCubit = context.read<AuthCubit>();

    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: BlocConsumer<AuthCubit, AuthState>(
        listener: (context, state) {

        },
        builder: (context, state) {
          return Scaffold(
            body: SingleChildScrollView(
              child: SizedBox(
                height: context.height,
                width: context.width,
                child: Stack(
                  children: [
                    Container(
                      height: context.height / 2,
                      color: context.color.primaryColor,
                    ),
                    Column(
                      children: [
                        Expanded(
                          child: SafeArea(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 16),
                              child: Form(
                                key: registerFormKey2,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    CircleAvatar(
                                      backgroundColor:
                                          context.color.primaryColor,
                                      radius: 35,
                                      child: AppImages
                                          .images.salimIcon.cloudIcon
                                          .svg(),
                                    ),
                                    TextApp(
                                      text: context.translate.registerNew,
                                      style: context.textStyle.copyWith(
                                        color: context.color.whiteColor,
                                        fontSize:
                                            AppDimensions.fontSizeOverLarge,
                                        fontWeight: FontWeightHelper.bold,
                                      ),
                                    ),
                                    TextApp(
                                      text: context.translate
                                          .enterYourNameEmailAndPasswordToSubscribe,
                                      style: context.textStyle.copyWith(
                                        color: context.color.whiteColor,
                                        fontSize: AppDimensions.fontSizeSmall,
                                        fontWeight: FontWeightHelper.regular,
                                      ),
                                    ),
                                    Spacer(flex: 3),
                                    Container(
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        boxShadow: [
                                          BoxShadow(
                                            color: context.color.borderColor!,
                                            spreadRadius: 1,
                                          ),
                                        ],
                                        color: context.color.whiteColor,
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Column(
                                        children: [
                                          CustomTextField(
                                            titleText:
                                                context.translate.fullName,
                                            hintText:
                                                context.translate.fullName,
                                            prefixIcon: AppImages
                                                .images.salimIcon.profileCircle
                                                .svg(),
                                            controller:  authCubit.fullNameController,
                                            inputType: TextInputType.name,
                                            onValidate: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return context
                                                    .translate.enterFullName;
                                              }
                                              return null;
                                            },
                                          ),
                                          SizedBox(height: 16),
                                          CustomTextField(
                                            titleText:
                                                context.translate.surname,
                                            hintText: context.translate.surname,
                                            prefixIcon: AppImages
                                                .images.salimIcon.profileCircle
                                                .svg(),
                                            controller: authCubit.surnameController,
                                            inputType: TextInputType.name,
                                            onValidate: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return context
                                                    .translate.enterSurname;
                                              }
                                              return null;
                                            },
                                          ),
                                          SizedBox(height: 16),

                                          CustomTextField(
                                            titleText:
                                                context.translate.phoneNumber,
                                            hintText: context.translate.phoneNumber,
                                            prefixIcon: Icon(Icons.phone_android),
                                            controller: authCubit.phoneController,
                                            inputType: TextInputType.name,
                                            onValidate: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return context
                                                    .translate.enterPhoneNumber;
                                              }
                                              return null;
                                            },
                                          ),
                                          SizedBox(height: 16),
                                          CustomTextField(
                                              titleText:
                                                  context.translate.email,
                                              hintText: context.translate.email,
                                              prefixIcon: AppImages
                                                  .images.salimIcon.profileIcon
                                                  .svg(),
                                              controller:  authCubit.emailController,
                                              inputType:
                                                  TextInputType.emailAddress,
                                              onValidate: MultiValidator([
                                                RequiredValidator(
                                                    errorText: context
                                                        .translate.enterEmail),
                                                EmailValidator(
                                                    errorText: context.translate
                                                        .enterAValidEmail)
                                              ])),
                                          SizedBox(height: 16),
                                          CustomTextField(
                                            titleText:
                                                context.translate.password,
                                            hintText:
                                                context.translate.password,
                                            prefixIcon: AppImages
                                                .images.salimIcon.key
                                                .svg(),
                                            controller:  authCubit.passwordController,
                                            inputType:
                                                TextInputType.visiblePassword,
                                            isPassword: true,
                                            onValidate: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return context
                                                    .translate.enterPassword;
                                              } else if (value.length < 8) {
                                                return context
                                                    .translate.passwordTooShort;
                                              }
                                              return null;
                                            },
                                          ),
                                          SizedBox(height: 16),
                                          CustomButton(
                                            isLoading: state.isLoadingRegister,
                                            width: double.infinity,
                                            onPressed: () {
                                              if (registerFormKey2.currentState
                                                      ?.validate() ??
                                                  false) {

                                                //
                                                context.pushRoute(SelectSexViewRoute());


                                                // authCubit.registerUser(
                                                //     // fullName: fullNameController.text,
                                                //     // surname: surnameController.text,
                                                //     // email: emailController.text,
                                                //     // password: passwordController.text,
                                                //     );
                                              }
                                            },
                                            child: TextApp(
                                              text:
                                                  context.translate.registerNew,
                                              style: context.textStyleButton,
                                            ),
                                          ),
                                          SizedBox(height: 16),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              TextApp(
                                                text: context.translate
                                                    .doYouAlreadyHaveAnAccount,
                                                style:
                                                    context.textStyle.copyWith(
                                                  fontSize: AppDimensions
                                                      .fontSizeDefault,
                                                  fontWeight:
                                                      FontWeightHelper.regular,
                                                ),
                                              ),
                                              InkWell(
                                                onTap: () {
                                                  context.router
                                                      .push(LoginViewRoute());
                                                },
                                                child: TextApp(
                                                  text: ' ' +
                                                      context.translate.logIn,
                                                  style: context.textStyle
                                                      .copyWith(
                                                    fontSize: AppDimensions
                                                        .fontSizeDefault,
                                                    fontWeight: FontWeightHelper
                                                        .regular,
                                                    color: context
                                                        .color.primaryColor,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 16),
                                          Row(
                                            children: [
                                              // Expanded(
                                              //   child: Container(
                                              //     height: 0.5,
                                              //     color: context.color
                                              //         .greyCounterBorderColor,
                                              //   ),
                                              // ),
                                              // SizedBox(width: 10),
                                              // TextApp(
                                              //   text: context
                                              //       .translate.orLogInUsing,
                                              //   style:
                                              //       context.textStyle.copyWith(
                                              //     color: context
                                              //         .color.descriptionColor,
                                              //     fontSize: AppDimensions
                                              //         .fontSizeDefault,
                                              //     fontWeight:
                                              //         FontWeightHelper.medium,
                                              //   ),
                                              // ),
                                              // SizedBox(width: 10),
                                              // Expanded(
                                              //   child: Container(
                                              //     height: 0.5,
                                              //     color: context.color
                                              //         .greyCounterBorderColor,
                                              //   ),
                                              // ),
                                            ],
                                          ),
                                          // SizedBox(height: 16),
                                          // CustomButton(
                                          //   bgColor: Colors.transparent,
                                          //   withBorderOnly: true,
                                          //   width: double.infinity,
                                          //   onPressed: () {},
                                          //   child: Row(
                                          //     mainAxisAlignment:
                                          //         MainAxisAlignment.center,
                                          //     children: [
                                          //       AppImages
                                          //           .images.salimIcon.google
                                          //           .svg(),
                                          //       SizedBox(width: 16),
                                          //       TextApp(
                                          //         text: context.translate
                                          //             .continueWithGoogle,
                                          //         style: context.textStyle
                                          //             .copyWith(
                                          //           color:
                                          //               context.color.textColor,
                                          //           fontSize: AppDimensions
                                          //               .fontSizeDefault,
                                          //           fontWeight:
                                          //               FontWeightHelper.medium,
                                          //         ),
                                          //       ),
                                          //     ],
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    ),
                                    Spacer(flex: 9),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
