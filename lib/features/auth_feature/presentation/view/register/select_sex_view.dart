import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:percent_indicator/percent_indicator.dart';

@RoutePage()
class SelectSexView extends StatefulWidget {
  const SelectSexView({super.key});

  @override
  State<SelectSexView> createState() => _SelectSexViewState();
}

class _SelectSexViewState extends State<SelectSexView> {
  @override
  Widget build(BuildContext context) {
    AuthCubit authCubit = context.read<AuthCubit>();
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          Stack(
            children: [
              Container(
                width: context.width,
                height: 5,
                color: context.color.primaryColor!.withOpacity(.3),
              ),
              LinearPercentIndicator(
                padding: EdgeInsets.zero,
                alignment: MainAxisAlignment.start,
                backgroundColor: context.color.primaryColor!.withOpacity(0.2),
                width: 50,
                animation: false,
                lineHeight: 5.0,
                animationDuration: 2500,
                percent: 1,
                linearStrokeCap: LinearStrokeCap.roundAll,
                progressColor: context.color.primaryColor,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    TextApp(
                      text: context.translate.whatIsYourGender,
                      style: context.textStyle
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                SizedBox(height: 16),

                // اختيار الجنس (ذكر)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      authCubit.gender = 'male';
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: context.color.whiteColor,
                      boxShadow: [
                        BoxShadow(
                          color: context.color.grayColor!.withOpacity(.1),
                          spreadRadius: 1,
                          blurRadius: 1,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        AppImages.images.salimIcon.male.svg(),
                        SizedBox(width: 16),
                        TextApp(
                          text: context.translate.male,
                          style: context.textStyle
                              .copyWith(fontWeight: FontWeightHelper.regular),
                        ),
                        Spacer(),
                        Icon(
                          authCubit.gender == 'male'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          color: context.color.primaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16),

                // اختيار الجنس (أنثى)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      authCubit.gender = 'female';
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: context.color.whiteColor,
                      boxShadow: [
                        BoxShadow(
                          color: context.color.grayColor!.withOpacity(.1),
                          spreadRadius: 1,
                          blurRadius: 1,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        AppImages.images.salimIcon.womment.svg(),
                        SizedBox(width: 16),
                        TextApp(
                          text: context.translate.women,
                          style: context.textStyle
                              .copyWith(fontWeight: FontWeightHelper.regular),
                        ),
                        Spacer(),
                        Icon(
                          authCubit.gender == 'female'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          color: context.color.primaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Spacer(),

          // زر المتابعة
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: CustomButton(
                width: double.infinity,
                onPressed: authCubit.gender != null
                    ? () {
                        context.pushRoute(
                            SelectAgeViewRoute());
                      }
                    : null, // يتأكد من تحديد الجنس قبل المتابعة
                child: TextApp(
                  text: context.translate.continue1,
                  style: context.textStyleButton,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
