// import 'package:clean_arc/core/presentation/widget/screen_utils.dart';
// import 'package:clean_arc/core/routing/app_router.gr.dart';
// import 'package:clean_arc/core/utils_package/utils_package.dart';
// import 'package:clean_arc/features/auth_feature/controller/distraction_cubit.dart';
// import 'package:clean_arc/features/auth_feature/presentation/component/teerms_condition_componant.dart';
// import 'package:clean_arc/features/layout_feature/view/user_layout_view.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_pin_code_fields/flutter_pin_code_fields.dart';
//
// @RoutePage()
// class ConfirmOtpView extends StatefulWidget {
//   final String code;
//
//   const ConfirmOtpView({super.key, required this.code});
//
//   static const path = '/ConfirmOtpView';
//
//   @override
//   State<ConfirmOtpView> createState() => _ConfirmOtpViewState();
// }
//
// class _ConfirmOtpViewState extends State<ConfirmOtpView> with ScreenUtils {
//   String otpCode = '';
//
//   void submitOtp() {
//     print(widget.code);
//       if (otpCode.isNotEmpty) {
//       if (otpCode.length >= 6) {
//         FocusScope.of(context).requestFocus(FocusNode());
//
//         context.read<AuthCubit>().verificationCode(otpCode);
//       } else {
//         showError(customMessage: 'pleaseEnterValidOTP1');
//       }
//     } else {
//       showError(customMessage: 'pleaseEnterValidOTP00');
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     AuthCubit authCubit = context.watch<AuthCubit>();
//     return Scaffold(
//         // backgroundColor: context.color.primaryColor?.withOpacity(.2),
//         body: BlocListener<AuthCubit, AuthState>(
//       listener: (context, state) {
//         print("state.successLoginstate.successLogin ${state.successLogin}");
//         if (state.isErrorVerificationCode != null) {
//           showError(failure: state.isErrorVerificationCode);
//         }
//         if (state.successVerificationCode != null) {
//           context.router.push(UserLayoutViewRoute());
//         }
//       },
//       child: SafeArea(
//         child: SizedBox(
//           height: context.height,
//           width: context.width,
//           child: Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 16),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Spacer(),
//                 IconButton(
//                     onPressed: () {
//                       context.router.maybePop();
//                     },
//                     icon: const Icon(
//                       Icons.arrow_back_ios,
//                       size: AppDimensions.fontSizeExtraLarge,
//                     )),
//                 Spacer(),
//                 Container(
//                   child: TextApp(
//                     text: context.translate.confirmVerificationCode,
//                     style: context.textStyle.copyWith(
//                       fontWeight: FontWeightHelper.medium,
//                       fontSize: AppDimensions.fontSizeLarge,
//                       height: 2,
//                     ),
//                     textAlign: TextAlign.start,
//                   ),
//                 ),
//                 const Spacer(
//                   flex: 3,
//                 ),
//                 TextApp(
//                   text: context.translate.codeSent,
//                   style: context.textStyle.copyWith(
//                       fontWeight: FontWeightHelper.bold,
//                       height: 1.6,
//                       fontSize: AppDimensions.fontSizeOverLarge),
//                 ),
//                 SizedBox(
//                   height: 16.h,
//                 ),
//                 SizedBox(
//                   height: 16.h,
//                 ),
//                 PinCodeFields(
//                   length: 6,
//                   keyboardType: TextInputType.number,
//                   borderColor: context.color.grayColor!,
//                   fieldBorderStyle: FieldBorderStyle.square,
//                   borderRadius: BorderRadius.circular(10),
//                   fieldHeight: 50,
//                   borderWidth: 1,
//                   activeBorderColor: Theme.of(context).primaryColor,
//
//                   //set to true to show as box or false to show as dash
//                   //runs when a code is typed in
//                   onChange: (String code) {
//                     otpCode = code;
//                     setState(() {});
//                   },
//                   autofocus: true,
//                   onComplete: (String verificationCode) {
//                     // log(otpCode);
//                     otpCode = verificationCode;
//                     print(widget.code);
//                     if (otpCode == widget.code) {
//                       submitOtp();
//                     } else {
//                       showError(
//                           customMessage: context.translate.enterValidOtp);
//                     }                  }, // end onSubmit
//                 ),
//                 SizedBox(
//                   height: 20.h,
//                 ),
//
//                 Text(currentUser?.value.user?.verificationCode ?? '', style: context.textStyle.copyWith(
// fontSize: 18
//                 )),
//
//                 Spacer(
//                   flex: 10,
//                 ),
//                 CustomButton(
//                     isLoading: authCubit.state.isLoadingVerificationCode,
//                     width: double.infinity,
//                     onPressed: () {
//                       print(widget.code);
//                       // submitOtp();
//                       if (otpCode == widget.code) {
//                         submitOtp();
//                       } else {
//                         showError(
//                             customMessage: context.translate.enterValidOtp);
//                       }
//                     },
//                     child: TextApp(
//                       text: context.translate.confirm,
//                       style: context.textStyleButton,
//                     )),
//                 SizedBox(
//                   height: 22.h,
//                 ),
//                 Spacer(
//                   flex: 10,
//                 ),
//                 TermsConditionComponent(),
//                 Spacer(
//                   flex: 2,
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     ));
//   }
// }
