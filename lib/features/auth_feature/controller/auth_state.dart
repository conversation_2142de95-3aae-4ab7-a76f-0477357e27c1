part of 'auth_cubit.dart';

@freezed
class AuthState with _$AuthState {
  factory AuthState({
    ///============================================================

    @Default(UserTypeEnum.user) UserTypeEnum userType,

    ///============================================================
    required bool? isLoadingLogin,
    required Failure? isErrorLogin,
    required UserModel? successLogin,

    ///============================================================
    required bool? isLoadingRegister,
    required Failure? isErrorRegister,
    required UserModel? successRegister,

    ///============================================================
    required bool? isLoadingVerificationCode,
    required Failure? isErrorVerificationCode,
    required MessageModel? successVerificationCode,

    ///============================================================
    required bool? isLoadingUpdateProfile,
    required Failure? isErrorUpdateProfile,
    required UserModel? successUpdateProfile,

    ///============================================================
    required bool? isLoadingDeleteAccount,
    required Failure? isErrorDeleteAccount,
    required MessageModel? successDeleteAccount,

    ///============================================================

    required bool? isLoadingGetAppVersions,
    required Failure? errorGetAppVersions,
    required AppVersionModel? successGetAppVersions,
    @Default(true) bool isChecked,
  }) = _$AuthStateImpl;

  factory AuthState.initial() => AuthState(
      isErrorRegister: null,
      isLoadingRegister: null,
      successRegister: null,
      userType: UserTypeEnum.user,
      isLoadingLogin: null,
      isErrorLogin: null,
      successLogin: null,
      isLoadingVerificationCode: null,
      isErrorVerificationCode: null,
      successVerificationCode: null,
      isLoadingUpdateProfile: null,
      isErrorUpdateProfile: null,
      successUpdateProfile: null,
      isLoadingDeleteAccount: null,
      isErrorDeleteAccount: null,
      successDeleteAccount: null,
      errorGetAppVersions: null,
      isLoadingGetAppVersions: null,
      successGetAppVersions: null);
}
