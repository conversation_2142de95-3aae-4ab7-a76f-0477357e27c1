import 'dart:convert';
import 'dart:io';

import 'package:clean_arc/core/domain/entity/enums/server_error_code.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/auth_feature/domain/enum/user_type_enum.dart';
import 'package:clean_arc/features/auth_feature/domain/model/app_version.dart';
import 'package:clean_arc/features/auth_feature/domain/model/message_model/response_message_model.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:clean_arc/features/auth_feature/domain/repository/auth_repository.dart';
import 'package:clean_arc/features/auth_feature/presentation/dialog/updating_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../../core/data/services/shared_prefs/shared_pref.dart';
import '../../../core/data/services/shared_prefs/shared_prefs_key.dart';

part 'auth_cubit.freezed.dart';
part 'auth_state.dart';

@injectable
class AuthCubit extends Cubit<AuthState> {
  AuthCubit(this.repository) : super(AuthState.initial());

  final AuthRepository repository;
  bool rememberMe = true;

  // GlobalKey<FormState> changePasswordKey = GlobalKey<FormState>();

  ///===================
  ///register domain
  ///===================
  int currentRegisterPage = 0;

  // TextEditingController phoneNumberController = TextEditingController();
  // TextEditingController emailController = TextEditingController();
  // TextEditingController nameController = TextEditingController();

  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController surnameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  // reset controllers
  void resetRegisterControllers() {
    fullNameController.clear();
    surnameController.clear();
    emailController.clear();
    passwordController.clear();
    phoneController.clear();
  }

  String? gender;
  int age = 20;

  // Profile editing controllers (reuse existing ones)
  void populateProfileControllers() {
    if (currentUser?.value.user != null) {
      fullNameController.text = currentUser?.value.user?.name ?? '';
      surnameController.text = currentUser?.value.user?.nickname ?? '';
      emailController.text = currentUser?.value.user?.email ?? '';
      phoneController.text = currentUser?.value.user?.phone ?? '';
      gender = currentUser?.value.user?.gender;
      age = currentUser?.value.user?.age ?? 20;
      // Don't populate password for security
      passwordController.clear();
    }
  }

  void toggleChecked() {
    rememberMe = !rememberMe;
    emit(state.copyWith(isChecked: rememberMe));
  }

  void setUserType(UserTypeEnum userType) {
    emit(state.copyWith(userType: userType));
  }

  Future<void> login({
    required String email,
    required String password,
  }) async {
    emit(state.copyWith(
        isLoadingLogin: true, isErrorLogin: null, successLogin: null));

    final result = await repository.login(email: email, password: password);

    result.fold(
      (failure) {
        emit(state.copyWith(
            isLoadingLogin: false, isErrorLogin: failure, successLogin: null));
      },
      (success) async {
        await setCurrentUser(success.toJson(), rememberMe: rememberMe);
        emit(state.copyWith(
            isLoadingLogin: false, successLogin: success, isErrorLogin: null));
      },
    );
  }

  Future<void> register(// {
      // required String name,
      // required String password,
      // required String email,
      // required String phone,
      // required String gender,
      // required int age,
      // }
      ) async {
    emit(state.copyWith(
        isLoadingRegister: true, isErrorRegister: null, successRegister: null));

    final result = await repository.register(
      name: fullNameController.text,
      nickname: surnameController.text,
      password: passwordController.text,
      email: emailController.text,
      phone: phoneController.text,
      gender: gender ?? 'female',
      age: age,
    );

    result.fold(
      (failure) {
        emit(state.copyWith(
            isLoadingRegister: false,
            isErrorRegister: failure,
            successRegister: null));
      },
      (success) {
        emit(state.copyWith(
            isLoadingRegister: false,
            successRegister: success,
            isErrorRegister: null));
      },
    );
  }

  Future<void> verificationCode(String code) async {
    emit(AuthState.initial());

    emit(state.copyWith(
      isLoadingVerificationCode: true,
      isErrorVerificationCode: null,
      successVerificationCode: null,
    ));
    final result = await repository.verificationCode(
      code: code,
    );
    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingVerificationCode: false,
          isErrorVerificationCode: failure,
          successVerificationCode: null,
        ));
      },
      (result) async {
        await setCurrentUser(result.toJson(),
            // token: state.successLogin?.token ?? '',
            // password: passwordController.text,
            rememberMe: rememberMe);
        await getCurrentUser();

        emit(state.copyWith(
            isLoadingVerificationCode: false,
            isErrorVerificationCode: null,
            successVerificationCode: result));
      },
    );
  }

  ///======save UserData =====

  Future<void> setCurrentUser(Map<String, dynamic>? jsonData,
      {required bool rememberMe}) async {
    try {
      print("jsonDatajsonData ${jsonData}");
      if (jsonData != null) {
        print(
            "SharedPref().containPreference(PrefKeys.000 ${SharedPref().containPreference(PrefKeys.currentUser)}");
        Map<String, dynamic> currentUserJson = jsonData;
        // currentUserJson['password'] = password;
        currentUserJson['rememberMe'] = rememberMe;
        // currentUserJson['token'] = token;
        await SharedPref().setString(
          PrefKeys.currentUser,
          json.encode(jsonData),
        );
        currentUser?.value = UserModel.fromJson(jsonData);
        getCurrentUser();
        emit(state);
      } else {
        currentUser = null;
      }
    } catch (e) {
      throw new Exception(e);
    }
  }

  Future<UserModel?> getCurrentUser() async {
    if (await SharedPref().containPreference(PrefKeys.currentUser)) {
      currentUser?.value = UserModel.fromJson(
          json.decode((await SharedPref().getString(PrefKeys.currentUser)!)));
      print("currentUser?.value  ${currentUser?.value.toJson()}");
      emit(state);
      currentUser?.value.auth = true;
    } else {
      currentUser?.value.auth = false;
    }
    return currentUser?.value;
  }

  Future<void> getAppVersions({
    required BuildContext context,
  }) async {
    String androidAppVersion = "1.0.0";
    String iosAppVersion = "1.0.0";

    emit(state.copyWith(
      isLoadingGetAppVersions: true,
      errorGetAppVersions: null,
      successGetAppVersions: null,
    ));

    final result = await repository.getAppVersions();

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingGetAppVersions: false,
          errorGetAppVersions: failure,
          successGetAppVersions: null,
        ));
      },
      (appVersion) {
        if (isVersionLower(iosAppVersion, appVersion.version) &&
            Platform.isIOS) {
          updateDialog(
            context,
            androidUrl: appVersion.versionUrl ?? '',
            iosUrl: appVersion.versionUrl ?? '',
          );
        } else if (isVersionLower(androidAppVersion, appVersion.versionUrl) &&
            Platform.isAndroid) {
          updateDialog(
            context,
            androidUrl: appVersion.versionUrl ?? '',
            iosUrl: appVersion.versionUrl ?? '',
          );
        }

        emit(state.copyWith(
          isLoadingGetAppVersions: false,
          errorGetAppVersions: null,
          successGetAppVersions: appVersion,
        ));
      },
    );
  }

  bool isVersionLower(String current, String? latest) {
    final c = current.split('.').map(int.parse).toList();
    final l = (latest ?? '0').split('.').map(int.parse).toList();
    for (int i = 0; i < c.length; i++) {
      if (i >= l.length || c[i] < l[i]) return true;
      if (c[i] > l[i]) return false;
    }
    return c.length < l.length;
  }

  Future<void> updateProfile() async {
    if (currentUser?.value.user?.id == null) {
      emit(state.copyWith(
        isLoadingUpdateProfile: false,
        isErrorUpdateProfile: ServerFailure(
          errorCode: ServerErrorCode.wrongInput,
          message: 'User ID not found',
        ),
        successUpdateProfile: null,
      ));
      return;
    }

    emit(state.copyWith(
      isLoadingUpdateProfile: true,
      isErrorUpdateProfile: null,
      successUpdateProfile: null,
    ));

    final result = await repository.updateProfile(
      id: currentUser!.value.user!.id!,
      name: fullNameController.text,
      nickname: surnameController.text,
      email: emailController.text,
      phone: phoneController.text,
      gender: gender ?? 'male',
      age: age,
      password:
          passwordController.text.isEmpty ? null : passwordController.text,
    );

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingUpdateProfile: false,
          isErrorUpdateProfile: failure,
          successUpdateProfile: null,
        ));
      },
      (success) async {
        // Update local storage with new user data, preserving the existing token
        Map<String, dynamic> updatedUserData = success.toJson();

        // Preserve the existing token and expiresAt from current user
        if (currentUser?.value.token != null) {
          updatedUserData['token'] = currentUser!.value.token;
        }
        if (currentUser?.value.expiresAt != null) {
          updatedUserData['expiresAt'] =
              currentUser!.value.expiresAt?.toIso8601String();
        }

        await setCurrentUser(updatedUserData, rememberMe: rememberMe);
        emit(state.copyWith(
          isLoadingUpdateProfile: false,
          successUpdateProfile: success,
          isErrorUpdateProfile: null,
        ));
      },
    );
  }

  Future<void> deleteAccount() async {
    if (currentUser?.value.user?.id == null) {
      emit(state.copyWith(
        isLoadingDeleteAccount: false,
        isErrorDeleteAccount: ServerFailure(
          errorCode: ServerErrorCode.serverError,
          message: 'User ID not found',
        ),
        successDeleteAccount: null,
      ));
      return;
    }

    emit(state.copyWith(
      isLoadingDeleteAccount: true,
      isErrorDeleteAccount: null,
      successDeleteAccount: null,
    ));

    final result = await repository.deleteAccount(
      id: currentUser!.value.user!.id!,
    );

    result.fold(
      (failure) {
        emit(state.copyWith(
          isLoadingDeleteAccount: false,
          isErrorDeleteAccount: failure,
          successDeleteAccount: null,
        ));
      },
      (success) async {
        // Clear local storage
        await SharedPref().removePreference(PrefKeys.currentUser);
        currentUser?.value.auth = false;
        emit(state.copyWith(
          isLoadingDeleteAccount: false,
          successDeleteAccount: success,
          isErrorDeleteAccount: null,
        ));
      },
    );
  }
}

ValueNotifier<UserModel>? currentUser = ValueNotifier(UserModel());
