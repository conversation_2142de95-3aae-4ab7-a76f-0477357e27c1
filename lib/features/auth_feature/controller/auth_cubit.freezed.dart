// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthState {
  ///============================================================
  UserTypeEnum get userType => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingLogin => throw _privateConstructorUsedError;
  Failure? get isErrorLogin => throw _privateConstructorUsedError;
  UserModel? get successLogin => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingRegister => throw _privateConstructorUsedError;
  Failure? get isErrorRegister => throw _privateConstructorUsedError;
  UserModel? get successRegister => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingVerificationCode => throw _privateConstructorUsedError;
  Failure? get isErrorVerificationCode => throw _privateConstructorUsedError;
  MessageModel? get successVerificationCode =>
      throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingUpdateProfile => throw _privateConstructorUsedError;
  Failure? get isErrorUpdateProfile => throw _privateConstructorUsedError;
  UserModel? get successUpdateProfile => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingDeleteAccount => throw _privateConstructorUsedError;
  Failure? get isErrorDeleteAccount => throw _privateConstructorUsedError;
  MessageModel? get successDeleteAccount => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingGetAppVersions => throw _privateConstructorUsedError;
  Failure? get errorGetAppVersions => throw _privateConstructorUsedError;
  AppVersionModel? get successGetAppVersions =>
      throw _privateConstructorUsedError;
  bool get isChecked => throw _privateConstructorUsedError;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthStateCopyWith<AuthState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
  @useResult
  $Res call(
      {UserTypeEnum userType,
      bool? isLoadingLogin,
      Failure? isErrorLogin,
      UserModel? successLogin,
      bool? isLoadingRegister,
      Failure? isErrorRegister,
      UserModel? successRegister,
      bool? isLoadingVerificationCode,
      Failure? isErrorVerificationCode,
      MessageModel? successVerificationCode,
      bool? isLoadingUpdateProfile,
      Failure? isErrorUpdateProfile,
      UserModel? successUpdateProfile,
      bool? isLoadingDeleteAccount,
      Failure? isErrorDeleteAccount,
      MessageModel? successDeleteAccount,
      bool? isLoadingGetAppVersions,
      Failure? errorGetAppVersions,
      AppVersionModel? successGetAppVersions,
      bool isChecked});
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userType = null,
    Object? isLoadingLogin = freezed,
    Object? isErrorLogin = freezed,
    Object? successLogin = freezed,
    Object? isLoadingRegister = freezed,
    Object? isErrorRegister = freezed,
    Object? successRegister = freezed,
    Object? isLoadingVerificationCode = freezed,
    Object? isErrorVerificationCode = freezed,
    Object? successVerificationCode = freezed,
    Object? isLoadingUpdateProfile = freezed,
    Object? isErrorUpdateProfile = freezed,
    Object? successUpdateProfile = freezed,
    Object? isLoadingDeleteAccount = freezed,
    Object? isErrorDeleteAccount = freezed,
    Object? successDeleteAccount = freezed,
    Object? isLoadingGetAppVersions = freezed,
    Object? errorGetAppVersions = freezed,
    Object? successGetAppVersions = freezed,
    Object? isChecked = null,
  }) {
    return _then(_value.copyWith(
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as UserTypeEnum,
      isLoadingLogin: freezed == isLoadingLogin
          ? _value.isLoadingLogin
          : isLoadingLogin // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorLogin: freezed == isErrorLogin
          ? _value.isErrorLogin
          : isErrorLogin // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successLogin: freezed == successLogin
          ? _value.successLogin
          : successLogin // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      isLoadingRegister: freezed == isLoadingRegister
          ? _value.isLoadingRegister
          : isLoadingRegister // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorRegister: freezed == isErrorRegister
          ? _value.isErrorRegister
          : isErrorRegister // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successRegister: freezed == successRegister
          ? _value.successRegister
          : successRegister // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      isLoadingVerificationCode: freezed == isLoadingVerificationCode
          ? _value.isLoadingVerificationCode
          : isLoadingVerificationCode // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorVerificationCode: freezed == isErrorVerificationCode
          ? _value.isErrorVerificationCode
          : isErrorVerificationCode // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successVerificationCode: freezed == successVerificationCode
          ? _value.successVerificationCode
          : successVerificationCode // ignore: cast_nullable_to_non_nullable
              as MessageModel?,
      isLoadingUpdateProfile: freezed == isLoadingUpdateProfile
          ? _value.isLoadingUpdateProfile
          : isLoadingUpdateProfile // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorUpdateProfile: freezed == isErrorUpdateProfile
          ? _value.isErrorUpdateProfile
          : isErrorUpdateProfile // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successUpdateProfile: freezed == successUpdateProfile
          ? _value.successUpdateProfile
          : successUpdateProfile // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      isLoadingDeleteAccount: freezed == isLoadingDeleteAccount
          ? _value.isLoadingDeleteAccount
          : isLoadingDeleteAccount // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorDeleteAccount: freezed == isErrorDeleteAccount
          ? _value.isErrorDeleteAccount
          : isErrorDeleteAccount // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successDeleteAccount: freezed == successDeleteAccount
          ? _value.successDeleteAccount
          : successDeleteAccount // ignore: cast_nullable_to_non_nullable
              as MessageModel?,
      isLoadingGetAppVersions: freezed == isLoadingGetAppVersions
          ? _value.isLoadingGetAppVersions
          : isLoadingGetAppVersions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetAppVersions: freezed == errorGetAppVersions
          ? _value.errorGetAppVersions
          : errorGetAppVersions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetAppVersions: freezed == successGetAppVersions
          ? _value.successGetAppVersions
          : successGetAppVersions // ignore: cast_nullable_to_non_nullable
              as AppVersionModel?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$$AuthStateImplImplCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$$$AuthStateImplImplCopyWith(_$$AuthStateImplImpl value,
          $Res Function(_$$AuthStateImplImpl) then) =
      __$$$AuthStateImplImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {UserTypeEnum userType,
      bool? isLoadingLogin,
      Failure? isErrorLogin,
      UserModel? successLogin,
      bool? isLoadingRegister,
      Failure? isErrorRegister,
      UserModel? successRegister,
      bool? isLoadingVerificationCode,
      Failure? isErrorVerificationCode,
      MessageModel? successVerificationCode,
      bool? isLoadingUpdateProfile,
      Failure? isErrorUpdateProfile,
      UserModel? successUpdateProfile,
      bool? isLoadingDeleteAccount,
      Failure? isErrorDeleteAccount,
      MessageModel? successDeleteAccount,
      bool? isLoadingGetAppVersions,
      Failure? errorGetAppVersions,
      AppVersionModel? successGetAppVersions,
      bool isChecked});
}

/// @nodoc
class __$$$AuthStateImplImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$$AuthStateImplImpl>
    implements _$$$AuthStateImplImplCopyWith<$Res> {
  __$$$AuthStateImplImplCopyWithImpl(
      _$$AuthStateImplImpl _value, $Res Function(_$$AuthStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userType = null,
    Object? isLoadingLogin = freezed,
    Object? isErrorLogin = freezed,
    Object? successLogin = freezed,
    Object? isLoadingRegister = freezed,
    Object? isErrorRegister = freezed,
    Object? successRegister = freezed,
    Object? isLoadingVerificationCode = freezed,
    Object? isErrorVerificationCode = freezed,
    Object? successVerificationCode = freezed,
    Object? isLoadingUpdateProfile = freezed,
    Object? isErrorUpdateProfile = freezed,
    Object? successUpdateProfile = freezed,
    Object? isLoadingDeleteAccount = freezed,
    Object? isErrorDeleteAccount = freezed,
    Object? successDeleteAccount = freezed,
    Object? isLoadingGetAppVersions = freezed,
    Object? errorGetAppVersions = freezed,
    Object? successGetAppVersions = freezed,
    Object? isChecked = null,
  }) {
    return _then(_$$AuthStateImplImpl(
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as UserTypeEnum,
      isLoadingLogin: freezed == isLoadingLogin
          ? _value.isLoadingLogin
          : isLoadingLogin // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorLogin: freezed == isErrorLogin
          ? _value.isErrorLogin
          : isErrorLogin // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successLogin: freezed == successLogin
          ? _value.successLogin
          : successLogin // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      isLoadingRegister: freezed == isLoadingRegister
          ? _value.isLoadingRegister
          : isLoadingRegister // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorRegister: freezed == isErrorRegister
          ? _value.isErrorRegister
          : isErrorRegister // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successRegister: freezed == successRegister
          ? _value.successRegister
          : successRegister // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      isLoadingVerificationCode: freezed == isLoadingVerificationCode
          ? _value.isLoadingVerificationCode
          : isLoadingVerificationCode // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorVerificationCode: freezed == isErrorVerificationCode
          ? _value.isErrorVerificationCode
          : isErrorVerificationCode // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successVerificationCode: freezed == successVerificationCode
          ? _value.successVerificationCode
          : successVerificationCode // ignore: cast_nullable_to_non_nullable
              as MessageModel?,
      isLoadingUpdateProfile: freezed == isLoadingUpdateProfile
          ? _value.isLoadingUpdateProfile
          : isLoadingUpdateProfile // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorUpdateProfile: freezed == isErrorUpdateProfile
          ? _value.isErrorUpdateProfile
          : isErrorUpdateProfile // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successUpdateProfile: freezed == successUpdateProfile
          ? _value.successUpdateProfile
          : successUpdateProfile // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      isLoadingDeleteAccount: freezed == isLoadingDeleteAccount
          ? _value.isLoadingDeleteAccount
          : isLoadingDeleteAccount // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorDeleteAccount: freezed == isErrorDeleteAccount
          ? _value.isErrorDeleteAccount
          : isErrorDeleteAccount // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successDeleteAccount: freezed == successDeleteAccount
          ? _value.successDeleteAccount
          : successDeleteAccount // ignore: cast_nullable_to_non_nullable
              as MessageModel?,
      isLoadingGetAppVersions: freezed == isLoadingGetAppVersions
          ? _value.isLoadingGetAppVersions
          : isLoadingGetAppVersions // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorGetAppVersions: freezed == errorGetAppVersions
          ? _value.errorGetAppVersions
          : errorGetAppVersions // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetAppVersions: freezed == successGetAppVersions
          ? _value.successGetAppVersions
          : successGetAppVersions // ignore: cast_nullable_to_non_nullable
              as AppVersionModel?,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$$AuthStateImplImpl implements _$AuthStateImpl {
  _$$AuthStateImplImpl(
      {this.userType = UserTypeEnum.user,
      required this.isLoadingLogin,
      required this.isErrorLogin,
      required this.successLogin,
      required this.isLoadingRegister,
      required this.isErrorRegister,
      required this.successRegister,
      required this.isLoadingVerificationCode,
      required this.isErrorVerificationCode,
      required this.successVerificationCode,
      required this.isLoadingUpdateProfile,
      required this.isErrorUpdateProfile,
      required this.successUpdateProfile,
      required this.isLoadingDeleteAccount,
      required this.isErrorDeleteAccount,
      required this.successDeleteAccount,
      required this.isLoadingGetAppVersions,
      required this.errorGetAppVersions,
      required this.successGetAppVersions,
      this.isChecked = true});

  ///============================================================
  @override
  @JsonKey()
  final UserTypeEnum userType;

  ///============================================================
  @override
  final bool? isLoadingLogin;
  @override
  final Failure? isErrorLogin;
  @override
  final UserModel? successLogin;

  ///============================================================
  @override
  final bool? isLoadingRegister;
  @override
  final Failure? isErrorRegister;
  @override
  final UserModel? successRegister;

  ///============================================================
  @override
  final bool? isLoadingVerificationCode;
  @override
  final Failure? isErrorVerificationCode;
  @override
  final MessageModel? successVerificationCode;

  ///============================================================
  @override
  final bool? isLoadingUpdateProfile;
  @override
  final Failure? isErrorUpdateProfile;
  @override
  final UserModel? successUpdateProfile;

  ///============================================================
  @override
  final bool? isLoadingDeleteAccount;
  @override
  final Failure? isErrorDeleteAccount;
  @override
  final MessageModel? successDeleteAccount;

  ///============================================================
  @override
  final bool? isLoadingGetAppVersions;
  @override
  final Failure? errorGetAppVersions;
  @override
  final AppVersionModel? successGetAppVersions;
  @override
  @JsonKey()
  final bool isChecked;

  @override
  String toString() {
    return 'AuthState(userType: $userType, isLoadingLogin: $isLoadingLogin, isErrorLogin: $isErrorLogin, successLogin: $successLogin, isLoadingRegister: $isLoadingRegister, isErrorRegister: $isErrorRegister, successRegister: $successRegister, isLoadingVerificationCode: $isLoadingVerificationCode, isErrorVerificationCode: $isErrorVerificationCode, successVerificationCode: $successVerificationCode, isLoadingUpdateProfile: $isLoadingUpdateProfile, isErrorUpdateProfile: $isErrorUpdateProfile, successUpdateProfile: $successUpdateProfile, isLoadingDeleteAccount: $isLoadingDeleteAccount, isErrorDeleteAccount: $isErrorDeleteAccount, successDeleteAccount: $successDeleteAccount, isLoadingGetAppVersions: $isLoadingGetAppVersions, errorGetAppVersions: $errorGetAppVersions, successGetAppVersions: $successGetAppVersions, isChecked: $isChecked)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$$AuthStateImplImpl &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.isLoadingLogin, isLoadingLogin) ||
                other.isLoadingLogin == isLoadingLogin) &&
            (identical(other.isErrorLogin, isErrorLogin) ||
                other.isErrorLogin == isErrorLogin) &&
            (identical(other.successLogin, successLogin) ||
                other.successLogin == successLogin) &&
            (identical(other.isLoadingRegister, isLoadingRegister) ||
                other.isLoadingRegister == isLoadingRegister) &&
            (identical(other.isErrorRegister, isErrorRegister) ||
                other.isErrorRegister == isErrorRegister) &&
            (identical(other.successRegister, successRegister) ||
                other.successRegister == successRegister) &&
            (identical(other.isLoadingVerificationCode,
                    isLoadingVerificationCode) ||
                other.isLoadingVerificationCode == isLoadingVerificationCode) &&
            (identical(
                    other.isErrorVerificationCode, isErrorVerificationCode) ||
                other.isErrorVerificationCode == isErrorVerificationCode) &&
            (identical(
                    other.successVerificationCode, successVerificationCode) ||
                other.successVerificationCode == successVerificationCode) &&
            (identical(other.isLoadingUpdateProfile, isLoadingUpdateProfile) ||
                other.isLoadingUpdateProfile == isLoadingUpdateProfile) &&
            (identical(other.isErrorUpdateProfile, isErrorUpdateProfile) ||
                other.isErrorUpdateProfile == isErrorUpdateProfile) &&
            (identical(other.successUpdateProfile, successUpdateProfile) ||
                other.successUpdateProfile == successUpdateProfile) &&
            (identical(other.isLoadingDeleteAccount, isLoadingDeleteAccount) ||
                other.isLoadingDeleteAccount == isLoadingDeleteAccount) &&
            (identical(other.isErrorDeleteAccount, isErrorDeleteAccount) ||
                other.isErrorDeleteAccount == isErrorDeleteAccount) &&
            (identical(other.successDeleteAccount, successDeleteAccount) ||
                other.successDeleteAccount == successDeleteAccount) &&
            (identical(
                    other.isLoadingGetAppVersions, isLoadingGetAppVersions) ||
                other.isLoadingGetAppVersions == isLoadingGetAppVersions) &&
            (identical(other.errorGetAppVersions, errorGetAppVersions) ||
                other.errorGetAppVersions == errorGetAppVersions) &&
            (identical(other.successGetAppVersions, successGetAppVersions) ||
                other.successGetAppVersions == successGetAppVersions) &&
            (identical(other.isChecked, isChecked) ||
                other.isChecked == isChecked));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        userType,
        isLoadingLogin,
        isErrorLogin,
        successLogin,
        isLoadingRegister,
        isErrorRegister,
        successRegister,
        isLoadingVerificationCode,
        isErrorVerificationCode,
        successVerificationCode,
        isLoadingUpdateProfile,
        isErrorUpdateProfile,
        successUpdateProfile,
        isLoadingDeleteAccount,
        isErrorDeleteAccount,
        successDeleteAccount,
        isLoadingGetAppVersions,
        errorGetAppVersions,
        successGetAppVersions,
        isChecked
      ]);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$$AuthStateImplImplCopyWith<_$$AuthStateImplImpl> get copyWith =>
      __$$$AuthStateImplImplCopyWithImpl<_$$AuthStateImplImpl>(
          this, _$identity);
}

abstract class _$AuthStateImpl implements AuthState {
  factory _$AuthStateImpl(
      {final UserTypeEnum userType,
      required final bool? isLoadingLogin,
      required final Failure? isErrorLogin,
      required final UserModel? successLogin,
      required final bool? isLoadingRegister,
      required final Failure? isErrorRegister,
      required final UserModel? successRegister,
      required final bool? isLoadingVerificationCode,
      required final Failure? isErrorVerificationCode,
      required final MessageModel? successVerificationCode,
      required final bool? isLoadingUpdateProfile,
      required final Failure? isErrorUpdateProfile,
      required final UserModel? successUpdateProfile,
      required final bool? isLoadingDeleteAccount,
      required final Failure? isErrorDeleteAccount,
      required final MessageModel? successDeleteAccount,
      required final bool? isLoadingGetAppVersions,
      required final Failure? errorGetAppVersions,
      required final AppVersionModel? successGetAppVersions,
      final bool isChecked}) = _$$AuthStateImplImpl;

  ///============================================================
  @override
  UserTypeEnum get userType;

  ///============================================================
  @override
  bool? get isLoadingLogin;
  @override
  Failure? get isErrorLogin;
  @override
  UserModel? get successLogin;

  ///============================================================
  @override
  bool? get isLoadingRegister;
  @override
  Failure? get isErrorRegister;
  @override
  UserModel? get successRegister;

  ///============================================================
  @override
  bool? get isLoadingVerificationCode;
  @override
  Failure? get isErrorVerificationCode;
  @override
  MessageModel? get successVerificationCode;

  ///============================================================
  @override
  bool? get isLoadingUpdateProfile;
  @override
  Failure? get isErrorUpdateProfile;
  @override
  UserModel? get successUpdateProfile;

  ///============================================================
  @override
  bool? get isLoadingDeleteAccount;
  @override
  Failure? get isErrorDeleteAccount;
  @override
  MessageModel? get successDeleteAccount;

  ///============================================================
  @override
  bool? get isLoadingGetAppVersions;
  @override
  Failure? get errorGetAppVersions;
  @override
  AppVersionModel? get successGetAppVersions;
  @override
  bool get isChecked;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$$AuthStateImplImplCopyWith<_$$AuthStateImplImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
