import 'package:clean_arc/features/user_layout/lip_feature/domain/model/book_cach_model.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/concern_model.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/termination_model.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/adapters.dart';

class BookManage {
  static final ValueNotifier<Set<int>> savedBooks = ValueNotifier({});
  static final ValueNotifier<Set<int>> savedTreatments = ValueNotifier({});
  static final ValueNotifier<Set<int>> savedMeditations = ValueNotifier({});
  static final ValueNotifier<Set<int>> savedLectures = ValueNotifier({});

  static final ValueNotifier<Set<int>> savedConcerns = ValueNotifier({});

  static Future<void> init() async {
    await Hive.initFlutter();

    // تسجيل الـ Adapters
    Hive.registerAdapter(BooksCashModelAdapter());
    Hive.registerAdapter(TreatmentCashModelAdapter());
    Hive.registerAdapter(MeditationCashModelAdapter());
    Hive.registerAdapter(LectureCashModelAdapter());
    Hive.registerAdapter(DiscountCashOptionAdapter());
    Hive.registerAdapter(PageCashModelAdapter());

    // تسجيل الـ Adapters
    Hive.registerAdapter(QuestionsCashModelAdapter());
    Hive.registerAdapter(ThoughtCashModelAdapter());

    // فتح الـ Boxes
    final booksBox = await Hive.openBox<BooksCashModel>('books');
    final treatmentsBox = await Hive.openBox<TreatmentCashModel>('treatments');
    final meditationsBox =
        await Hive.openBox<MeditationCashModel>('meditations');
    final lecturesBox = await Hive.openBox<LectureCashModel>('lectures');
    final concernsBox = await Hive.openBox<QuestionsCashModel>('concerns');

    // تحميل البيانات المحفوظة

    savedConcerns.value = concernsBox.keys.cast<int>().toSet();

    savedBooks.value = booksBox.keys.cast<int>().toSet();
    savedTreatments.value = treatmentsBox.keys.cast<int>().toSet();
    savedMeditations.value = meditationsBox.keys.cast<int>().toSet();
    savedLectures.value = lecturesBox.keys.cast<int>().toSet();
  }

  static Future<void> toggleConcern(QuestionsCashModel concern) async {
    final box = Hive.box<QuestionsCashModel>('concerns');

    if (savedConcerns.value.contains(concern.id)) {
      await box.delete(concern.id);
      savedConcerns.value.remove(concern.id);
    } else {
      await box.put(concern.id, concern);
      savedConcerns.value.add(concern.id ?? 0);
    }

    savedConcerns.notifyListeners();
  }

  static bool isConcernSaved(int concernId) =>
      savedConcerns.value.contains(concernId);

  static Future<void> toggleBook(BooksCashModel book) async {
    final box = Hive.box<BooksCashModel>('books');

    if (savedBooks.value.contains(book.id)) {
      await box.delete(book.id);
      savedBooks.value.remove(book.id);
    } else {
      await box.put(book.id, book);
      savedBooks.value.add(book.id);
    }

    savedBooks.notifyListeners();
  }

  static Future<void> toggleTreatment(TreatmentCashModel treatment) async {
    print("sdfdsadsd");
    final box = Hive.box<TreatmentCashModel>('treatments');

    if (savedTreatments.value.contains(treatment.id)) {
      print('asdasas');
      await box.delete(treatment.id);
      savedTreatments.value.remove(treatment.id);
    } else {
      await box.put(treatment.id, treatment);
      savedTreatments.value.add(treatment.id);
    }

    savedTreatments.notifyListeners();
  }

  static Future<void> toggleMeditation(MeditationCashModel meditation) async {
    final box = Hive.box<MeditationCashModel>('meditations');

    if (savedMeditations.value.contains(meditation.id)) {
      await box.delete(meditation.id);
      savedMeditations.value.remove(meditation.id);
    } else {
      await box.put(meditation.id, meditation);
      savedMeditations.value.add(meditation.id);
    }

    savedMeditations.notifyListeners();
  }

  static bool isBookSaved(int bookId) => savedBooks.value.contains(bookId);

  static bool isTreatmentSaved(int treatmentId) =>
      savedTreatments.value.contains(treatmentId);

  static bool isMeditationSaved(int meditationId) =>
      savedMeditations.value.contains(meditationId);

  static Future<void> toggleLecture(LectureCashModel lecture) async {
    final box = Hive.box<LectureCashModel>('lectures');

    if (savedLectures.value.contains(lecture.id)) {
      await box.delete(lecture.id);
      savedLectures.value.remove(lecture.id);
    } else {
      await box.put(lecture.id, lecture);
      savedLectures.value.add(lecture.id);
    }

    savedLectures.notifyListeners();
  }

  static bool isLectureSaved(int lectureId) =>
      savedLectures.value.contains(lectureId);
}
