import 'package:hive/hive.dart';

part 'concern_model.g.dart';

@HiveType(typeId: 9)
class ConcernCashModel extends HiveObject {
  ConcernCashModel({
    required this.id,
    required this.name,
    required this.image,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.thoughts,
  });

  @HiveField(0)
  final int? id;

  @HiveField(1)
  final String? name;

  @HiveField(2)
  final String? image;

  @HiveField(3)
  final int? isActive;

  @HiveField(4)
  final DateTime? createdAt;

  @HiveField(5)
  final DateTime? updatedAt;

  @HiveField(6)
  final List<ThoughtCashModel> thoughts;

  factory ConcernCashModel.fromJson(Map<String, dynamic> json) {
    return ConcernCashModel(
      id: json["id"],
      name: json["name"],
      image: json["image"],
      isActive: json["is_active"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      thoughts: json["thoughts"] == null
          ? []
          : List<ThoughtCashModel>.from(
              json["thoughts"].map((x) => ThoughtCashModel.fromJson(x))),
    );
  }
}

@HiveType(typeId: 10)
class ThoughtCashModel extends HiveObject {
  ThoughtCashModel({
    required this.id,
    required this.concernId,
    required this.content,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.questions,
  });

  @HiveField(0)
  final int? id;

  @HiveField(1)
  final int? concernId;

  @HiveField(2)
  final String? content;

  @HiveField(3)
  final int? isActive;

  @HiveField(4)
  final DateTime? createdAt;

  @HiveField(5)
  final DateTime? updatedAt;

  @HiveField(6)
  final List<QuestionsCashModel> questions;

  factory ThoughtCashModel.fromJson(Map<String, dynamic> json) {
    return ThoughtCashModel(
      id: json["id"],
      concernId: json["concern_id"],
      content: json["content"],
      isActive: json["is_active"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      questions: json["questions"] == null
          ? []
          : List<QuestionsCashModel>.from(
              json["questions"].map((x) => QuestionsCashModel.fromJson(x))),
    );
  }
}

@HiveType(typeId: 11)
class QuestionsCashModel extends HiveObject {
  QuestionsCashModel({
    required this.id,
    required this.thoughtId,
    required this.questionText,
    required this.isPositiveRoute,
    required this.isShow,
    required this.yesRouteId,
    required this.noRouteId,
    required this.createdAt,
    required this.updatedAt,
    required this.questionType,
    required this.answers,
  });

  @HiveField(0)
  final int? id;

  @HiveField(1)
  final int? thoughtId;

  @HiveField(2)
  final String? questionText;

  @HiveField(3)
  final int? isPositiveRoute;

  @HiveField(4)
  final int? isShow;

  @HiveField(5)
  final int? yesRouteId;

  @HiveField(6)
  final int? noRouteId;

  @HiveField(7)
  final DateTime? createdAt;

  @HiveField(8)
  final DateTime? updatedAt;

  @HiveField(9)
  final String? questionType;

  @HiveField(10)
  final List<dynamic> answers;

  factory QuestionsCashModel.fromJson(Map<String, dynamic> json) {
    return QuestionsCashModel(
      id: json["id"],
      thoughtId: json["thought_id"],
      questionText: json["question_text"],
      isPositiveRoute: json["is_positive_route"],
      isShow: json["is_show"],
      yesRouteId: json["yes_route_id"],
      noRouteId: json["no_route_id"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      questionType: json["question_type"],
      answers: json["answers"] == null
          ? []
          : List<dynamic>.from(json["answers"].map((x) => x)),
    );
  }
}
