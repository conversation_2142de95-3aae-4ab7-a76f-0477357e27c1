// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'book_cach_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BooksCashModelAdapter extends TypeAdapter<BooksCashModel> {
  @override
  final int typeId = 1;

  @override
  BooksCashModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BooksCashModel(
      id: fields[0] as int,
      name: fields[1] as String,
      description: fields[2] as String,
      originalPrice: fields[3] as String,
      image: fields[4] as String,
      imageSecondary: fields[5] as String,
      isExists: fields[6] as num,
      createdAt: fields[7] as DateTime,
      updatedAt: fields[8] as DateTime,
      totalPages: fields[9] as num,
      discountOptions: (fields[10] as List).cast<DiscountCashOption>(),
      pages: (fields[11] as List).cast<PageCashModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, BooksCashModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.originalPrice)
      ..writeByte(4)
      ..write(obj.image)
      ..writeByte(5)
      ..write(obj.imageSecondary)
      ..writeByte(6)
      ..write(obj.isExists)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.totalPages)
      ..writeByte(10)
      ..write(obj.discountOptions)
      ..writeByte(11)
      ..write(obj.pages);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BooksCashModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DiscountCashOptionAdapter extends TypeAdapter<DiscountCashOption> {
  @override
  final int typeId = 2;

  @override
  DiscountCashOption read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DiscountCashOption(
      subscriptionType: fields[0] as String,
      discountPercentage: fields[1] as num,
      discountedPrice: fields[2] as num,
    );
  }

  @override
  void write(BinaryWriter writer, DiscountCashOption obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.subscriptionType)
      ..writeByte(1)
      ..write(obj.discountPercentage)
      ..writeByte(2)
      ..write(obj.discountedPrice);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DiscountCashOptionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PageCashModelAdapter extends TypeAdapter<PageCashModel> {
  @override
  final int typeId = 3;

  @override
  PageCashModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PageCashModel(
      bookId: fields[0] as int,
      pageImage: fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, PageCashModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.bookId)
      ..writeByte(1)
      ..write(obj.pageImage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PageCashModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
