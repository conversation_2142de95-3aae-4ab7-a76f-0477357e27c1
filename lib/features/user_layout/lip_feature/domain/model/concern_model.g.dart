// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'concern_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ConcernCashModelAdapter extends TypeAdapter<ConcernCashModel> {
  @override
  final int typeId = 9;

  @override
  ConcernCashModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ConcernCashModel(
      id: fields[0] as int?,
      name: fields[1] as String?,
      image: fields[2] as String?,
      isActive: fields[3] as int?,
      createdAt: fields[4] as DateTime?,
      updatedAt: fields[5] as DateTime?,
      thoughts: (fields[6] as List).cast<ThoughtCashModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, ConcernCashModel obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.image)
      ..writeByte(3)
      ..write(obj.isActive)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.updatedAt)
      ..writeByte(6)
      ..write(obj.thoughts);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConcernCashModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ThoughtCashModelAdapter extends TypeAdapter<ThoughtCashModel> {
  @override
  final int typeId = 10;

  @override
  ThoughtCashModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ThoughtCashModel(
      id: fields[0] as int?,
      concernId: fields[1] as int?,
      content: fields[2] as String?,
      isActive: fields[3] as int?,
      createdAt: fields[4] as DateTime?,
      updatedAt: fields[5] as DateTime?,
      questions: (fields[6] as List).cast<QuestionsCashModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, ThoughtCashModel obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.concernId)
      ..writeByte(2)
      ..write(obj.content)
      ..writeByte(3)
      ..write(obj.isActive)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.updatedAt)
      ..writeByte(6)
      ..write(obj.questions);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ThoughtCashModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class QuestionsCashModelAdapter extends TypeAdapter<QuestionsCashModel> {
  @override
  final int typeId = 11;

  @override
  QuestionsCashModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuestionsCashModel(
      id: fields[0] as int?,
      thoughtId: fields[1] as int?,
      questionText: fields[2] as String?,
      isPositiveRoute: fields[3] as int?,
      isShow: fields[4] as int?,
      yesRouteId: fields[5] as int?,
      noRouteId: fields[6] as int?,
      createdAt: fields[7] as DateTime?,
      updatedAt: fields[8] as DateTime?,
      questionType: fields[9] as String?,
      answers: (fields[10] as List).cast<dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, QuestionsCashModel obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.thoughtId)
      ..writeByte(2)
      ..write(obj.questionText)
      ..writeByte(3)
      ..write(obj.isPositiveRoute)
      ..writeByte(4)
      ..write(obj.isShow)
      ..writeByte(5)
      ..write(obj.yesRouteId)
      ..writeByte(6)
      ..write(obj.noRouteId)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.questionType)
      ..writeByte(10)
      ..write(obj.answers);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuestionsCashModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
