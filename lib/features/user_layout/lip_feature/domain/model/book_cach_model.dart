import 'package:hive/hive.dart';

part 'book_cach_model.g.dart';

@HiveType(typeId: 1)
class BooksCashModel extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String originalPrice;

  @HiveField(4)
  final String image;

  @HiveField(5)
  final String imageSecondary;

  @HiveField(6)
  final num isExists;

  @HiveField(7)
  final DateTime createdAt;

  @HiveField(8)
  final DateTime updatedAt;

  @HiveField(9)
  final num totalPages;

  @HiveField(10)
  final List<DiscountCashOption> discountOptions;

  @HiveField(11)
  final List<PageCashModel> pages;

  BooksCashModel({
    required this.id,
    required this.name,
    required this.description,
    required this.originalPrice,
    required this.image,
    required this.imageSecondary,
    required this.isExists,
    required this.createdAt,
    required this.updatedAt,
    required this.totalPages,
    required this.discountOptions,
    required this.pages,
  });

  factory BooksCashModel.fromJson(Map<String, dynamic> json) {
    return BooksCashModel(
      id: json["id"],
      name: json["name"],
      description: json["description"]??'',
      originalPrice: json["original_price"],
      image: json["image"],
      imageSecondary: json["image_secondary"],
      isExists: json["is_exists"],
      createdAt: DateTime.tryParse(json["created_at"] ?? "") ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? "") ?? DateTime.now(),
      totalPages: json["totalPages"],
      discountOptions: json["discount_options"] == null
          ? []
          : List<DiscountCashOption>.from(json["discount_options"].map((x) => DiscountCashOption.fromJson(x))),
      pages: json["pages"] == null
          ? []
          : List<PageCashModel>.from(json["pages"].map((x) => PageCashModel.fromJson(x))),
    );
  }
}

@HiveType(typeId: 2)
class DiscountCashOption {
  @HiveField(0)
  final String subscriptionType;

  @HiveField(1)
  final num discountPercentage;

  @HiveField(2)
  final num discountedPrice;

  DiscountCashOption({
    required this.subscriptionType,
    required this.discountPercentage,
    required this.discountedPrice,
  });

  factory DiscountCashOption.fromJson(Map<String, dynamic> json) {
    return DiscountCashOption(
      subscriptionType: json["subscription_type"],
      discountPercentage: json["discount_percentage"],
      discountedPrice: json["discounted_price"],
    );
  }
}

@HiveType(typeId: 3)
class PageCashModel {
  @HiveField(0)
  final int bookId;

  @HiveField(1)
  final String pageImage;

  PageCashModel({
    required this.bookId,
    required this.pageImage,
  });

  factory PageCashModel.fromJson(Map<String, dynamic> json) {
    return PageCashModel(
      bookId: json["book_id"],
      pageImage: json["page_image"],
    );
  }
}