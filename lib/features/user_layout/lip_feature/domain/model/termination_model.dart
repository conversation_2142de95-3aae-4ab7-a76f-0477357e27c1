import 'package:hive/hive.dart';

part 'termination_model.g.dart';

@HiveType(typeId: 4)
class TreatmentCashModel extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String text;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String image;

  @HiveField(4)
  final int isActive;

  @HiveField(5)
  final String videoId;

  @HiveField(6)
  final String type;

  @HiveField(7)
  final DateTime createdAt;

  @HiveField(8)
  final DateTime updatedAt;

  TreatmentCashModel({
    required this.id,
    required this.text,
    required this.description,
    required this.image,
    required this.isActive,
    required this.videoId,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TreatmentCashModel.fromJson(Map<String, dynamic> json) {
    return TreatmentCashModel(
      id: json["id"],
      text: json["text"],
      description: json["description"] ?? '',
      image: json["image"],
      isActive: json["is_active"] ?? 0,
      videoId: json["video_id"] ?? '',
      type: json["type"],
      createdAt: DateTime.tryParse(json["created_at"] ?? "") ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? "") ?? DateTime.now(),
    );
  }
}

@HiveType(typeId: 5)
class MeditationCashModel extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String image;

  @HiveField(4)
  final int isActive;

  @HiveField(5)
  final String meditationId;

  @HiveField(6)
  final String audioUrl;

  @HiveField(7)
  final String type;

  @HiveField(8)
  final DateTime createdAt;

  @HiveField(9)
  final DateTime updatedAt;

  MeditationCashModel({
    required this.id,
    required this.title,
    required this.description,
    required this.image,
    required this.isActive,
    required this.meditationId,
    required this.audioUrl,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MeditationCashModel.fromJson(Map<String, dynamic> json) {
    return MeditationCashModel(
      id: json["id"],
      title: json["title"],
      description: json["description"] ?? '',
      image: json["image"],
      isActive: json["is_active"] ?? 0,
      meditationId: json["meditation_id"] ?? '',
      audioUrl: json["audio_url"] ?? '',
      type: json["type"],
      createdAt: DateTime.tryParse(json["created_at"] ?? "") ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? "") ?? DateTime.now(),
    );
  }
}

@HiveType(typeId: 6)
class LectureCashModel extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String text;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String image;

  @HiveField(4)
  final int isActive;

  @HiveField(5)
  final String type;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime updatedAt;

  LectureCashModel({
    required this.id,
    required this.text,
    required this.description,
    required this.image,
    required this.isActive,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LectureCashModel.fromJson(Map<String, dynamic> json) {
    return LectureCashModel(
      id: json["id"],
      text: json["text"],
      description: json["description"] ?? '',
      image: json["image"],
      isActive: json["is_active"] ?? 0,
      type: json["type"],
      createdAt: DateTime.tryParse(json["created_at"] ?? "") ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? "") ?? DateTime.now(),
    );
  }
}
