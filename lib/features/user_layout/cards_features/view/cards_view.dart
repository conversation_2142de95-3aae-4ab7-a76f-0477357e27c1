import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/user_layout/cards_features/item/card_item.dart';
import 'package:flutter/material.dart';
@RoutePage()

class CardsView extends StatelessWidget {
  const CardsView({super.key});

  static const path = '/CardsView';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TitleAppBar(title: context.translate.myCards),
      ),
      body: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(children: [
                  Container(
                    decoration: BoxDecoration(
                        color: context.color.fillColor,
                        borderRadius:
                            BorderRadius.circular(AppDimensions.radiusDefault)),
                    child: Column(
                      children: [
                        CartItem(
                          expiredDate: '28/12',
                          isSelect: true,
                          title: context.translate.visaCard,
                          cardIcon: AppImages.images.svg.visa
                              .svg(height: 35, width: 35),
                        ),
                        CartItem(
                          expiredDate: '28/12',
                          isSelect: false,
                          title: context.translate.visaCard,
                          cardIcon: AppImages.images.svg.master
                              .svg(height: 35, width: 35),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(
                              AppDimensions.paddingSizeDefault),
                          child: CustomButton(
                            height: 50.h,
                            bgColor: Colors.transparent,
                            // borderRadius: 0,
                            withBorderOnly: true,
                            borderColor: context.color.primaryColor,
                            width: double.infinity,
                            onPressed: () async {
                              context.router.push(AddCardViewRoute());
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add,
                                  color: context.color.primaryColor,
                                  size: 20,
                                ),
                                TextApp(
                                  text: context.translate.addCard,
                                  style: context.textStyle.copyWith(
                                      fontSize: AppDimensions.fontSizeDefault,
                                      color: context.color.primaryColor,
                                      fontWeight: FontWeightHelper.semiBold),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ]),
              ),
            ),
            CustomButton(
              width: context.width,
              onPressed: () {

                context.router.maybePop(true);

              },
              child: TextApp(
                text: context.translate.continues,
                style: context.textStyleButton,
              ),
            )
          ],
        ),
      ),
    );
  }
}
