import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/user_layout/cards_features/creadit_card_componant/flutter_credit_card.dart';
import 'package:flutter/material.dart';
import 'package:u_credit_card/u_credit_card.dart' as u_credit_card;

@RoutePage()
class AddCardView extends StatefulWidget {
  static const String path = '/AddCardView';

  const AddCardView({super.key});

  @override
  State<StatefulWidget> createState() => AddCardViewState();
}

class AddCardViewState extends State<AddCardView> {
  String cardNumber = '';
  String expiryDate = '';
  String cardHolderName = '';
  String cvvCode = '';
  bool isCvvFocused = false;
  bool useGlassMorphism = false;
  bool useFloatingAnimation = true;
  final OutlineInputBorder border = OutlineInputBorder(
    borderSide: BorderSide(
      color: Colors.grey.withOpacity(0.7),
      width: 2.0,
    ),
  );
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(context, title: context.translate.addCard,showNotification: false),
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: <Widget>[
            Center(
              child: u_credit_card.CreditCardUi(
                scale: 1,
                bottomRightColor: context.color.blueColor!,
                cardHolderFullName: cardHolderName.isEmpty
                    ? context.translate.cardHolder
                    : cardHolderName,
                cardNumber:
                    cardNumber.isEmpty ? '0000 0000 0000 0000' : cardNumber,
                validThru: expiryDate.isEmpty ? '00/00' : expiryDate,
                topLeftColor: context.color.darkBlue!,
                placeNfcIconAtTheEnd: true,
                cardType: u_credit_card.CardType.debit,
                cardProviderLogo: AppImages.images.svg.visa.svg(),
              ),
            ),
            SizedBox(
              height: 16,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    CreditCardForm(
                      formKey: formKey,
                      obscureCvv: true,
                      obscureNumber: true,
                      cardNumber: cardNumber,
                      cvvCode: cvvCode,
                      cardHolderName: cardHolderName,
                      expiryDate: expiryDate,
                      inputConfiguration: InputConfiguration(
                        cardNumberDecoration: InputDecoration(
                          labelText: context.translate.cardNumber,
                          hintText: 'credit number',
                        ),
                        expiryDateDecoration: InputDecoration(
                          labelText: context.translate.expiryDate,
                          hintText: 'MM/YY',
                        ),
                        cvvCodeDecoration: InputDecoration(
                          labelText: context.translate.cvv,
                          hintText: 'CVV',
                        ),
                        cardHolderDecoration: InputDecoration(
                            labelText: context.translate.cardHolder,
                            hintText: context.translate.cardHolder),
                      ),
                      onCreditCardModelChange: onCreditCardModelChange,
                    ),
                    Row(
                      children: [
                        Checkbox(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          value: true,
                          onChanged: (value) {},
                        ),
                        TextApp(
                          text: 'Set as default payment card',
                          style: context.textStyle.copyWith(
                            fontWeight: FontWeightHelper.bold,
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
              child: CustomButton(
                  width: context.width,
                  onPressed: () {
                    context.router.maybePop();
                  },
                  child: TextApp(
                    text: context.translate.saveCard,
                    style: context.textStyleButton,
                  )),
            )
          ],
        ),
      ),
    );
  }

  void _onValidate() {
    if (formKey.currentState?.validate() ?? false) {
      print('valid!');
    } else {
      print('invalid!');
    }
  }

  void onCreditCardModelChange(CreditCardModel creditCardModel) {
    setState(() {
      cardNumber = creditCardModel.cardNumber;
      expiryDate = creditCardModel.expiryDate;
      cardHolderName = creditCardModel.cardHolderName;
      cvvCode = creditCardModel.cvvCode;
      isCvvFocused = creditCardModel.isCvvFocused;
    });
  }
}
