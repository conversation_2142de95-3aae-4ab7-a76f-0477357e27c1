import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';

class CartItem extends StatelessWidget {
  // const CartIt({super.key});
  String expiredDate;
  bool isSelect;
  String title;
  Widget cardIcon;

  CartItem({
    required this.expiredDate,
    required this.isSelect,
    required this.title,
    required this.cardIcon,
    super.key,
  });

  // String cardNumber = '****  ****  ****  1234';

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(AppDimensions.paddingSizeDefault),
      decoration: BoxDecoration(
          color: isSelect ? context.color.darkBlue : Colors.transparent,
          borderRadius: BorderRadius.circular(AppDimensions.radiusDefault)),
      padding: const EdgeInsets.all(AppDimensions.paddingSizeSmall),
      child: Row(
        children: [
          cardIcon,
          const SizedBox(
            width: 10,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextApp(
                text: title,
                style: context.textStyle.copyWith(
                    color: !isSelect
                        ? context.color.textColor
                        : context.color.whiteColor,
                    fontWeight: FontWeightHelper.semiBold),
              ),
              const SizedBox(
                height: 5,
              ),
              TextApp(
                text: '${context.translate.expirationDate}: $expiredDate',
                style: context.textStyle.copyWith(
                  color: !isSelect
                      ? context.color.textColor
                      : context.color.whiteColor,
                  fontSize: AppDimensions.fontSizeSmall,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
