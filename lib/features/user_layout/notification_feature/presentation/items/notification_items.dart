import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class NotificationItem extends StatelessWidget {
  Widget image;
  String title;
  String description;

  NotificationItem({
    required this.image,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          image,
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextApp(
                        text: title,
                        style: context.textStyle.copyWith(
                          fontWeight: FontWeight.bold,
                          height: 1.6,
                          fontSize: AppDimensions.fontSizeLarge,
                        ),
                      ),
                    ),
                  ],
                ),
                TextApp(
                  text: description,
                  style: context.textStyle.copyWith(
                    height: 1.2,

                    color: context.color.hintColor,
                    fontSize: AppDimensions.fontSizeSmall,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: TextApp(
              text: '1h',
              style: context.textStyle.copyWith(
                height: 1.2,

                fontWeight: FontWeight.bold,
                color: context.color.descriptionColor,
                fontSize: AppDimensions.fontSizeDefault,
              ),
            ),
          ),

        ],
      ),
    );
  }
}
