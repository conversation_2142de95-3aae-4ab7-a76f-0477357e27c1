import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/user_layout/notification_feature/presentation/items/notification_items.dart';
import 'package:clean_arc/features/user_layout/support_feature/presentation/items/support_items.dart';
import 'package:clean_arc/features/user_layout/support_feature/presentation/items/support_items.dart';
import 'package:flutter/material.dart';

@RoutePage()
class NotificationView extends StatelessWidget {
  const NotificationView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.color.whiteColor,
      appBar: CustomAppBar(context,
          title: "الاشعارات-è",
          showNotification: false,
          additionIcon: Container(
            decoration: BoxDecoration(
                color: context.color.primaryColor,
                borderRadius: BorderRadius.circular(10),
                border:
                    Border.all(color: context.color.primaryColor!, width: 2)),
            margin: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSizeDefault,
                vertical: AppDimensions.paddingSizeSmall),
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingSizeDefault,
            ),
            child: Row(
              children: [
                TextApp(
                  text: '2' + ' ' + context.translate.new2,
                  style: context.textStyle.copyWith(
                      color: context.color.whiteColor,
                      fontSize: AppDimensions.fontSizeDefault,
                      fontWeight: FontWeight.bold),
                ),
              ],
            ),
          )),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextApp(
                    text: context.translate.today,
                    style: context.textStyle.copyWith(
                      // color: context.color.b,
                      fontWeight: FontWeight.bold,
                      fontSize: AppDimensions.fontSizeDefault,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  TextApp(
                    text: context.translate.markAllAsRead,
                    style: context.textStyle.copyWith(
                      // color: context.color.b,
                      color: context.color.primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: AppDimensions.fontSizeDefault,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
              ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                separatorBuilder: (context, index) => SizedBox(
                  height: 16,
                ),
                itemCount: 5,
                itemBuilder: (context, index) {
                  return NotificationItem(
                      image:
                          AppImages.images.svgIcon.notificationRightIcon.svg(),
                      title: "Flash Sale Alert",
                      description:
                          "Lorem ipsum dolor sit amet, consectetu adipiscing elit, sed do eiusmod tempor");
                },
              ),
              SizedBox(
                height: 16,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextApp(
                    text: context.translate.yesterday,
                    style: context.textStyle.copyWith(
                      // color: context.color.b,
                      fontWeight: FontWeight.bold,
                      fontSize: AppDimensions.fontSizeDefault,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  TextApp(
                    text: context.translate.markAllAsRead,
                    style: context.textStyle.copyWith(
                      // color: context.color.b,
                      color: context.color.primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: AppDimensions.fontSizeDefault,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
              ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                separatorBuilder: (context, index) => SizedBox(
                  height: 10,
                ),
                itemCount: 5,
                itemBuilder: (context, index) {
                  return NotificationItem(
                      image:
                          AppImages.images.svgIcon.notificationRightIcon.svg(),
                      title: "Flash Sale Alert",
                      description:
                          "Lorem ipsum dolor sit amet, consectetu adipiscing elit, sed do eiusmod tempor");
                },
              ),
              SizedBox(
                height: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
