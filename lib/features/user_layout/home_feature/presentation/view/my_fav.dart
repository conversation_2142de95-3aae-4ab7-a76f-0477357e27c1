import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:clean_arc/core/presentation/extintions/widget_extensions.dart';
import 'package:clean_arc/core/presentation/widget/custom_dropdown_field.dart';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/book_feature/presentation/componant/book_componant.dart';
import 'package:clean_arc/features/book_feature/presentation/items/book_item.dart';
import 'package:clean_arc/features/favorite_feature/controller/fav_manage_ment.dart';
import 'package:clean_arc/features/favorite_feature/domain/model/audio_model.dart';
import 'package:clean_arc/features/forma_feature/presentation/view/questions_view.dart';
import 'package:clean_arc/features/meditation_feature/presentation/componant/meditation_component.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/persentation/view/singer_profile.dart';
import 'package:clean_arc/features/user_layout/home_feature/controller/user_home_cubit.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/model/music_model.dart';
import 'package:clean_arc/features/user_layout/home_feature/presentation/componant/music_componant.dart';
import 'package:clean_arc/features/concerns_feature/presentation/componant/user_concerns_component.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:hive_flutter/adapters.dart';

class MyFav extends StatefulWidget {
  const MyFav({super.key});

  @override
  State<MyFav> createState() => _MyFavState();
}

class _MyFavState extends State<MyFav> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'مفضلتي',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: SafeArea(
        child: ValueListenableBuilder(
          valueListenable: Hive.box<AudioModel>('favorite_audios').listenable(),
          builder: (context, Box<AudioModel> box, _) {
            final favoriteAudios = box.values.toList();

            if (favoriteAudios.isEmpty) {
              return Center(
                child: Icon(
                  IconlyLight.heart,
                  size: 150,
                  color: context.color.primaryColor,
                ),
              );
            }

            return ListView.builder(
              itemCount: favoriteAudios.length,
              itemBuilder: (context, index) {
                final audio = favoriteAudios[index];

                return InkWell(
                  onTap: () {
print("audio.url ${audio.url}");
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ArtistProfile(
                              title: audio.title ??
                                  '',
                              soung:[
                                SongModel(
                                    album: '',
                                    artist: audio.title,
                                    song: audio.title,
                                    url:
                                     audio.url ?? ''
                                )
                              ]),
                        ));

                  },
                  child: ListTile(
                    leading: Icon(Icons.music_note, color: Colors.blue),
                    title: Text(audio.title??''), // اسم الملف الصوتي
                    subtitle: Text(audio.title??''), // اسم الفنان (اختياري)
                    trailing: IconButton(
                      icon: Icon(IconlyBold.heart, color: Colors.red),
                      onPressed: () async {
                        await FavoritesManager.removeAudioFromFavorites(audio.id??'0');
                      },
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
