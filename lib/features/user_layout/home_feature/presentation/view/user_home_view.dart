import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/book_feature/presentation/componant/book_componant.dart';
import 'package:clean_arc/features/concerns_feature/presentation/componant/user_concerns_component.dart';
import 'package:clean_arc/features/distraction_feature/presentation/componant/distraction_component.dart';
import 'package:clean_arc/features/lecture_feature/presentation/component/lecture_component.dart';
import 'package:clean_arc/features/meditation_feature/presentation/componant/meditation_component.dart';
import 'package:clean_arc/features/treatment_feature/presentation/componant/treatment_component.dart';
import 'package:flutter/material.dart';

class UserHomeView extends StatefulWidget {
  const UserHomeView({super.key});

  @override
  State<UserHomeView> createState() => _UserHomeViewState();
}

class _UserHomeViewState extends State<UserHomeView> {
  @override
  void initState() {
    // context.read<UserHomeCubit>().getFeatureVehicleType();
    // Removed getAppVersions call to prevent multiple navigation issues
    // context.read<AuthCubit>().getAppVersions(context: context);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          AppImages.images.salimIcon.userIcon
                              .image(width: 50, height: 50, fit: BoxFit.cover),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Container(
                              //   width: 200,
                              //   child: CustomDropdownField(
                              //       onChanged: (value) {},
                              //       title: context.translate.howYouFeelToday),
                              // )
                            ],
                          ),
                          // Icon(IconlyLight.notification)
                          InkWell(
                            onTap: () {
                              context.router.push(NotificationViewRoute());
                            },
                            child: AppImages.images.svgIcon.notification
                                .svg(width: 27, height: 27, fit: BoxFit.cover),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),

                      UserConcernsComponent(),

                      UserTreatmentComponent(),

                      LectureComponent(),

                      SizedBox(
                        height: 10,
                      ),

                      MeditationsComponent(),
                      SizedBox(
                        height: 16,
                      ),

                      SizedBox(
                        height: 10,
                      ),
                      // MusicComponent(),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 8,
            ),
            BooksListComponent(),
            Padding(
              padding: const EdgeInsets.all(16),
              child: DistractionComponent(),
            ),
          ],
        ),
      ),
    );
  }
}
