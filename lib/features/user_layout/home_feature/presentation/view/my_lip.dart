import 'package:clean_arc/core/presentation/extintions/widget_extensions.dart';
import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/user_layout/lip_feature/controller/lib_controller.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/book_cach_model.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/concern_model.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/termination_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:hive_flutter/hive_flutter.dart';

class MyLibrary extends StatefulWidget {
  const MyLibrary({super.key});

  @override
  State<MyLibrary> createState() => _MyLibraryState();
}

class _MyLibraryState extends State<MyLibrary> {
  @override
  void initState() {
    // loadBooks();
    super.initState();
  }

  List<BooksCashModel> books = [];

  // /// تحميل الكتب من Hive
  // Future<void> loadBooks() async {
  //   books = await BookManage.getAllBooks();
  //   setState(() {});
  // }
  //
  // /// حذف كتاب وإعادة تحميل القائمة
  // Future<void> deleteBook(int id, String bookName) async {
  //   await BookManage.deleteBook(id);
  //   ScaffoldMessenger.of(context).showSnackBar(
  //     SnackBar(content: Text('$bookName تم حذفه')),
  //   );
  //   await loadBooks();
  // }

  @override
  Widget build(BuildContext context) {
    List<String> repitationPhr = [
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
    ];
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'مكتبتي',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextApp(
                          text: 'جمل الترديد',
                          style: context.textStyle.copyWith(
                            fontSize: AppDimensions.fontSizeLarge,
                            fontWeight: FontWeightHelper.bold,
                          ),
                        ),
                        Divider(),

                        ValueListenableBuilder<Set<int>>(
                          valueListenable: BookManage.savedConcerns,
                          builder: (context, savedBooks, child) {
                            // ✅ استرجاع جميع الكتب المحفوظة من Hive
                            final box =
                                Hive.box<QuestionsCashModel>('concerns');
                            final savedBooksList = savedBooks
                                .map((id) =>
                                    box.get(id)) // جلب كل كتاب بناءً على الـ id
                                .whereType<
                                    QuestionsCashModel>() // ✅ التخلص من القيم null
                                .toList();

                            return savedBooksList.isEmpty
                                ? Center(child: Text('لا توجد جمل ترديد'))
                                : SizedBox(
                                    child: Column(
                                      children: savedBooksList
                                          .map(
                                            (QuestionsCashModel e) => Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Flexible(
                                                  child: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 8.0),
                                                    child: TextApp(
                                                      text: '${e.questionText}',
                                                      style: context.textStyle
                                                          .copyWith(
                                                        fontSize: AppDimensions
                                                            .fontSizeDefault,
                                                        fontWeight:
                                                            FontWeightHelper
                                                                .bold,
                                                      ),
                                                      textAlign:
                                                          TextAlign.start,
                                                    ),
                                                  ),
                                                ),
                                                InkWell(
                                                  onTap: () {
                                                    BookManage.toggleConcern(e);
                                                    setState(() {});
                                                  },
                                                  child: Icon(
                                                    IconlyLight.delete,
                                                    color:
                                                        context.color.redColor,
                                                  ),
                                                )
                                              ],
                                            ),
                                          )
                                          .toList(),
                                    ),
                                  );
                          },
                        ),

                        Divider(),

                        TextApp(
                          text: 'كتبي',
                          style: context.textStyle.copyWith(
                            fontSize: AppDimensions.fontSizeLarge,
                            fontWeight: FontWeightHelper.bold,
                          ),
                        ),
                        Divider(),
                        // Stack(
                        //   children: [
                        //     AppImages.images.demo.book.image(
                        //       height: 200,
                        //       fit: BoxFit.cover,
                        //     ),
                        //     Positioned(
                        //       left: 10,
                        //       top: 10,
                        //       child: CircleAvatar(
                        //           backgroundColor: context.color.primaryColor,
                        //           child: Icon(
                        //             IconlyBold.star,
                        //             color: context.color.yellowColor,
                        //           )),
                        //     )
                        //   ],
                        // ),
                        ValueListenableBuilder<Set<int>>(
                          valueListenable: BookManage.savedBooks,
                          builder: (context, savedBooks, child) {
                            // ✅ استرجاع جميع الكتب المحفوظة من Hive
                            final box = Hive.box<BooksCashModel>('books');
                            final savedBooksList = savedBooks
                                .map((id) =>
                                    box.get(id)) // جلب كل كتاب بناءً على الـ id
                                .whereType<
                                    BooksCashModel>() // ✅ التخلص من القيم null
                                .toList();

                            return savedBooksList.isEmpty
                                ? Center(child: Text('لا توجد كتب محفوظة'))
                                : SizedBox(
                                    height: 220, // ✅ تحديد الارتفاع المناسب
                                    child: ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      // ✅ قائمة أفقية
                                      itemCount: savedBooksList.length,
                                      itemBuilder: (context, index) {
                                        final book = savedBooksList[index];

                                        return Container(
                                          width: 150, // ✅ عرض كل عنصر
                                          margin: EdgeInsets.symmetric(
                                              horizontal: 8),
                                          child: Stack(
                                            children: [
                                              /// ✅ صورة الكتاب

                                              ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                child: CustomCachedNetworkImage(
                                                  imageUrl: book.image,
                                                  height: 200,
                                                  width: double.infinity,
                                                  fit: BoxFit.cover,
                                                ),
                                                // Image.network(
                                                //    ?? '',
                                                //   height: 200,
                                                //   width: double.infinity,
                                                //   fit: BoxFit.cover,
                                                //   errorBuilder: (context, error, stackTrace) =>
                                                //       Container(
                                                //         height: 200,
                                                //         color: Colors.grey,
                                                //         child: Center(child: Text('لا توجد صورة')),
                                                //       ),
                                                // ),
                                              ),

                                              /// ✅ زرار النجمة للحذف
                                              Positioned(
                                                left: 10,
                                                top: 10,
                                                child: GestureDetector(
                                                  onTap: () =>
                                                      BookManage.toggleBook(
                                                          book),
                                                  child: CircleAvatar(
                                                    backgroundColor: Colors.red,
                                                    child: Icon(
                                                      IconlyBold.delete,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  );
                          },
                        ),
                        Divider(),

                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                TextApp(
                                  text: 'الخيال الموجه: طريقك الى الإسترخاء',
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeLarge,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                                SizedBox(height: 5),
                                TextApp(
                                  text: 'انطلق الى حيث الراحة والطمأنينة',
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeDefault,
                                    fontWeight: FontWeightHelper.bold,
                                    color: context.color.grayColor,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 10),
                            Container(
                              height: 200,
                              child: ValueListenableBuilder<
                                  Box<TreatmentCashModel>>(
                                valueListenable:
                                    Hive.box<TreatmentCashModel>('treatments')
                                        .listenable(),
                                builder: (context, box, child) {
                                  final savedMeditations = box.values.toList();

                                  return Container(
                                    height: 200,
                                    child: ListView.separated(
                                      separatorBuilder: (context, index) =>
                                          SizedBox(
                                        width: 5,
                                      ),
                                      scrollDirection: Axis.horizontal,
                                      itemCount: savedMeditations?.length ?? 0,
                                      itemBuilder: (context, index) {
                                        TreatmentCashModel? concern =
                                            savedMeditations?[index];

                                        return InkWell(
                                          onTap: () {
                                            context.pushRoute(
                                                TreatmentVideoViewRoute(
                                                    id: concern!.id!));
                                          },
                                          child: Stack(
                                            children: [
                                              Column(
                                                children: [
                                                  Center(
                                                    child: Container(
                                                      height: 150,
                                                      width: 150,
                                                      child: Center(
                                                          child: CustomCachedNetworkImage(
                                                              borderRadius: 10,
                                                              imageUrl: concern
                                                                      ?.image ??
                                                                  '')),
                                                    ),
                                                  ),
                                                  Center(
                                                      child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.end,
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                vertical: 8.0),
                                                        child: Container(
                                                          width: 150,
                                                          child: TextApp(
                                                            text:
                                                                concern?.text ??
                                                                    '',
                                                            style: context
                                                                .textStyle
                                                                .copyWith(
                                                              fontSize:
                                                                  AppDimensions
                                                                      .fontSizeDefault,
                                                              // color: context.color.whiteColor,
                                                              fontWeight:
                                                                  FontWeightHelper
                                                                      .bold,
                                                            ),
                                                            textAlign: TextAlign
                                                                .center,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  )),
                                                  // Center(
                                                  //   child: Container(
                                                  //     decoration: BoxDecoration(
                                                  //         borderRadius: BorderRadius.circular(10),
                                                  //         color: context.color.textColor?.withOpacity(.5)),
                                                  //     height: 150,
                                                  //     width: 150,
                                                  //     child: Padding(
                                                  //       padding: const EdgeInsets.all(8.0),
                                                  //       child: Center(
                                                  //           child: Column(
                                                  //         crossAxisAlignment: CrossAxisAlignment.start,
                                                  //         mainAxisAlignment: MainAxisAlignment.end,
                                                  //         children: [
                                                  //           Padding(
                                                  //             padding: const EdgeInsets.all(8.0),
                                                  //             child: TextApp(
                                                  //               text: concern?.text ?? '',
                                                  //               style: context.textStyle.copyWith(
                                                  //                 fontSize: AppDimensions.fontSizeDefault,
                                                  //                 color: context.color.whiteColor,
                                                  //                 fontWeight: FontWeightHelper.bold,
                                                  //               ),
                                                  //               textAlign: TextAlign.center,
                                                  //             ),
                                                  //           ),
                                                  //         ],
                                                  //       )),
                                                  //     ),
                                                  //   ),
                                                  // )
                                                ],
                                              ),
                                              InkWell(
                                                onTap: () {
                                                  BookManage.toggleTreatment(
                                                      concern!);
                                                },
                                                child: ValueListenableBuilder<
                                                    Set<int>>(
                                                  valueListenable: BookManage
                                                      .savedTreatments,
                                                  builder: (context, savedBooks,
                                                      child) {
                                                    return Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              8.0),
                                                      child: Icon(
                                                        savedBooks.contains(
                                                                concern?.id)
                                                            ? Icons.bookmark
                                                            : Icons
                                                                .bookmark_border,
                                                        color: context
                                                            .color.yellowColor,
                                                      ),
                                                    );
                                                  },
                                                ),
                                              )
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                        Divider(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                TextApp(
                                  text: 'الخيال الموجه: طريقك الى الإسترخاء',
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeLarge,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                                SizedBox(height: 5),
                                TextApp(
                                  text: 'انطلق الى حيث الراحة والطمأنينة',
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeDefault,
                                    fontWeight: FontWeightHelper.bold,
                                    color: context.color.grayColor,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 10),
                            Container(
                              height: 330,
                              child: ValueListenableBuilder<
                                  Box<MeditationCashModel>>(
                                valueListenable:
                                    Hive.box<MeditationCashModel>('meditations')
                                        .listenable(),
                                builder: (context, box, child) {
                                  final savedMeditations = box.values.toList();

                                  return ListView.separated(
                                    separatorBuilder: (context, index) =>
                                        SizedBox(width: 5),
                                    scrollDirection: Axis.horizontal,
                                    itemCount: savedMeditations.length,
                                    itemBuilder: (context, index) {
                                      MeditationCashModel meditation =
                                          savedMeditations[index];

                                      return InkWell(
                                        onTap: () {},
                                        child: Stack(
                                          children: [
                                            Column(
                                              children: [
                                                Center(
                                                  child: Container(
                                                    height: 280,
                                                    width: 250,
                                                    child:
                                                        CustomCachedNetworkImage(
                                                      imageUrl:
                                                          meditation.image ??
                                                              '',
                                                      fit: BoxFit.fill,
                                                    ),
                                                  ).cornerRadiusWithClipRRect(
                                                      10),
                                                ),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Center(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment.end,
                                                      children: [
                                                        Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8.0),
                                                          child: TextApp(
                                                            text: meditation
                                                                    .title ??
                                                                '',
                                                            style: context
                                                                .textStyle
                                                                .copyWith(
                                                              fontSize:
                                                                  AppDimensions
                                                                      .fontSizeDefault,
                                                              fontWeight:
                                                                  FontWeightHelper
                                                                      .bold,
                                                            ),
                                                            textAlign: TextAlign
                                                                .center,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),

                                            /// أيقونة الحفظ وإزالتها عند الضغط
                                            InkWell(
                                              onTap: () {
                                                BookManage.toggleMeditation(
                                                    meditation);
                                              },
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Icon(
                                                  Icons.bookmark,
                                                  color:
                                                      context.color.yellowColor,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                          ],
                        ),

                        // Lectures Section
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.video_library,
                                  color: context.color.primaryColor,
                                ),
                                SizedBox(width: 8),
                                TextApp(
                                  text: 'المحاضرات المحفوظة',
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeLarge,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 10),
                            Container(
                              height: 200,
                              child:
                                  ValueListenableBuilder<Box<LectureCashModel>>(
                                valueListenable:
                                    Hive.box<LectureCashModel>('lectures')
                                        .listenable(),
                                builder: (context, box, child) {
                                  final savedLectures = box.values.toList();

                                  return savedLectures.isEmpty
                                      ? Center(
                                          child: Text('لا توجد محاضرات محفوظة'))
                                      : ListView.separated(
                                          separatorBuilder: (context, index) =>
                                              SizedBox(width: 5),
                                          scrollDirection: Axis.horizontal,
                                          itemCount: savedLectures.length,
                                          itemBuilder: (context, index) {
                                            LectureCashModel lecture =
                                                savedLectures[index];

                                            return InkWell(
                                              onTap: () {
                                                // Navigate to lecture subcategories
                                                // context.router.push(LectureSubcategoriesViewRoute(lecture: lecture));
                                              },
                                              child: Stack(
                                                children: [
                                                  Column(
                                                    children: [
                                                      Container(
                                                        height: 150,
                                                        width: 150,
                                                        child:
                                                            CustomCachedNetworkImage(
                                                          borderRadius: 10,
                                                          imageUrl:
                                                              lecture.image,
                                                        ),
                                                      ),
                                                      SizedBox(height: 8),
                                                      Container(
                                                        width: 150,
                                                        child: TextApp(
                                                          text: lecture.text,
                                                          style: context
                                                              .textStyle
                                                              .copyWith(
                                                            fontSize: AppDimensions
                                                                .fontSizeDefault,
                                                            fontWeight:
                                                                FontWeightHelper
                                                                    .bold,
                                                          ),
                                                          textAlign:
                                                              TextAlign.center,
                                                          maxLines: 2,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  // Bookmark icon
                                                  Positioned(
                                                    top: 8,
                                                    right: 8,
                                                    child: InkWell(
                                                      onTap: () {
                                                        BookManage
                                                            .toggleLecture(
                                                                lecture);
                                                      },
                                                      child:
                                                          ValueListenableBuilder<
                                                              Set<int>>(
                                                        valueListenable:
                                                            BookManage
                                                                .savedLectures,
                                                        builder: (context,
                                                            savedLectures,
                                                            child) {
                                                          return Container(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    4),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: Colors
                                                                  .black54,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          15),
                                                            ),
                                                            child: Icon(
                                                              savedLectures.contains(
                                                                      lecture
                                                                          .id)
                                                                  ? Icons
                                                                      .bookmark
                                                                  : Icons
                                                                      .bookmark_border,
                                                              color: context
                                                                  .color
                                                                  .yellowColor,
                                                              size: 20,
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        );
                                },
                              ),
                            ),
                          ],
                        )
                        // MeditationsComponent(),
                        // UserConcernsComponent(),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 8,
              ),
              // BooksListComponent()
            ],
          ),
        ),
      ),
    );
  }
}
