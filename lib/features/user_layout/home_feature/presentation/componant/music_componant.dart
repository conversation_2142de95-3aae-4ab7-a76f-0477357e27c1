// import 'package:clean_arc/core/presentation/extintions/context_extintions.dart';
// import 'package:clean_arc/core/presentation/util/app_dimensions.dart';
// import 'package:clean_arc/core/presentation/util/style/fonts/font_weight_helper.dart';
// import 'package:clean_arc/core/presentation/util/style/images/assets.gen.dart';
// import 'package:clean_arc/core/presentation/widget/text_app.dart';
// import 'package:clean_arc/features/user_layout/home_feature/domain/model/music_model.dart';
// import 'package:flutter/material.dart';
//
// class MusicComponent extends StatelessWidget {
//   MusicComponent({super.key});
//
//   List<MusicModel> musicList = <MusicModel>[
//     MusicModel(
//         minCount: '04',
//         title: 'التخلص من الخوف',
//         imageUrl: AppImages.images.svgIcon.music1.path),
//     MusicModel(
//         minCount: '05',
//         title: 'أخاف أن أفقد بصري',
//         imageUrl: AppImages.images.svgIcon.music2.path),
//     MusicModel(
//         minCount: '03',
//         title: 'التخلص من الخوف',
//         imageUrl: AppImages.images.svgIcon.music3.path),
//   ];
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           mainAxisAlignment: MainAxisAlignment.start,
//           children: [
//             TextApp(
//               text: 'العلاج النفسي الإلكتروني',
//               style: context.textStyle.copyWith(
//                 fontSize: AppDimensions.fontSizeLarge,
//                 fontWeight: FontWeightHelper.bold,
//               ),
//             ),
//             SizedBox(
//               height: 5,
//             ),
//             TextApp(
//               text: 'علاج نفسي كامل. عبر جلسات مسجله',
//               style: context.textStyle.copyWith(
//                   fontSize: AppDimensions.fontSizeDefault,
//                   fontWeight: FontWeightHelper.bold,
//                   color: context.color.grayColor),
//             ),
//           ],
//         ),
//         SizedBox(
//           height: 16,
//         ),
//         Container(
//           height: 150,
//           child: ListView.separated(
//             separatorBuilder: (context, index) => SizedBox(
//               width: 10,
//             ),
//             scrollDirection: Axis.horizontal,
//             itemCount: musicList.length,
//             itemBuilder: (context, index) => Stack(
//               children: [
//                 Container(
//                   height: 150,
//                   width: 130,
//                   child: Center(
//                       child: Image.asset(
//                     musicList[index].imageUrl,
//                     fit: BoxFit.cover,
//                     height: 150,
//                     width: 130,
//                   )),
//                 ),
//                 Container(
//                   height: 150,
//                   width: 130,
//                   child: Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Center(
//                         child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                       children: [
//                         TextApp(
//                            text: musicList[index].minCount.toString() + ' min',
//                           style: context.textStyle.copyWith(
//                             fontSize: AppDimensions.fontSizeLarge,
//                             color: context.color.whiteColor,
//                             fontWeight: FontWeightHelper.bold,
//                           ),
//                         ),
//                         AppImages.images.salimIcon.playerImage
//                             .image(width: 75, height: 23),
//                         TextApp(
//                           text: musicList[index].title.toString(),
//                           style: context.textStyle.copyWith(
//                             fontSize: AppDimensions.fontSizeDefault,
//                             color: context.color.whiteColor,
//                             fontWeight: FontWeightHelper.bold,
//                           ),
//                         ),
//                       ],
//                     )),
//                   ),
//                 )
//               ],
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
