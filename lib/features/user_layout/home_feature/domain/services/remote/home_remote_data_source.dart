import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/domain/model/message_model/response_message_model.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/model/vycle_type_model;/vycle_type_model.dart';
import 'package:injectable/injectable.dart';
import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/data/utill/configration.dart';
import 'package:retrofit/retrofit.dart';

part 'home_remote_data_source.g.dart';

@LazySingleton()
@RestApi(baseUrl: '')
abstract class HomeServices {
  @factoryMethod
  factory HomeServices(Dio dio, Configuration configuration) {
    return _HomeServices(dio, baseUrl: configuration.getApiUrl);
  }

  @GET('vehicle-types')
  Future<List<VehicleTypeModel>> getVehicleType({
    @Query('featured') required bool featured,
  });
}
