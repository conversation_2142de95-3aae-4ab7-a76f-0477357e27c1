import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/data/repositories/base_repository_impl.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/core/domain/repositories/base_repository.dart';
import 'package:clean_arc/features/auth_feature/domain/model/message_model/response_message_model.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:clean_arc/features/auth_feature/domain/services/remote/login_remote_data_source.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/model/vycle_type_model;/vycle_type_model.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/services/remote/home_remote_data_source.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

abstract class HomeRepository {
  Future<Either<Failure, List<VehicleTypeModel>>> getVehicleType({
    required bool featured,
  });
}

@LazySingleton(as: HomeRepository)
class HomeRepositoryImpl extends BaseRepositoryImpl implements HomeRepository {
  final HomeServices _homeServices;

  HomeRepositoryImpl(super.logger, this._homeServices);

  @override
  Future<Either<Failure, List<VehicleTypeModel>>> getVehicleType({
    required bool featured,
  }) {
    return request(() async {
      late final result;
      result = await _homeServices.getVehicleType(
        featured: featured,
      );

      return Right(result);
    });
  }
}
