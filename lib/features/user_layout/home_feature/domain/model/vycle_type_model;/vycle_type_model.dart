class VehicleTypeModel {
  VehicleTypeModel({
    required this.id,
    required this.name,
    required this.photo,
    required this.featured,
    required this.deletedAt,
  });

  final int? id;
  final String? name;
  final dynamic photo;
  final bool? featured;
  final dynamic deletedAt;

  factory VehicleTypeModel.fromJson(Map<String, dynamic> json){
    return VehicleTypeModel(
      id: json["id"],
      name: json["name"],
      photo: json["photo"],
      featured: json["featured"],
      deletedAt: json["deletedAt"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "photo": photo,
    "featured": featured,
    "deletedAt": deletedAt,
  };

}
