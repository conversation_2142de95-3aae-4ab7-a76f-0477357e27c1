// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_home_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$UserHomeState {
  ///================================== ==========================
  bool? get isLoadingGetFeatureVehicleType =>
      throw _privateConstructorUsedError;
  Failure? get isErrorGetFeatureVehicleType =>
      throw _privateConstructorUsedError;
  List<VehicleTypeModel>? get successGetFeatureVehicleType =>
      throw _privateConstructorUsedError;

  /// Create a copy of UserHomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserHomeStateCopyWith<UserHomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserHomeStateCopyWith<$Res> {
  factory $UserHomeStateCopyWith(
          UserHomeState value, $Res Function(UserHomeState) then) =
      _$UserHomeStateCopyWithImpl<$Res, UserHomeState>;
  @useResult
  $Res call(
      {bool? isLoadingGetFeatureVehicleType,
      Failure? isErrorGetFeatureVehicleType,
      List<VehicleTypeModel>? successGetFeatureVehicleType});
}

/// @nodoc
class _$UserHomeStateCopyWithImpl<$Res, $Val extends UserHomeState>
    implements $UserHomeStateCopyWith<$Res> {
  _$UserHomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserHomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingGetFeatureVehicleType = freezed,
    Object? isErrorGetFeatureVehicleType = freezed,
    Object? successGetFeatureVehicleType = freezed,
  }) {
    return _then(_value.copyWith(
      isLoadingGetFeatureVehicleType: freezed == isLoadingGetFeatureVehicleType
          ? _value.isLoadingGetFeatureVehicleType
          : isLoadingGetFeatureVehicleType // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorGetFeatureVehicleType: freezed == isErrorGetFeatureVehicleType
          ? _value.isErrorGetFeatureVehicleType
          : isErrorGetFeatureVehicleType // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetFeatureVehicleType: freezed == successGetFeatureVehicleType
          ? _value.successGetFeatureVehicleType
          : successGetFeatureVehicleType // ignore: cast_nullable_to_non_nullable
              as List<VehicleTypeModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$$UserHomeStateImplImplCopyWith<$Res>
    implements $UserHomeStateCopyWith<$Res> {
  factory _$$$UserHomeStateImplImplCopyWith(_$$UserHomeStateImplImpl value,
          $Res Function(_$$UserHomeStateImplImpl) then) =
      __$$$UserHomeStateImplImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isLoadingGetFeatureVehicleType,
      Failure? isErrorGetFeatureVehicleType,
      List<VehicleTypeModel>? successGetFeatureVehicleType});
}

/// @nodoc
class __$$$UserHomeStateImplImplCopyWithImpl<$Res>
    extends _$UserHomeStateCopyWithImpl<$Res, _$$UserHomeStateImplImpl>
    implements _$$$UserHomeStateImplImplCopyWith<$Res> {
  __$$$UserHomeStateImplImplCopyWithImpl(_$$UserHomeStateImplImpl _value,
      $Res Function(_$$UserHomeStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserHomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingGetFeatureVehicleType = freezed,
    Object? isErrorGetFeatureVehicleType = freezed,
    Object? successGetFeatureVehicleType = freezed,
  }) {
    return _then(_$$UserHomeStateImplImpl(
      isLoadingGetFeatureVehicleType: freezed == isLoadingGetFeatureVehicleType
          ? _value.isLoadingGetFeatureVehicleType
          : isLoadingGetFeatureVehicleType // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorGetFeatureVehicleType: freezed == isErrorGetFeatureVehicleType
          ? _value.isErrorGetFeatureVehicleType
          : isErrorGetFeatureVehicleType // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successGetFeatureVehicleType: freezed == successGetFeatureVehicleType
          ? _value._successGetFeatureVehicleType
          : successGetFeatureVehicleType // ignore: cast_nullable_to_non_nullable
              as List<VehicleTypeModel>?,
    ));
  }
}

/// @nodoc

class _$$UserHomeStateImplImpl implements _$UserHomeStateImpl {
  _$$UserHomeStateImplImpl(
      {required this.isLoadingGetFeatureVehicleType,
      required this.isErrorGetFeatureVehicleType,
      required final List<VehicleTypeModel>? successGetFeatureVehicleType})
      : _successGetFeatureVehicleType = successGetFeatureVehicleType;

  ///================================== ==========================
  @override
  final bool? isLoadingGetFeatureVehicleType;
  @override
  final Failure? isErrorGetFeatureVehicleType;
  final List<VehicleTypeModel>? _successGetFeatureVehicleType;
  @override
  List<VehicleTypeModel>? get successGetFeatureVehicleType {
    final value = _successGetFeatureVehicleType;
    if (value == null) return null;
    if (_successGetFeatureVehicleType is EqualUnmodifiableListView)
      return _successGetFeatureVehicleType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'UserHomeState(isLoadingGetFeatureVehicleType: $isLoadingGetFeatureVehicleType, isErrorGetFeatureVehicleType: $isErrorGetFeatureVehicleType, successGetFeatureVehicleType: $successGetFeatureVehicleType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$$UserHomeStateImplImpl &&
            (identical(other.isLoadingGetFeatureVehicleType,
                    isLoadingGetFeatureVehicleType) ||
                other.isLoadingGetFeatureVehicleType ==
                    isLoadingGetFeatureVehicleType) &&
            (identical(other.isErrorGetFeatureVehicleType,
                    isErrorGetFeatureVehicleType) ||
                other.isErrorGetFeatureVehicleType ==
                    isErrorGetFeatureVehicleType) &&
            const DeepCollectionEquality().equals(
                other._successGetFeatureVehicleType,
                _successGetFeatureVehicleType));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoadingGetFeatureVehicleType,
      isErrorGetFeatureVehicleType,
      const DeepCollectionEquality().hash(_successGetFeatureVehicleType));

  /// Create a copy of UserHomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$$UserHomeStateImplImplCopyWith<_$$UserHomeStateImplImpl> get copyWith =>
      __$$$UserHomeStateImplImplCopyWithImpl<_$$UserHomeStateImplImpl>(
          this, _$identity);
}

abstract class _$UserHomeStateImpl implements UserHomeState {
  factory _$UserHomeStateImpl(
      {required final bool? isLoadingGetFeatureVehicleType,
      required final Failure? isErrorGetFeatureVehicleType,
      required final List<VehicleTypeModel>?
          successGetFeatureVehicleType}) = _$$UserHomeStateImplImpl;

  ///================================== ==========================
  @override
  bool? get isLoadingGetFeatureVehicleType;
  @override
  Failure? get isErrorGetFeatureVehicleType;
  @override
  List<VehicleTypeModel>? get successGetFeatureVehicleType;

  /// Create a copy of UserHomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$$UserHomeStateImplImplCopyWith<_$$UserHomeStateImplImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
