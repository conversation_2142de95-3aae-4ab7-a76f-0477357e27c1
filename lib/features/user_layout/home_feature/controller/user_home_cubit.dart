import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/model/vycle_type_model;/vycle_type_model.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/repository/home_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_home_cubit.freezed.dart';
part 'user_home_state.dart'; 

@injectable
class UserHomeCubit extends Cubit<UserHomeState> {
  UserHomeCubit(this.repository) : super(UserHomeState.initial());

  final HomeRepository repository;


}
