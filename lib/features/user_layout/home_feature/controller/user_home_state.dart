part of 'user_home_cubit.dart';

@freezed
class UserHomeState with _$UserHomeState {
  factory UserHomeState({
    ///================================== ==========================
    required bool? isLoadingGetFeatureVehicleType,
    required Failure? isErrorGetFeatureVehicleType,
    required List<VehicleTypeModel>? successGetFeatureVehicleType,

    ///======================= =========== ==========================
  }) = _$UserHomeStateImpl;

  factory UserHomeState.initial() => UserHomeState(
        isLoadingGetFeatureVehicleType: null,
        isErrorGetFeatureVehicleType: null,
        successGetFeatureVehicleType: null,
      );
}
