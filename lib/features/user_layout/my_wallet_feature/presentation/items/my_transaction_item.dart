import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class TransactionItem extends StatelessWidget {
  Widget image;
  String title;
  String titleSub;
  Color titleSubColor;
  String description;

  TransactionItem({
    required this.image,
    required this.title,
    required this.titleSub,
    required this.titleSubColor,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: context.color.whiteColor
      ),
      child: Row(
        children: [
          image,
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextApp(
                        text: title,
                        style: context.textStyle.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: AppDimensions.fontSizeLarge,
                        ),
                      ),
                    ),
                    TextApp(
                      text: '${titleSub}',
                      style: context.textStyle.copyWith(
                        fontWeight: FontWeight.bold,
                        color: titleSubColor,
                        fontSize: AppDimensions.fontSizeDefault,
                      ),
                    ),
                  ],
                ),
                TextApp(
                  text: description,
                  style: context.textStyle.copyWith(
                    color: context.color.hintColor,
                    fontSize: AppDimensions.fontSizeDefault,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
