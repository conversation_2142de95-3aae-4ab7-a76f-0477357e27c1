import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/user_layout/support_feature/presentation/items/support_items.dart';
import 'package:flutter/material.dart';

@RoutePage()
class SupportView extends StatelessWidget {
  const SupportView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(context, title: context.translate.support),
      body: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSizeDefault,
              ),
              child: TextApp(
                text: context.translate
                    .selectWithContactDetailsShouldWeUseToContactSupport,
                style: context.textStyle.copyWith(
                  color: context.color.hintColor,
                  fontSize: AppDimensions.fontSizeDefault,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(
              height: 16,
            ),
            SupportItems(
                image: AppImages.images.svgIcon.mailIcon.svg(),
                title: context.translate.email,
                description: '<EMAIL>'),
            SizedBox(
              height: 16,
            ),
            SupportItems(
                image: AppImages.images.svgIcon.phoneIcon.svg(),
                title: context.translate.phoneNumber,
                description: '+201060994711'),
            SizedBox(
              height: 16,
            ),
            SupportItems(
                image: AppImages.images.svgIcon.whatsappIcon.svg(),
                title: context.translate.whatsapp,
                description: '+201060994711'),
          ],
        ),
      ),
    );
  }
}
