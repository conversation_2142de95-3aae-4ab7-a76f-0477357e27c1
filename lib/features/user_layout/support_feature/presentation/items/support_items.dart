import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';

class SupportItems extends StatelessWidget {
  Widget image;
  String title;
  String description;

  SupportItems({
    required this.image,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingSizeSmall,
          vertical: AppDimensions.paddingSizeExtraSmall),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: context.color.borderColor!),
      ),
      child: Row(
        children: [
          image,
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextApp(
                text: title,
                style: context.textStyle.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: AppDimensions.fontSizeLarge,
                ),
              ),
              TextApp(
                text: description,
                style: context.textStyle.copyWith(
                  color: context.color.hintColor,
                  fontSize: AppDimensions.fontSizeDefault,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
