import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/presentation/widget/emoji_scale_dialog.dart';
import 'package:clean_arc/core/services/get_storage_service.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/my_global_cubit/thought_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/routing/app_router.gr.dart';

@RoutePage()
class RepetitionView extends StatefulWidget {
  int thoughtId;

  RepetitionView({super.key, required this.thoughtId});

  @override
  State<RepetitionView> createState() => _RepetitionViewState();
}

class _RepetitionViewState extends State<RepetitionView> {
  List<Map<String, String>> summaries = [];

  @override
  void initState() {
    super.initState();
    _loadSummaries();
  }

  void _loadSummaries() {
    summaries = GetStorageService.getQuestionSummaries();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(context,
            title: context.translate.formSummary,
            centerTitle: true,
            showNotification: false),
        body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Selected thought title
                BlocBuilder<ThoughtCubit, Thought?>(
                  builder: (context, selectedThought) {
                    if (selectedThought != null) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: context.color.primaryColor!.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color:
                                  context.color.primaryColor!.withOpacity(0.3),
                            ),
                          ),
                          child: Column(
                            children: [
                              Text(
                                "الخوف المختار:",
                                style: context.textStyle.copyWith(
                                  fontSize: 14,
                                  color: context.color.primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                selectedThought.content ?? '',
                                style: context.textStyle.copyWith(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    return SizedBox.shrink();
                  },
                ),

                // Title
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Text(
                    "ملخص الأسئلة",
                    style: context.textStyle.copyWith(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // Summaries list
                Expanded(
                  child: summaries.isEmpty
                      ? Center(
                          child: Text(
                            "لا توجد ملخصات متاحة",
                            style: context.textStyle.copyWith(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                      : ListView.builder(
                          itemCount: summaries.length,
                          itemBuilder: (context, index) {
                            final summary = summaries[index];
                            return Card(
                              margin: EdgeInsets.only(bottom: 16),
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Question title
                                    Text(
                                      summary['title'] ?? '',
                                      style: context.textStyle.copyWith(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: context.color.primaryColor,
                                      ),
                                    ),
                                    SizedBox(height: 8),

                                    // Summary content
                                    Text(
                                      summary['summary'] ?? '',
                                      style: context.textStyle.copyWith(
                                        fontSize: 14,
                                        color: Colors.grey[700],
                                        height: 1.4,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),

                // Finished button
                SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: CustomButton(
                        width: double.infinity,
                        onPressed: () {
                          // Show emoji scale dialog first
                          final selectedThought =
                              context.read<ThoughtCubit>().selectedThought;
                          if (selectedThought?.id != null) {
                            showEmojiScaleDialog(
                              context: context,
                              title: 'تقييم مستوى القلق النهائي',
                              description:
                                  'كيف تشعر الآن بعد إكمال جلسة العلاج؟',
                              onSubmit: (int score) async {
                                // Make PUT API call
                                await context
                                    .read<ConcernsCubit>()
                                    .updateAfterScore(
                                      id: selectedThought!.id!,
                                      afterScore: score,
                                    );

                                // context.pushRoute(SolveProblemMethodViewRoute(
                                //     thoughtsId: widget.thoughtId));

                                // Navigate back to SubCategoryListView
                                // Navigator.of(context)
                                //     .popUntil((route) => route.isFirst);
                                Navigator.of(context).pop();
                                Navigator.of(context).pop();
                                Navigator.of(context).pop();
                              },
                            );
                          } else {
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            // context.pushRoute(SolveProblemMethodViewRoute(
                            //     thoughtsId: widget.thoughtId));
                            // If no thought selected, just navigate back
                            // Navigator.of(context)
                            //     .popUntil((route) => route.isFirst);
                          }
                        },
                        child: Text(
                          context.translate.finished,
                          style: context.textStyleButton,
                        )),
                  ),
                ),
              ],
            )));
  }
}
