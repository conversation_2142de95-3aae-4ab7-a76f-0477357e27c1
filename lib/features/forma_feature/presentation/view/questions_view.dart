import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/presentation/widget/custom_loading.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/services/get_storage_service.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:clean_arc/features/splash_feature/presentation/view/repitation_widget.dart';
import 'package:clean_arc/features/user_layout/lip_feature/controller/lib_controller.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/concern_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class QuestionsView extends StatefulWidget {
  int thoughtId;

  QuestionsView({super.key, required this.thoughtId});

  @override
  State<QuestionsView> createState() => _QuestionsViewState();
}

class _QuestionsViewState extends State<QuestionsView> {
  @override
  void initState() {
    context
        .read<ConcernsCubit>()
        .getQuestionsByThoughtId(id: widget.thoughtId.toInt())
        .then(
      (value) {
        // previousQuestions
        //     .add(context.read<ConcernsCubit>().state.successQuestions!.first);
      },
    );

    super.initState();
  }

  int selectedIndex = 0;
  int? nextRoutId;
  int? currentQuestionId; // Track current question to detect changes

  List<QuestionsModel> previousQuestions = [];
  List<int> navigationHistory = []; // Track navigation path
  Map<int, bool?> questionAnswers = {}; // Track answers for each question
  List<QuestionsModel> answeredQuestions =
      []; // Track answered questions for summary
  int? selectedRoutId;

  bool? yes;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(context,
            title: context.translate.form,
            centerTitle: true,
            showNotification: false),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: BlocBuilder<ConcernsCubit, ConcernsState>(
            builder: (context, state) {
              if (state.errorQuestions != null) {
                return Center(
                  child: Column(
                    children: [
                      CustomErrorWidget(
                        failure: state.errorQuestions,
                        onPressed: () {
                          context
                              .read<ConcernsCubit>()
                              .getQuestionsByThoughtId(id: widget.thoughtId);
                        },
                      ),
                    ],
                  ),
                );
              } else if (state.isLoadingQuestions == true) {
                return Center(child: CustomLoading());
              }
              List<QuestionsModel>? questions = state.successQuestions;

              QuestionsModel question = nextRoutId == null
                  ? questions![0]
                  : questions!.firstWhere(
                      (element) =>
                          element.id == nextRoutId && element.id != null,
                    );

              // Only restore state when question changes
              if (currentQuestionId != question.id) {
                currentQuestionId = question.id;

                // Restore the previous answer for this question if it exists
                if (questionAnswers.containsKey(question.id)) {
                  yes = questionAnswers[question.id];
                  selectedRoutId =
                      yes == true ? question.yesRouteId : question.noRouteId;
                } else {
                  // Reset state for new question
                  yes = null;
                  selectedRoutId = null;
                }
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LinearProgressIndicator(
                    value: (navigationHistory.length + 1) / questions.length,
                    minHeight: 8,
                    backgroundColor:
                        context.color.primaryColor!.withOpacity(0.2),
                    valueColor:
                        AlwaysStoppedAnimation(context.color.primaryColor),
                  ),

                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   crossAxisAlignment: CrossAxisAlignment.start,
                  //   children: [
                  //     Icon(
                  //       Icons.arrow_back_ios,
                  //       size: 15,
                  //       color: context.color.primaryColor,
                  //     ),
                  //     TextApp(
                  //       text: '${questions.length}/',
                  //       style: context.textStyle.copyWith(
                  //         fontSize: 18,
                  //         fontWeight: FontWeight.bold,
                  //       ),
                  //     ),
                  //
                  //     TextApp(
                  //       text: '${previousQuestions.length + 1}',
                  //       style: context.textStyle.copyWith(
                  //           fontSize: 18,
                  //           fontWeight: FontWeight.bold,
                  //           color: context.color.primaryColor),
                  //     ),
                  //     Icon(
                  //       Icons.arrow_forward_ios_outlined,
                  //       size: 15,
                  //       color: context.color.primaryColor,
                  //     ),
                  //   ],
                  // ),
                  SizedBox(
                    height: 16,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: TextApp(
                                text: question.questionText ?? '',
                                style: context.textStyle
                                    .copyWith(fontWeight: FontWeight.bold),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            TextApp(
                              text: 'الخوف من نوبة الهلع',
                              style: context.textStyle.copyWith(
                                  fontWeight: FontWeight.normal,
                                  color: context.color.descriptionColor),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Divider(),
                  SizedBox(
                    height: 16,
                  ),

                  if (question.questionType == 'repeating_sentence')
                    InkWell(
                      onTap: () {
                        BookManage.toggleConcern(QuestionsCashModel.fromJson(
                            question.toJson() ?? {}));
                      },
                      child: ValueListenableBuilder<Set<int>>(
                        valueListenable: BookManage.savedConcerns,
                        builder: (context, savedBooks, child) {
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  savedBooks.contains(question.id)
                                      ? Icons.bookmark
                                      : Icons.bookmark_border,
                                  color: context.color.yellowColor,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  "إضافة إلى المفضلة",
                                  style: context.textStyle.copyWith(
                                    color: context.color.yellowColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),

                  ((question.yesRouteId == question.noRouteId) &&
                          (question.yesRouteId != null &&
                              question.yesRouteId != null))
                      ? Container()
                      : Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  yes = true;
                                  selectedRoutId = question.yesRouteId;
                                  setState(() {});
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: (yes == true)
                                          ? context.color.primaryColor!
                                              .withOpacity(.3)
                                          : Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: context.color.grayColor!
                                              .withOpacity(.1),
                                          spreadRadius: 1,
                                          blurRadius: 1,
                                          offset: Offset(0,
                                              1), // changes position of shadow
                                        ),
                                      ]),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: TextApp(
                                            text: question.button_yes_text ??
                                                (question.questionText
                                                        .toString()
                                                        .contains('تفق')
                                                    ? "اتفق"
                                                    : context.translate.yes),
                                            style: context.textStyle.copyWith(
                                                fontWeight: FontWeight.bold,
                                                color:
                                                    context.color.primaryColor),
                                          ),
                                        ),
                                        (yes == true)
                                            ? Icon(
                                                Icons.check_circle,
                                                color:
                                                    context.color.primaryColor,
                                                size: 20,
                                              )
                                            : Icon(
                                                Icons.radio_button_unchecked,
                                                color:
                                                    context.color.primaryColor,
                                                size: 20,
                                              )
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 16,
                            ),
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  yes = false;
                                  selectedRoutId = question.noRouteId;
                                  setState(() {});
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: (yes == false)
                                          ? context.color.primaryColor!
                                              .withOpacity(.3)
                                          : Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: context.color.grayColor!
                                              .withOpacity(.1),
                                          spreadRadius: 1,
                                          blurRadius: 1,
                                          offset: Offset(0,
                                              1), // changes position of shadow
                                        ),
                                      ]),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        TextApp(
                                          text: question.button_no_text ??
                                              (question.questionText
                                                      .toString()
                                                      .contains('تفق')
                                                  ? "لا اتفق"
                                                  : context.translate.no),
                                          style: context.textStyle.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color:
                                                  context.color.primaryColor),
                                        ),
                                        (yes == false)
                                            ? Icon(
                                                Icons.check_circle,
                                                color:
                                                    context.color.primaryColor,
                                                size: 20,
                                              )
                                            : Icon(
                                                Icons.radio_button_unchecked,
                                                color:
                                                    context.color.primaryColor,
                                                size: 20,
                                              )
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                  SizedBox(
                    height: 16,
                  ),
                  Divider(),
                  SizedBox(
                    height: 16,
                  ),
                  RepetitionVideo(),
                  // AppImages.images.salimIcon.formaImage.image(),
                  Spacer(),

                  // Previous and Next buttons
                  Row(
                    children: [
                      // Previous button
                      if (navigationHistory.isNotEmpty)
                        Expanded(
                          child: SizedBox(
                            height: 55,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: OutlinedButton(
                                onPressed: () {
                                  if (navigationHistory.isNotEmpty) {
                                    // Remove current question from history
                                    navigationHistory.removeLast();

                                    // Set next route to previous question or null for first question
                                    nextRoutId = navigationHistory.isNotEmpty
                                        ? navigationHistory.last
                                        : null;

                                    // Remove the current question from previous questions
                                    if (previousQuestions.isNotEmpty) {
                                      previousQuestions.removeLast();
                                    }

                                    // Reset current question ID to force state restoration
                                    currentQuestionId = null;

                                    setState(() {});
                                  }
                                },
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(
                                      color: context.color.primaryColor!),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  padding: EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: TextApp(
                                  text: "السابق",
                                  style: context.textStyle.copyWith(
                                    color: context.color.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                      SizedBox(width: 16),

                      // Next button
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: navigationHistory.isNotEmpty ? 8.0 : 0),
                          child: CustomButton(
                            onPressed: () {
                              // Check if this is a yes/no question (has different routes for yes and no)
                              bool isYesNoQuestion =
                                  (question.yesRouteId != question.noRouteId) ||
                                      (question.yesRouteId == null ||
                                          question.noRouteId == null);

                              // Check if this is a single-route question (same route for both yes and no)
                              bool isSingleRouteQuestion =
                                  (question.yesRouteId == question.noRouteId) &&
                                      (question.yesRouteId != null &&
                                          question.noRouteId != null);

                              // Validation: For yes/no questions, user must select an option
                              if (isYesNoQuestion && yes == null) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content:
                                        Text('يرجى اختيار إجابة قبل المتابعة'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                                return;
                              }

                              // Save the current answer for this question
                              if (question.id != null) {
                                questionAnswers[question.id!] = yes;
                              }

                              // Determine the next route based on question type and selection
                              int? nextRoute;
                              if (isSingleRouteQuestion) {
                                // For single-route questions, use the common route
                                nextRoute = question.yesRouteId;
                              } else {
                                // For yes/no questions, use the selected route
                                nextRoute = yes == true
                                    ? question.yesRouteId
                                    : question.noRouteId;
                              }

                              // Save question summary if it has one and was answered
                              if (question.summary?.isNotEmpty == true) {
                                _saveQuestionSummary(question);
                              }

                              // Add current question to navigation history
                              if (question.id != null) {
                                navigationHistory.add(question.id!);
                              }

                              // Add to previous questions if not already there
                              if (!previousQuestions.contains(question)) {
                                previousQuestions.add(question);
                              }

                              // Set the next route
                              nextRoutId = nextRoute;

                              // Reset current question ID to force state restoration for next question
                              currentQuestionId = null;

                              setState(() {});

                              // If no next route, go to repetition view (end of questions)
                              if (nextRoutId == null) {
                                context.pushRoute(RepetitionViewRoute(
                                    thoughtId: widget.thoughtId));
                              }
                            },
                            child: TextApp(
                              text: context.translate.nextQuestion,
                              style: context.textStyleButton,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ));
  }

  // Save question summary to local storage
  void _saveQuestionSummary(QuestionsModel question) {
    if (question.summary?.isNotEmpty == true &&
        question.questionText?.isNotEmpty == true) {
      Map<String, String> summaryData = {
        'title': question.questionText!,
        'summary': question.summary!,
      };

      // Get existing summaries
      List<Map<String, String>> existingSummaries =
          GetStorageService.getQuestionSummaries();

      // Check if this question summary already exists
      bool alreadyExists = existingSummaries
          .any((summary) => summary['title'] == question.questionText);

      if (!alreadyExists) {
        existingSummaries.add(summaryData);
        GetStorageService.saveQuestionSummaries(existingSummaries);
      }
    }
  }
}
