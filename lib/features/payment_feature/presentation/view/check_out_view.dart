import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

@RoutePage()
class CheckoutView extends StatelessWidget {
  const CheckoutView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(context,
          title: context.translate.payments,
          centerTitle: true,
          showNotification: false),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: context.color.whiteColor,
              // image: DecorationImage(
              //     fit: BoxFit.fill,
              //     image: AssetImage(
              //         AppImages.images.svgIcon.homeImageHeader.path)
              //
              // ),
              boxShadow: [
                BoxShadow(
                  color: context.color.borderColor!,
                  spreadRadius: 1,
                )
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          TextApp(
                            text:
                                context.translate.bookGoodbyeToNegativeThoughts,
                            style: context.textStyle.copyWith(
                              fontSize: AppDimensions.fontSizeDefault,
                              // color: context.color.whiteColor,
                              fontWeight: FontWeightHelper.bold,
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          TextApp(
                            text: context.translate
                                .yourCompleteGuideToGettingRidOfNegativeThoughts,
                            style: context.textStyle.copyWith(
                              fontSize: AppDimensions.fontSizeDefault,
                              fontWeight: FontWeightHelper.regular,
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                        ],
                      ),
                    ),
                    AppImages.images.demo.book.image(
                      height: 200,
                      fit: BoxFit.cover,
                    )
                  ],
                ),
              ),
            ),
          ),
          Divider(),
          SizedBox(
            height: 16,
          ),
          Row(
            children: [
              AppImages.images.salimIcon.coupon.svg(),
              // proceedToPayment
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextApp(
                      text: context.translate.applyCoupons,
                      style: context.textStyle.copyWith(
                        fontSize: AppDimensions.fontSizeDefault,
                        // color: context.color.whiteColor,
                        fontWeight: FontWeightHelper.bold,
                      ),
                    ),
                    TextApp(
                      text: context.translate.select,
                      style: context.textStyle.copyWith(
                        fontSize: AppDimensions.fontSizeDefault,
                        color: context.color.primaryColor,
                        fontWeight: FontWeightHelper.regular,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Divider(),
          SizedBox(
            height: 16,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextApp(
                text: context.translate.orderPaymentDetails,
                style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeDefault,
                  // color: context.color.whiteColor,
                  fontWeight: FontWeightHelper.bold,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextApp(
                text: context.translate.orderPrice,
                style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeDefault,
                  // color: context.color.whiteColor,
                  fontWeight: FontWeightHelper.regular,
                ),
              ),
              TextApp(
                text: '150',
                style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeDefault,
                  color: context.color.primaryColor,
                  fontWeight: FontWeightHelper.bold,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextApp(
                text: context.translate.deliveryFee,
                style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeDefault,
                  // color: context.color.whiteColor,
                  fontWeight: FontWeightHelper.regular,
                ),
              ),
              TextApp(
                text: context.translate.free,
                style: context.textStyle.copyWith(
                  fontSize: AppDimensions.fontSizeDefault,
                  color: context.color.primaryColor,
                  fontWeight: FontWeightHelper.bold,
                ),
              ),
            ],
          ),
        ]),
      ),
    );
  }
}
