import 'package:clean_arc/core/presentation/widget/custom_app_bar.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/user_layout/my_wallet_feature/presentation/items/my_transaction_item.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

@RoutePage()
class MyWalletView extends StatelessWidget {
  const MyWalletView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        context,
        title: context.translate.myWallet,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
          child: Column(
            children: [
              Row(),
              TextApp(
                text: context.translate.yourBalance,
                style: context.textStyle.copyWith(
                  color: context.color.hintColor,
                  fontWeight: FontWeightHelper.medium,
                  fontSize: AppDimensions.fontSizeDefault,
                ),
                textAlign: TextAlign.center,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextApp(
                    text: 'SAR',
                    style: context.textStyle.copyWith(
                      color: context.color.hintColor,
                      fontWeight: FontWeightHelper.bold,
                      fontSize: AppDimensions.fontSizeDefault,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  TextApp(
                    text: '700.50',
                    style: context.textStyle.copyWith(
                        fontWeight: FontWeightHelper.bold,
                        fontSize: AppDimensions.fontSizeExtraLarge40,
                        height: 1.2),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                        withBorderOnly: true,
                        borderColor: context.color.primaryColor,
                        bgColor: Colors.transparent,
                        onPressed: () {},
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AppImages.images.svgIcon.despoit.svg(),
                            SizedBox(
                              width: 5,
                            ),
                            TextApp(
                              text: context.translate.deposit,
                              style: context.textStyleButton.copyWith(
                                  fontWeight: FontWeightHelper.bold,
                                  fontSize: AppDimensions.fontSizeDefault,
                                  color: context.color.primaryColor),
                            ),
                          ],
                        )),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    child: CustomButton(
                        onPressed: () {},
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AppImages.images.svgIcon.withdraw.svg(),
                            SizedBox(
                              width: 5,
                            ),
                            TextApp(
                              text: context.translate.withdraw,
                              style: context.textStyleButton.copyWith(
                                  fontWeight: FontWeightHelper.bold,
                                  fontSize: AppDimensions.fontSizeDefault,
                                  color: context.color.whiteColor),
                            ),
                          ],
                        )),
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  TextApp(
                    text: context.translate.transaction,
                    style: context.textStyle.copyWith(
                        fontWeight: FontWeightHelper.bold,
                        fontSize: AppDimensions.fontSizeLarge,
                        color: context.color.descriptionColor,
                        height: 1.2),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
              TransactionItem(
                image: AppImages.images.svg.visa.svg(),
                title: 'Paypal Charge Money',
                titleSub: '+\$500.00',
                description: "23 Sep - Paypal",
                titleSubColor: context.color.greenColor!,
              ),
              SizedBox(
                height: 16,
              ),
              TransactionItem(
                image: AppImages.images.svg.visa.svg(),
                title: 'Visa Charge Money',
                titleSub: '+\$500.00',
                description: "23 Sep - Paypal",
                titleSubColor: context.color.grayColor!,
              ),
              SizedBox(
                height: 16,
              ),
              TransactionItem(
                image: AppImages.images.svg.master.svg(),
                title: 'Paypal Charge Money',
                titleSub: '+\$500.00',
                description: "23 Sep - Paypal",
                titleSubColor: context.color.redColor!,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
