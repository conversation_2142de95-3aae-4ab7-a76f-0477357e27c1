import 'package:auto_route/auto_route.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/payment_feature/presentation/items/deals_items.dart';
import 'package:flutter/material.dart';

@RoutePage()
class FreeTryingView extends StatelessWidget {
  FreeTryingView({super.key});

  int selectedIndex = 1;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            AppImages.images.salimIcon.packge.image(
                height: context.height * .2,
                fit: BoxFit.cover,
                width: double.infinity),
            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: context.color.primaryColor?.withOpacity(.2)),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AppImages.images.salimIcon.daimond.svg(),
                        SizedBox(
                          width: 10,
                        ),
                        TextApp(
                          text: context.translate.dayFreeTrial,
                          style: context.textStyle.copyWith(
                              fontWeight: FontWeight.bold,
                              color: context.color.primaryColor),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  TextApp(
                    text: context.translate.openingSalimCbt,
                    style: context.textStyle.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: AppDimensions.fontSizeLarge),
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Row(
                    children: [
                      AppImages.images.salimIcon.checkCircle
                          .svg(height: 25, width: 25, fit: BoxFit.cover),
                      SizedBox(
                        width: 5,
                      ),
                      TextApp(
                        text: context.translate.oneOnOneLiveSessions,
                        style: context.textStyle.copyWith(
                            fontWeight: FontWeightHelper.medium,
                            fontSize: AppDimensions.fontSizeDefault),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      AppImages.images.salimIcon.checkCircle
                          .svg(height: 25, width: 25, fit: BoxFit.cover),
                      SizedBox(
                        width: 5,
                      ),
                      TextApp(
                        text: context.translate.continuousFollowUp,
                        style: context.textStyle.copyWith(
                            fontWeight: FontWeightHelper.medium,
                            fontSize: AppDimensions.fontSizeDefault),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      AppImages.images.salimIcon.checkCircle
                          .svg(height: 25, width: 25, fit: BoxFit.cover),
                      SizedBox(
                        width: 5,
                      ),
                      TextApp(
                        text: context.translate.hourSupport,
                        style: context.textStyle.copyWith(
                            fontWeight: FontWeightHelper.medium,
                            fontSize: AppDimensions.fontSizeDefault),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      AppImages.images.salimIcon.checkCircle
                          .svg(height: 25, width: 25, fit: BoxFit.cover),
                      SizedBox(
                        width: 5,
                      ),
                      TextApp(
                        text: context.translate.quickConsultations,
                        style: context.textStyle.copyWith(
                            fontWeight: FontWeightHelper.medium,
                            fontSize: AppDimensions.fontSizeDefault),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      AppImages.images.salimIcon.checkCircle
                          .svg(height: 25, width: 25, fit: BoxFit.cover),
                      SizedBox(
                        width: 5,
                      ),
                      TextApp(
                        text: context.translate.customContent,
                        style: context.textStyle.copyWith(
                            fontWeight: FontWeightHelper.medium,
                            fontSize: AppDimensions.fontSizeDefault),
                      ),
                    ],
                  ),

                  SizedBox(
                    height: 16,
                  ),

                  ///=========================

                  // Container(
                  //   decoration: BoxDecoration(
                  //     borderRadius: BorderRadius.circular(10),
                  //     color: context.color.whiteColor,
                  //     boxShadow: [
                  //       BoxShadow(
                  //           color: Colors.grey.withOpacity(0.5),
                  //           spreadRadius: 1,
                  //           blurRadius: 5,
                  //           offset: Offset(0, 3))
                  //     ],
                  //   ),
                  //   height: 120,
                  //   width: 120,
                  //   child: Padding(
                  //     padding: const EdgeInsets.symmetric(
                  //         horizontal: 16.0, vertical: 16),
                  //     child: Column(
                  //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //       children: [
                  //         TextApp(
                  //           text: '6 شهري',
                  //           style: context.textStyle.copyWith(
                  //               fontWeight: FontWeightHelper.bold,
                  //               fontSize: AppDimensions.fontSizeLarge),
                  //         ),
                  //         TextApp(
                  //           text: '89.99\$',
                  //           style: context.textStyle.copyWith(
                  //               fontWeight: FontWeightHelper.bold,
                  //               fontSize: AppDimensions.fontSizeLarge),
                  //         ),
                  //         TextApp(
                  //           text: '\$14.99\nكل شهر',
                  //           style: context.textStyle.copyWith(
                  //               fontWeight: FontWeightHelper.regular,
                  //               color: context.color.descriptionColor,
                  //               fontSize: AppDimensions.fontSizeSmall),
                  //         ),
                  //       ],
                  //     ),
                  //   ),
                  // ),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      DealsItems(
                          duration: '6 شهري',
                          price: '\$89.99',
                          paymentNumber: '\$14.99\nكل شهر'),
                      Spacer(),
                      DealsItems(
                          isBest: true,
                          duration: '6 شهري',
                          price: '\$89.99',
                          paymentNumber: '\$14.99\nكل شهر'),
                      Spacer(),
                      DealsItems(
                          duration: '6 شهري',
                          price: '\$89.99',
                          paymentNumber: '\$14.99\nكل شهر'),
                    ],
                  ),
                  SizedBox(
                    height: 16,
                  ),

                  TextApp(
                    text: context.translate
                        .try14DaysForFreeThenPay12999OnceAndUseForeverYouCanCancelAnyTime,
                    style: context.textStyle.copyWith(
                        fontWeight: FontWeightHelper.regular,
                        color: context.color.descriptionColor,
                        fontSize: AppDimensions.fontSizeDefault),
                    textAlign: TextAlign.center,
                  ),

                  SafeArea(
                    child: Column(
                      children: [
                        CustomButton(
                          width: double.infinity,
                          onPressed: () {
                            // context
                            context.pushRoute(UserLayoutViewRoute());
                          },
                          child: TextApp(
                            text: context.translate.start14DayFreeTrial,
                            style: context.textStyleButton,
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        CustomButton(
                          withBorderOnly: true,
                          bgColor: Colors.transparent,
                          borderColor: context.color.primaryColor,
                          width: double.infinity,
                          onPressed: () {
                            context.pushRoute(UserLayoutViewRoute());

                            // context.pushRoute(SelectBestObjectViewRoute());
                          },
                          child: TextApp(
                            text: context.translate.finished,
                            style: context.textStyleButton
                                .copyWith(color: context.color.primaryColor),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
