import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';

class DealsItems extends StatelessWidget {
  String duration;
  String price;
  String paymentNumber;
  bool isBest;

  DealsItems(
      {Key? key,
      required this.duration,
      required this.price,
      this.isBest = false,
      required this.paymentNumber})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          height: 140,
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: context.color.whiteColor,
            boxShadow: [
              BoxShadow(
                  color: isBest
                      ? context.color.primaryColor!
                      : Colors.grey.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: isBest ? 5 : 3,
                  offset: Offset(0, isBest ? 1 : 3))
            ],
          ),
          height: 120,
          width: 110,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextApp(
                  text: duration,
                  style: context.textStyle.copyWith(
                      fontWeight: FontWeightHelper.bold,
                      fontSize: AppDimensions.fontSizeLarge),
                ),
                TextApp(
                  text: price,
                  style: context.textStyle.copyWith(
                      fontWeight: FontWeightHelper.bold,
                      fontSize: AppDimensions.fontSizeLarge),
                ),
                TextApp(
                  text: paymentNumber,
                  style: context.textStyle.copyWith(
                      fontWeight: FontWeightHelper.regular,
                      color: context.color.descriptionColor,
                      fontSize: AppDimensions.fontSizeSmall),
                ),
              ],
            ),
          ),
        ),
        isBest
            ? Positioned(
                top: 0,
                child: Container(
                  width: 75,
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: context.color.primaryColor?.withOpacity(.3)),
                  child: TextApp(
                    text: context.translate.bestDeal,
                    style: context.textStyle.copyWith(
                        fontSize: AppDimensions.fontSizeSmall,
                        fontWeight: FontWeight.normal,
                        color: context.color.primaryColor),
                    textAlign: TextAlign.center,
                  ),
                ),
              )
            : SizedBox(),
      ],
    );
  }
}
