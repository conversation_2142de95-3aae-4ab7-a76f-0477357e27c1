import 'package:clean_arc/core/data/services/shared_prefs/shared_pref.dart';
import 'package:clean_arc/core/data/services/shared_prefs/shared_prefs_key.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/splash_feature/domain/entity/onboarding_entity.dart';
import 'package:clean_arc/features/splash_feature/presentation/componant/dots.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_html/flutter_html.dart';

@RoutePage()
class OnBoardingView extends StatefulWidget {
  const OnBoardingView({super.key});

  static const String path = '/OnBoardingView';

  @override
  State<OnBoardingView> createState() => _OnBoardingViewState();
}

class _OnBoardingViewState extends State<OnBoardingView> {
  late PageController controller;
  int currentPage = 0;

  List<OnBoardingEntity> onBoardingList = [];

  @override
  void initState() {
    controller = PageController();

    Future.delayed(Duration.zero, () {
      onBoardingList = [
        OnBoardingEntity(
          title1: context.translate.onBoard1,
          title2: context.translate.onBoard1color,
          title3: context.translate.onBoard1AfterColor,
          description: context.translate.onboardingDescription1,
          image: AppImages.images.core.onboarding.onboarding1.path,
        ),
        OnBoardingEntity(
            title1: context.translate.onBoard2,
            title2: context.translate.onBoard2color,
            title3: context.translate.onBoard2AfterColor,
            description: context.translate.onboardingDescription2,
            image: AppImages.images.core.onboarding.onboarding2.path),
        OnBoardingEntity(
            title1: context.translate.onBoard3,
            title2: context.translate.onBoard3color,
            title3: context.translate.onBoard3AfterColor,
            description: context.translate.onboardingDescription2,
            image: AppImages.images.core.onboarding.onboarding3.path),
        // OnBoardingEntity(
        //     title1: context.translate.onBoard4,
        //     title2: context.translate.onBoard4color,
        //     title3: context.translate.onBoard4AfterColor,
        //     description: context.translate.onboardingDescription3,
        //     image: AppImages.images.core.onboarding.onboarding3.path),
      ];
      setState(() {});
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // MoveToBackground.moveTaskToBack();
        return false;
      },
      child: Scaffold(
          body: SizedBox(
        height: context.height,
        width: context.width,
        child: Stack(
          children: [
            PageView.builder(
              physics: const BouncingScrollPhysics(),
              controller: controller,
              onPageChanged: (value) => setState(() => currentPage = value),
              itemCount: onBoardingList.length,
              itemBuilder: (context, i) {
                return Padding(
                  padding: const EdgeInsets.all(40.0),
                  child: Column(
                    children: [
                      const Spacer(
                        flex: 9,
                      ),
                      Image.asset(
                        onBoardingList[i].image,
                        height: context.height / 3,
                      ),
                      const Spacer(
                        flex: 2,
                      ),
                      Container(
                        height: 180,
                        width: context.width / 1.5,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  onBoardingList[i].title1 + ' ',
                                  textAlign: TextAlign.center,
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeExtraLarge,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                                Text(
                                  onBoardingList[i].title2 + ' ',
                                  textAlign: TextAlign.center,
                                  style: context.textStyle.copyWith(
                                      fontSize:
                                          AppDimensions.fontSizeExtraLarge,
                                      fontWeight: FontWeightHelper.bold,
                                      color: context.color.primaryColor),
                                ),
                                Text(
                                  onBoardingList[i].title3 + ' ',
                                  textAlign: TextAlign.center,
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeExtraLarge,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            Container(
                              child: Text(
                                onBoardingList[i].description,
                                style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeSmall,
                                    fontWeight: FontWeightHelper.medium,
                                    color: context.color.descriptionColor),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(
                        flex: 10,
                      ),
                    ],
                  ),
                );
              },
            ),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Padding(
                padding: EdgeInsets.all(30),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        onBoardingList.length,
                        (int index) => BulidDots(
                          index: index,
                          currentPage: currentPage,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 50,
                    ),
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      height: 50,
                      constraints: BoxConstraints(
                          minWidth: currentPage + 1 == onBoardingList.length
                              ? 140
                              : 50),
                      decoration: BoxDecoration(
                          color: context.color.primaryColor,
                          borderRadius: BorderRadius.circular(40)),
                      // foregroundColor: context.color.whiteColor,
                      child: InkWell(
                          onTap: () {
                            if (currentPage + 1 == onBoardingList.length) {
                              SharedPref().setBoolean(
                                  key: PrefKeys.onBoardingHide, value: true);
                              context.router.replace(UserTypeViewRoute());
                              // context.router.replaceNamed(HomeView.path);
                            } else {
                              controller.nextPage(
                                duration: const Duration(milliseconds: 200),
                                curve: Curves.easeIn,
                              );
                            }
                          },
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              currentPage + 1 == onBoardingList.length
                                  ? TextApp(
                                      text: context.translate.startNow,
                                      style: TextStyle(
                                          color: context.color.whiteColor,
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              AppDimensions.fontSizeDefault),
                                    )
                                  : SizedBox(),
                              Icon(
                                Icons.arrow_forward_ios_outlined,
                                color: context.color.whiteColor,
                              ),
                            ],
                          )),
                    ),
                   ],
                ),
              ),
            ),
            Positioned(
              top: 10,
              right: 0,
              left: 0,
              child: SafeArea(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(),
                      CustomButton(
                        bgColor: Colors.transparent,
                        width: AppDimensions.fontSizeExtraLarge40,
                        onPressed: () {
                          SharedPref().setBoolean(
                              key: PrefKeys.onBoardingHide, value: true);
                          context.router.replace(UserTypeViewRoute());
                          // context.router.replaceNamed(HomeView.path);
                        },
                        child: Text(
                          context.translate.skip,
                          style: context.textStyleButton.copyWith(
                            color: context.color.descriptionColor,
                            fontWeight: FontWeightHelper.medium,
                            fontSize: AppDimensions.fontSizeLarge,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      )),
    );
  }
}
