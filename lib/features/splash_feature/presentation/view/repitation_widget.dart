import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class RepetitionVideo extends StatefulWidget {
  const RepetitionVideo({super.key});

  @override
  RepetitionVideoState createState() => RepetitionVideoState();
}

class RepetitionVideoState extends State<RepetitionVideo> {
  late VideoPlayerController _videoController;
  ChewieController? _chewieController; // Nullable to avoid errors

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    _videoController =
        VideoPlayerController.asset("assets/images/core/vidddddddd.mp4");

    try {
      await _videoController.initialize();
      setState(() {
        _chewieController = ChewieController(
          videoPlayerController: _videoController,
          autoPlay: true,
          looping: true,
          showControls: false,
        );
      });
    } catch (e) {
      debugPrint("Error initializing video: $e");
    }
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Color(0xff52b7eb),
      child: Center(
        child: _videoController.value.isInitialized && _chewieController != null
            ? SizedBox(
                width: 400,
                height: 250,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Chewie(controller: _chewieController!),
                ),
              )
            : const CircularProgressIndicator(), // Show loading indicator
      ),
    );
  }
}
