import 'dart:async';

import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:clean_arc/features/layout_feature/view/user_layout_view.dart';
import 'package:flutter/material.dart';

@RoutePage()
class SplashView extends StatefulWidget {
  static const String path = '/SplashScreen';

  const SplashView({super.key});

  @override
  SplashViewState createState() => SplashViewState();
}

class SplashViewState extends State<SplashView>
    with SingleTickerProviderStateMixin {
  var _visible = true;

  AnimationController? animationController;
  Animation<double>? animation;

  Future<Timer> _startTime() async {
    var duration = const Duration(seconds: 3);
    return Timer(duration, _navigationPage);
  }

  void _navigationPage() {
    currentUser?.value.auth == true
        ? context.router.replace(UserLayoutViewRoute())
        // ? ((currentUser?.value.user?.role == 'Driver')
        //
        //     ? context.router.replaceAll([UserLayoutViewRoute()])
        //     : context.router.replaceAll([UserLayoutViewRoute()]))
        : context.router.replace(OnBoardingViewRoute());
  }

  @override
  void initState() {
    super.initState();
    animationController =
        AnimationController(vsync: this, duration: const Duration(seconds: 4));
    animation =
        CurvedAnimation(parent: animationController!, curve: Curves.easeOut);

    animation!.addListener(() => setState(() {}));
    animationController!.forward();

    setState(() {
      _visible = !_visible;
    });
    _startTime();
  }

  @override
  void dispose() {
    animationController!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppImages.images.core.logos.appLogo.image(
                width: animation!.value * 150,
                height: animation!.value * 350,
              ),
              TextApp(
                text:
                    context.translate.becauseYouDeserveHappinessAndPeaceOfMind,
                style: context.textStyle.copyWith(
                    color: context.color.primaryColor,
                    fontWeight: FontWeightHelper.bold),
              )
            ],
          ),
        ],
      ),
    );
  }
}
