part of 'books_cubit.dart';

@freezed
class BooksState with _$BooksState {
  factory BooksState({
    ///================================== ==========================
    required bool? isLoadingFetchBooks,
    required Failure? errorFetchBooks,
    required BaseResponseModel<BaseResponseModel<List<BooksModel>>>?
        successFetchBooks,

    ///============================================================
    required bool? isLoadingFetchBookById,
    required Failure? isErrorFetchBookById,
    required BaseResponseModel<BooksModel>? successFetchBookById,
  }) = _$BooksStateImpl;

  factory BooksState.initial() => BooksState(
        errorFetchBooks: null,
        isLoadingFetchBooks: null,
        successFetchBooks: null,
        isLoadingFetchBookById: null,
        isErrorFetchBookById: null,
        successFetchBookById: null,
      );
}
