import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:clean_arc/features/book_feature/domain/model/books_model.dart';
import 'package:clean_arc/features/book_feature/domain/repository/books_repository.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/model/vycle_type_model;/vycle_type_model.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/repository/home_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'books_cubit.freezed.dart';
part 'books_state.dart'; 

@injectable
class BooksCubit extends Cubit<BooksState> {
  BooksCubit(this.repository) : super(BooksState.initial());

  final BooksRepository repository;


  Future<void> fetchBooks() async {
    emit(state.copyWith(
      isLoadingFetchBooks: true,
      successFetchBooks: null,
      errorFetchBooks: null,
    ));

    final result = await repository.fetchBooks();

    result.fold(
          (failure) {
        emit(state.copyWith(
          isLoadingFetchBooks: false,
          errorFetchBooks: failure,
          successFetchBooks: null,
        ));
      },
          (success) {
        emit(state.copyWith(
          isLoadingFetchBooks: false,
          errorFetchBooks: null,
          successFetchBooks: success,
        ));
      },
    );
  }


  Future<void> fetchBookById(int id) async {
    emit(state.copyWith(
      isLoadingFetchBookById: true,
      successFetchBookById: null,
      isErrorFetchBookById: null,
    ));
    final result = await repository.fetchBookById(id: id);
    result.fold(
          (failure) {
        emit(state.copyWith(
          isLoadingFetchBookById: false,
          isErrorFetchBookById: failure,
          successFetchBookById: null,
        ));
      },
          (success) {
        emit(state.copyWith(
          isLoadingFetchBookById: false,
          isErrorFetchBookById: null,
          successFetchBookById: success,
        ));
      },
    );
  }
}
