// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'books_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BooksState {
  ///================================== ==========================
  bool? get isLoadingFetchBooks => throw _privateConstructorUsedError;
  Failure? get errorFetchBooks => throw _privateConstructorUsedError;
  BaseResponseModel<BaseResponseModel<List<BooksModel>>>?
      get successFetchBooks => throw _privateConstructorUsedError;

  ///============================================================
  bool? get isLoadingFetchBookById => throw _privateConstructorUsedError;
  Failure? get isErrorFetchBookById => throw _privateConstructorUsedError;
  BaseResponseModel<BooksModel>? get successFetchBookById =>
      throw _privateConstructorUsedError;

  /// Create a copy of BooksState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BooksStateCopyWith<BooksState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BooksStateCopyWith<$Res> {
  factory $BooksStateCopyWith(
          BooksState value, $Res Function(BooksState) then) =
      _$BooksStateCopyWithImpl<$Res, BooksState>;
  @useResult
  $Res call(
      {bool? isLoadingFetchBooks,
      Failure? errorFetchBooks,
      BaseResponseModel<BaseResponseModel<List<BooksModel>>>? successFetchBooks,
      bool? isLoadingFetchBookById,
      Failure? isErrorFetchBookById,
      BaseResponseModel<BooksModel>? successFetchBookById});
}

/// @nodoc
class _$BooksStateCopyWithImpl<$Res, $Val extends BooksState>
    implements $BooksStateCopyWith<$Res> {
  _$BooksStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BooksState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingFetchBooks = freezed,
    Object? errorFetchBooks = freezed,
    Object? successFetchBooks = freezed,
    Object? isLoadingFetchBookById = freezed,
    Object? isErrorFetchBookById = freezed,
    Object? successFetchBookById = freezed,
  }) {
    return _then(_value.copyWith(
      isLoadingFetchBooks: freezed == isLoadingFetchBooks
          ? _value.isLoadingFetchBooks
          : isLoadingFetchBooks // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorFetchBooks: freezed == errorFetchBooks
          ? _value.errorFetchBooks
          : errorFetchBooks // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successFetchBooks: freezed == successFetchBooks
          ? _value.successFetchBooks
          : successFetchBooks // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<BaseResponseModel<List<BooksModel>>>?,
      isLoadingFetchBookById: freezed == isLoadingFetchBookById
          ? _value.isLoadingFetchBookById
          : isLoadingFetchBookById // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorFetchBookById: freezed == isErrorFetchBookById
          ? _value.isErrorFetchBookById
          : isErrorFetchBookById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successFetchBookById: freezed == successFetchBookById
          ? _value.successFetchBookById
          : successFetchBookById // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<BooksModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$$BooksStateImplImplCopyWith<$Res>
    implements $BooksStateCopyWith<$Res> {
  factory _$$$BooksStateImplImplCopyWith(_$$BooksStateImplImpl value,
          $Res Function(_$$BooksStateImplImpl) then) =
      __$$$BooksStateImplImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isLoadingFetchBooks,
      Failure? errorFetchBooks,
      BaseResponseModel<BaseResponseModel<List<BooksModel>>>? successFetchBooks,
      bool? isLoadingFetchBookById,
      Failure? isErrorFetchBookById,
      BaseResponseModel<BooksModel>? successFetchBookById});
}

/// @nodoc
class __$$$BooksStateImplImplCopyWithImpl<$Res>
    extends _$BooksStateCopyWithImpl<$Res, _$$BooksStateImplImpl>
    implements _$$$BooksStateImplImplCopyWith<$Res> {
  __$$$BooksStateImplImplCopyWithImpl(
      _$$BooksStateImplImpl _value, $Res Function(_$$BooksStateImplImpl) _then)
      : super(_value, _then);

  /// Create a copy of BooksState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingFetchBooks = freezed,
    Object? errorFetchBooks = freezed,
    Object? successFetchBooks = freezed,
    Object? isLoadingFetchBookById = freezed,
    Object? isErrorFetchBookById = freezed,
    Object? successFetchBookById = freezed,
  }) {
    return _then(_$$BooksStateImplImpl(
      isLoadingFetchBooks: freezed == isLoadingFetchBooks
          ? _value.isLoadingFetchBooks
          : isLoadingFetchBooks // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorFetchBooks: freezed == errorFetchBooks
          ? _value.errorFetchBooks
          : errorFetchBooks // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successFetchBooks: freezed == successFetchBooks
          ? _value.successFetchBooks
          : successFetchBooks // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<BaseResponseModel<List<BooksModel>>>?,
      isLoadingFetchBookById: freezed == isLoadingFetchBookById
          ? _value.isLoadingFetchBookById
          : isLoadingFetchBookById // ignore: cast_nullable_to_non_nullable
              as bool?,
      isErrorFetchBookById: freezed == isErrorFetchBookById
          ? _value.isErrorFetchBookById
          : isErrorFetchBookById // ignore: cast_nullable_to_non_nullable
              as Failure?,
      successFetchBookById: freezed == successFetchBookById
          ? _value.successFetchBookById
          : successFetchBookById // ignore: cast_nullable_to_non_nullable
              as BaseResponseModel<BooksModel>?,
    ));
  }
}

/// @nodoc

class _$$BooksStateImplImpl implements _$BooksStateImpl {
  _$$BooksStateImplImpl(
      {required this.isLoadingFetchBooks,
      required this.errorFetchBooks,
      required this.successFetchBooks,
      required this.isLoadingFetchBookById,
      required this.isErrorFetchBookById,
      required this.successFetchBookById});

  ///================================== ==========================
  @override
  final bool? isLoadingFetchBooks;
  @override
  final Failure? errorFetchBooks;
  @override
  final BaseResponseModel<BaseResponseModel<List<BooksModel>>>?
      successFetchBooks;

  ///============================================================
  @override
  final bool? isLoadingFetchBookById;
  @override
  final Failure? isErrorFetchBookById;
  @override
  final BaseResponseModel<BooksModel>? successFetchBookById;

  @override
  String toString() {
    return 'BooksState(isLoadingFetchBooks: $isLoadingFetchBooks, errorFetchBooks: $errorFetchBooks, successFetchBooks: $successFetchBooks, isLoadingFetchBookById: $isLoadingFetchBookById, isErrorFetchBookById: $isErrorFetchBookById, successFetchBookById: $successFetchBookById)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$$BooksStateImplImpl &&
            (identical(other.isLoadingFetchBooks, isLoadingFetchBooks) ||
                other.isLoadingFetchBooks == isLoadingFetchBooks) &&
            (identical(other.errorFetchBooks, errorFetchBooks) ||
                other.errorFetchBooks == errorFetchBooks) &&
            (identical(other.successFetchBooks, successFetchBooks) ||
                other.successFetchBooks == successFetchBooks) &&
            (identical(other.isLoadingFetchBookById, isLoadingFetchBookById) ||
                other.isLoadingFetchBookById == isLoadingFetchBookById) &&
            (identical(other.isErrorFetchBookById, isErrorFetchBookById) ||
                other.isErrorFetchBookById == isErrorFetchBookById) &&
            (identical(other.successFetchBookById, successFetchBookById) ||
                other.successFetchBookById == successFetchBookById));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoadingFetchBooks,
      errorFetchBooks,
      successFetchBooks,
      isLoadingFetchBookById,
      isErrorFetchBookById,
      successFetchBookById);

  /// Create a copy of BooksState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$$BooksStateImplImplCopyWith<_$$BooksStateImplImpl> get copyWith =>
      __$$$BooksStateImplImplCopyWithImpl<_$$BooksStateImplImpl>(
          this, _$identity);
}

abstract class _$BooksStateImpl implements BooksState {
  factory _$BooksStateImpl(
          {required final bool? isLoadingFetchBooks,
          required final Failure? errorFetchBooks,
          required final BaseResponseModel<BaseResponseModel<List<BooksModel>>>?
              successFetchBooks,
          required final bool? isLoadingFetchBookById,
          required final Failure? isErrorFetchBookById,
          required final BaseResponseModel<BooksModel>? successFetchBookById}) =
      _$$BooksStateImplImpl;

  ///================================== ==========================
  @override
  bool? get isLoadingFetchBooks;
  @override
  Failure? get errorFetchBooks;
  @override
  BaseResponseModel<BaseResponseModel<List<BooksModel>>>? get successFetchBooks;

  ///============================================================
  @override
  bool? get isLoadingFetchBookById;
  @override
  Failure? get isErrorFetchBookById;
  @override
  BaseResponseModel<BooksModel>? get successFetchBookById;

  /// Create a copy of BooksState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$$BooksStateImplImplCopyWith<_$$BooksStateImplImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
