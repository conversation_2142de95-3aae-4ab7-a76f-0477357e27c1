import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/presentation/widget/price_widget.dart';
import 'package:clean_arc/core/presentation/widget/shimmer/shimar_widget.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/book_feature/controller/books_cubit.dart';
import 'package:clean_arc/features/book_feature/domain/model/books_model.dart';
import 'package:clean_arc/features/book_feature/presentation/items/book_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/presentation/widget/cached_network_image_utill.dart';

class BooksListShimmerComponent extends StatefulWidget {
  const BooksListShimmerComponent({super.key});

  @override
  State<BooksListShimmerComponent> createState() =>
      _BooksListShimmerComponentState();
}

class _BooksListShimmerComponentState extends State<BooksListShimmerComponent> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    BooksCubit booksCubit = context.watch<BooksCubit>();
    return BlocBuilder<BooksCubit, BooksState>(
      builder: (context, state) {
        return ListView.builder(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: 10,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: context.color.whiteColor,
                  // image: DecorationImage(
                  //     fit: BoxFit.fill,
                  //     image: AssetImage(
                  //         AppImages.images.svgIcon.homeImageHeader.path)
                  //
                  // ),
                  boxShadow: [
                    BoxShadow(
                      color: context.color.borderColor!,
                      spreadRadius: 1,
                    )
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Container(
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomShimmerWidget(
                                child: TextApp(
                                  text: 'name' ?? '',
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeDefault,
                                    // color: context.color.whiteColor,
                                    fontWeight: FontWeightHelper.bold,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              CustomShimmerWidget(
                                child: TextApp(
                                  text: 'description' ?? '',
                                  style: context.textStyle.copyWith(
                                    fontSize: AppDimensions.fontSizeDefault,
                                    fontWeight: FontWeightHelper.regular,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              CustomShimmerWidget(
                                child: TextPrice(
                                  price: '100' ?? '',
                                  color: context.color.primaryColor,
                                  fontSize: AppDimensions.fontSizeLarge,
                                  fontWeight: FontWeightHelper.bold,
                                ),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                children: [
                                  CustomShimmerWidget(
                                    child: TextApp(
                                      text: '(263)',
                                      style: context.textStyle.copyWith(
                                          fontSize:
                                              AppDimensions.fontSizeDefault,
                                          fontWeight: FontWeightHelper.regular,
                                          color:
                                              context.color.descriptionColor),
                                    ),
                                  ),
                                  AppImages.images.demo.rateBar.image()
                                ],
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              CustomShimmerWidget(
                                child: CustomButton(
                                    width: 110,
                                    height: 40,
                                    onPressed: () {},
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        TextApp(
                                          text: context.translate.startNow,
                                          style: context.textStyleButton,
                                        ),
                                        Icon(
                                          Icons.arrow_forward,
                                          color: context.color.whiteColor,
                                        ),
                                      ],
                                    )),
                              )
                            ],
                          ),
                        ),
                        // CustomCachedNetworkImage(imageUrl: booksModel?.image ?? '',height: 180,width: 138,)
                        CustomShimmerWidget(
                            child: AppImages.images.demo.book.image(
                          height: 200,
                          fit: BoxFit.cover,
                        ))
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
