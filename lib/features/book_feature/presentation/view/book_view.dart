import 'package:clean_arc/app.dart';
// import 'package:clean_arc/core/external_lip/location_picker/widgets/near_by_place_item.dart';
import 'package:clean_arc/core/external_lip/magnfier/magnifier.dart' as mag;
import 'package:clean_arc/core/external_lip/magnfier/magnifierPainters.dart';
import 'package:clean_arc/core/presentation/widget/custom_loading.dart';
import 'package:clean_arc/core/presentation/widget/empty_widget.dart';
import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/core/presentation/widget/shimmer/shimar_widget.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/book_feature/controller/books_cubit.dart';
import 'package:clean_arc/features/book_feature/presentation/componant/page_flip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:page_flip/page_flip.dart';
import 'package:url_launcher/url_launcher.dart';

// import '../../../../core/external_lip/magnifier/magnifier.dart' as mag;
@RoutePage()
class BookView extends StatefulWidget {
  int id;

  BookView({Key? key, required this.id}) : super(key: key);

  @override
  State<BookView> createState() => _BookViewState();
}

class _BookViewState extends State<BookView>
    with SingleTickerProviderStateMixin {
  // VIPLocalDataSourceImpl data = VIPLocalDataSourceImpl();
  @override
  void initState() {
    // print(SharedData.lang);
    // BlocProvider.of<UserDataBloc>(context)
    //     .add(GetUserDataEvent(SharedData.user?.id ?? ''));

    context.read<BooksCubit>().fetchBookById(widget.id);
    // BlocProvider.of<ImagesBloc>(context)
    //     .add(FetchImagesEvent(widget.cat, SharedData.lang ?? 'en'));
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  TapDownDetails? tapDownDetails;
  final _controller = GlobalKey<PageFlipWidgetState>();

  ValueNotifier<int> diameter = ValueNotifier(150);
  ValueNotifier<double> distortion = ValueNotifier(1.0);
  ValueNotifier<double> magnification = ValueNotifier(1.2);

  bool enabled = false;

  @override
  Widget build(BuildContext context) {
    return mag.Magnifier(
      enabled: enabled,
      painter: CrosshairMagnifierPainter(
          color: context.color.primaryColor!, strokeWidth: 5),
      scale: 1.3,
      size: const Size(0, 0),
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              children: [
                Expanded(
                  child: BlocBuilder<BooksCubit, BooksState>(
                    builder: (context, state) {
                      if (state.isLoadingFetchBookById == true) {
                        return CustomShimmerWidget(
                          child: AppImages.images.demo.book.image(
                              fit: BoxFit.fill,
                              width: double.infinity,
                              height: double.infinity),
                        );
                      } else if (state.isErrorFetchBookById != null) {
                        return CustomErrorWidget(
                            failure: state.isErrorFetchBookById,
                            onPressed: () {});
                      } else if (state.successFetchBookById != null) {
                        return (state.successFetchBookById?.data?.pages
                                    ?.isNotEmpty ??
                                true)
                            ? PageFlipWidget(
                                key: _controller,
                                backgroundColor: Colors.white,
                                lastPage:
                                    const Center(child: Text('Thank you')),
                                children: <Widget>[
                                  for (var i = 0;
                                      i <
                                          (state.successFetchBookById?.data
                                                  ?.pages?.length ??
                                              0);
                                      i++)
                                    GestureDetector(
                                      onDoubleTapDown: (details) =>
                                          tapDownDetails = details,
                                      onDoubleTap: () {},
                                      child: Stack(
                                        children: [
                                          Stack(
                                            fit: StackFit.expand,
                                            children: [
                                              PageSwapPdf(
                                                page: state
                                                        .successFetchBookById
                                                        ?.data
                                                        ?.pages![i]
                                                        .pageImage ??
                                                    "",
                                                id: state
                                                        .successFetchBookById
                                                        ?.data
                                                        ?.pages![i]
                                                        .pageImage ??
                                                    "",
                                              ),
                                            ],
                                          ),
                                          SafeArea(
                                            child: CircleAvatar(
                                              child: IconButton(
                                                  onPressed: () {
                                                    setState(() {
                                                      // print(state
                                                      //     .model[i].imageUrl);
                                                      enabled = !enabled;
                                                    });
// magnifyingGlassController.openGlass();
                                                  },
                                                  icon: const Text(
                                                    "🔍",
                                                    style: TextStyle(
                                                        fontSize: 20,
                                                        fontWeight:
                                                            FontWeight.bold),
                                                  )),
                                            ),
                                          ),
                                          // isSuberAdmin == true
                                          //     ? Positioned(
                                          //         top: 250,
                                          //         right: 0,
                                          //         child: Container(
                                          //           color: Colors.grey,
                                          //           height: 40,
                                          //           child: InkWell(
                                          //               onTap: () async {
                                          //                 await data
                                          //                     .deleteImage(state
                                          //                             .model[i]
                                          //                             .id ??
                                          //                         "")
                                          //                     .then((value) {
                                          //                   state.model
                                          //                       .removeAt(i);
                                          //                   setState(() {});
                                          //                   return Fluttertoast
                                          //                       .showToast(
                                          //                     msg:
                                          //                         'image removed successfully',
                                          //                     toastLength: Toast
                                          //                         .LENGTH_SHORT,
                                          //                     gravity:
                                          //                         ToastGravity
                                          //                             .BOTTOM,
                                          //                     timeInSecForIosWeb:
                                          //                         4,
                                          //                     backgroundColor:
                                          //                         AppColors
                                          //                             .orangeColor,
                                          //                     textColor:
                                          //                         Colors.white,
                                          //                     fontSize: 16.0,
                                          //                   );
                                          //                 }).whenComplete(() {
                                          //                   setState(() {});
                                          //                 });
                                          //               },
                                          //               child:
                                          //                   const CircleAvatar(
                                          //                       // radius: 20,
                                          //                       child: Icon(
                                          //                 Icons.delete,
                                          //                 color: Colors.red,
                                          //               ))),
                                          //         ),
                                          //       )
                                          //     : SizedBox()

                                          // isSuberAdmin==true?      Positioned(left:0,child: Container(color:Colors.green,child: IconButton(onPressed: (){
                                          //   // print("${state.model[i].id } state.model[i].imageUrl");
                                          // }, icon: Icon(Icons.vaccines)))):false,

                                          // Positioned(
                                          //   right: 4.0,
                                          //   top: 4.0,
                                          //   child: SafeArea(
                                          //     child: BlocBuilder<UserDataBloc,
                                          //         FetchUserDataState>(
                                          //       builder: (context, state) {
                                          //         if (state is UserDataLoadedState) {
                                          //           return state.user?.isSuperAdmin == true?
                                          //           CircleAvatar(
                                          //             child: IconButton(
                                          //                 onPressed: () {
                                          //                   // log('///////////////////////////////////');
                                          //                   //
                                          //                   // log('printed data expireDate= ${state.user?.expireDate}');
                                          //                   // log('printed data phone= ${state.user?.phone}');
                                          //                   // log('printed data email= ${state.user?.email}');
                                          //                   // log('printed data isVip= ${state.user?.isVip}');
                                          //                   // log('printed data id= ${state.user?.id}');
                                          //                   // log('printed data isSuperAdmin= ${state.user?.isSuperAdmin}');
                                          //                   // log('printed data isAdmin= ${state.user?.isAdmin}');
                                          //                   //
                                          //                   // log('/////////////////////////');
                                          //
                                          //                 },
                                          //                 icon: const Icon(
                                          //                   Icons.delete,
                                          //                   color: Colors.red,
                                          //                 )),
                                          //           ): Container() ;
                                          //         }
                                          //        return Container();
                                          //       },
                                          //     ),
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    ),
                                ],
                              )
                            : CustomEmptyWidget(title: 'Coming soon');
                      }
                      return Image.asset(
                        'asset/loading.gif',
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
