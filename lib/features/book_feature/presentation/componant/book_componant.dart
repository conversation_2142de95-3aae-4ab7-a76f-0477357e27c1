import 'package:clean_arc/core/presentation/widget/error_widget.dart';
import 'package:clean_arc/features/book_feature/controller/books_cubit.dart';
import 'package:clean_arc/features/book_feature/domain/model/books_model.dart';
import 'package:clean_arc/features/book_feature/presentation/items/book_item.dart';
import 'package:clean_arc/features/book_feature/presentation/shimmer/book_shimmer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BooksListComponent extends StatefulWidget {
  const BooksListComponent({super.key});

  @override
  State<BooksListComponent> createState() => _BooksListComponentState();
}

class _BooksListComponentState extends State<BooksListComponent> {
  @override
  void initState() {
    context.read<BooksCubit>().fetchBooks();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    BooksCubit booksCubit = context.watch<BooksCubit>();
    return BlocBuilder<BooksCubit, BooksState>(
      builder: (context, state) {
        if (state.errorFetchBooks != null)
          return CustomErrorWidget(
            failure: state.errorFetchBooks,
            onPressed: () {
              booksCubit.fetchBooks();
            },
          );

        if (state.isLoadingFetchBooks == true)
          return const Center(
            child: BooksListShimmerComponent(),
          );

        if (state.successFetchBooks != null)
          return Column(
            children: ((state.successFetchBooks?.data!.data ?? []))
                .map((e) => BookItem(
                      booksModel: e,
                    ))
                .toList(),
          );

        //   ListView.separated(
        //   separatorBuilder: (context, index) => const SizedBox(
        //     height: 16,
        //   ),
        //   itemCount: state.successFetchBooks?.data?.length ?? 0,
        //   itemBuilder: (context, index) {
        //     BooksModel? book = state.successFetchBooks?.data?[index];
        //     return BookItem(
        //       booksModel: book,
        //     );
        //   },
        // );
        return SizedBox();
      },
    );
  }
}
