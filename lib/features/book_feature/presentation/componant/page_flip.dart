import 'package:cached_network_image/cached_network_image.dart';
import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:flutter/material.dart';

class PageSwapPdf extends StatefulWidget {
  final String page;
  final String id;

  const PageSwapPdf({Key? key, required this.page, required this.id})
      : super(key: key);

  @override
  State<PageSwapPdf> createState() => _PageSwapPdfState();
}

class _PageSwapPdfState extends State<PageSwapPdf> {
  bool isSwitched = false;
  // VIPLocalDataSourceImpl data = VIPLocalDataSourceImpl();

  void toggleSwitch(bool value) {
    if (isSwitched == false) {
      setState(() {
        isSwitched = true;
      });
    } else {
      setState(() {
        isSwitched = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 2),
          width: MediaQuery.of(context).size.width,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(3.0),
            child: CustomCachedNetworkImage(
              fit: BoxFit.fill,
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              imageUrl: widget.page ?? '',
              // placeholder: (context, url) =>
              // const Center(child: CircularProgressIndicator()),
              // errorWidget: (context, url, error) => const Icon(
              //   Icons.error,
              //   color: Colors.white,
              // ),
            ),
          ),
        ),
      ],
    );
  }
}
