import 'package:clean_arc/core/presentation/widget/cached_network_image_utill.dart';
import 'package:clean_arc/core/presentation/widget/price_widget.dart';
import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/book_feature/domain/model/books_model.dart';
import 'package:clean_arc/features/user_layout/lip_feature/controller/lib_controller.dart';
import 'package:clean_arc/features/user_layout/lip_feature/domain/model/book_cach_model.dart';
import 'package:flutter/material.dart';

class BookItem extends StatelessWidget {
  final BooksModel? booksModel;

  const BookItem({super.key, required this.booksModel});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: InkWell(
        onTap: () {
          context.pushRoute(BookViewRoute(id: booksModel!.id!));
        },
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: context.color.whiteColor,
                // image: DecorationImage(
                //     fit: BoxFit.fill,
                //     image: AssetImage(
                //         AppImages.images.svgIcon.homeImageHeader.path)
                //
                // ),
                boxShadow: [
                  BoxShadow(
                    color: context.color.borderColor!,
                    spreadRadius: 1,
                  )
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Container(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextApp(
                              text: booksModel?.name ?? '',
                              style: context.textStyle.copyWith(
                                fontSize: AppDimensions.fontSizeDefault,
                                // color: context.color.whiteColor,
                                fontWeight: FontWeightHelper.bold,
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            TextApp(
                              text: booksModel?.description ?? '',
                              style: context.textStyle.copyWith(
                                fontSize: AppDimensions.fontSizeDefault,
                                fontWeight: FontWeightHelper.regular,
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            TextPrice(
                              price: booksModel?.originalPrice ?? '',
                              color: context.color.primaryColor,
                              fontSize: AppDimensions.fontSizeLarge,
                              fontWeight: FontWeightHelper.bold,
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Row(
                              children: [
                                TextApp(
                                  text: '(263)',
                                  style: context.textStyle.copyWith(
                                      fontSize: AppDimensions.fontSizeDefault,
                                      fontWeight: FontWeightHelper.regular,
                                      color: context.color.descriptionColor),
                                ),
                                AppImages.images.demo.rateBar.image()
                              ],
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            CustomButton(
                                width: 110,
                                height: 40,
                                onPressed: () {
                                  context.pushRoute(
                                      BookViewRoute(id: booksModel!.id!));
                                },
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    TextApp(
                                      text: context.translate.startNow,
                                      style: context.textStyleButton,
                                    ),
                                    Icon(
                                      Icons.arrow_forward,
                                      color: context.color.whiteColor,
                                    ),
                                  ],
                                ))
                          ],
                        ),
                      ),
                      CustomCachedNetworkImage(
                        imageUrl: booksModel?.image ?? '',
                        height: 180,
                        width: 138,
                      )
                      // AppImages.images.demo.book.image(height: 200, fit: BoxFit.cover,)
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                BookManage.toggleBook(
                    BooksCashModel.fromJson(booksModel?.toJson() ?? {}));
              },
              child: ValueListenableBuilder<Set<int>>(
                valueListenable: BookManage.savedBooks,
                builder: (context, savedBooks, child) {
                  return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Icon(
                      savedBooks.contains(booksModel?.id)
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                      color: context.color.yellowColor,
                    ),
                  );
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
