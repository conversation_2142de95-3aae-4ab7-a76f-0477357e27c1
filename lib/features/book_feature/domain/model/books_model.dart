class BooksModel {
  BooksModel({
    required this.id,
    required this.name,
    required this.description,
    required this.originalPrice,
    required this.image,
    required this.imageSecondary,
    required this.isExists,
    required this.createdAt,
    required this.updatedAt,
    required this.totalPages,
    required this.discountOptions,
    required this.pages,
  });

  final int? id;
  final String? name;
  final String? description;
  final String? originalPrice;
  final String? image;
  final String? imageSecondary;
  final num? isExists;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final num? totalPages;
  final List<DiscountOption> discountOptions;
  final List<Page> pages;

  factory BooksModel.fromJson(Map<String, dynamic> json){
    return BooksModel(
      id: json["id"],
      name: json["name"],
      description: json["description"],
      originalPrice: json["original_price"],
      image: json["image"],
      imageSecondary: json["image_secondary"],
      isExists: json["is_exists"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      totalPages: json["totalPages"],
      discountOptions: json["discount_options"] == null ? [] : List<DiscountOption>.from(json["discount_options"]!.map((x) => DiscountOption.fromJson(x))),
      pages: json["pages"] == null ? [] : List<Page>.from(json["pages"]!.map((x) => Page.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "description": description,
    "original_price": originalPrice,
    "image": image,
    "image_secondary": imageSecondary,
    "is_exists": isExists,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "totalPages": totalPages,
    "discount_options": discountOptions.map((x) => x?.toJson()).toList(),
    "pages": pages.map((x) => x?.toJson()).toList(),
  };

}

class DiscountOption {
  DiscountOption({
    required this.subscriptionType,
    required this.discountPercentage,
    required this.discountedPrice,
  });

  final String? subscriptionType;
  final num? discountPercentage;
  final num? discountedPrice;

  factory DiscountOption.fromJson(Map<String, dynamic> json){
    return DiscountOption(
      subscriptionType: json["subscription_type"],
      discountPercentage: json["discount_percentage"],
      discountedPrice: json["discounted_price"],
    );
  }

  Map<String, dynamic> toJson() => {
    "subscription_type": subscriptionType,
    "discount_percentage": discountPercentage,
    "discounted_price": discountedPrice,
  };

}

class Page {
  Page({
    required this.bookId,
    required this.pageImage,
  });

  final int? bookId;
  final String? pageImage;

  factory Page.fromJson(Map<String, dynamic> json){
    return Page(
      bookId: json["book_id"],
      pageImage: json["page_image"],
    );
  }

  Map<String, dynamic> toJson() => {
    "book_id": bookId,
    "page_image": pageImage,
  };

}
