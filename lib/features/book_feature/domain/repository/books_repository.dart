import 'package:clean_arc/core/data/model/base_response/base_response.dart';
import 'package:clean_arc/core/data/repositories/base_repository_impl.dart';
import 'package:clean_arc/core/domain/entity/failures.dart';
import 'package:clean_arc/core/domain/repositories/base_repository.dart';
import 'package:clean_arc/features/auth_feature/domain/model/message_model/response_message_model.dart';
import 'package:clean_arc/features/auth_feature/domain/model/user_model/user_model.dart';
import 'package:clean_arc/features/auth_feature/domain/services/remote/login_remote_data_source.dart';
import 'package:clean_arc/features/book_feature/domain/model/books_model.dart';
import 'package:clean_arc/features/book_feature/domain/services/remote/books_remote_data_source.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/model/vycle_type_model;/vycle_type_model.dart';
import 'package:clean_arc/features/user_layout/home_feature/domain/services/remote/home_remote_data_source.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

abstract class BooksRepository {
  Future<Either<Failure, BaseResponseModel<BaseResponseModel<List<BooksModel>>>>> fetchBooks();
  Future<Either<Failure, BaseResponseModel<BooksModel>>> fetchBookById({
    required int id,
  });
}

@LazySingleton(as: BooksRepository)
class BooksRepositoryImpl extends BaseRepositoryImpl implements BooksRepository {
  final BooksServices _services;

  BooksRepositoryImpl(super.logger, this._services);

  Future<Either<Failure, BaseResponseModel<BaseResponseModel<List<BooksModel>>>>> fetchBooks() {
    return request(() async {
      final result = await _services.fetchBooks();
      return Right(result);
    });
  }


  Future<Either<Failure, BaseResponseModel<BooksModel>>> fetchBookById({
    required int id,
  }) {
    return request(() async {
      final result = await _services.fetchBookById(id: id);
      return Right(result);
    });
  }
}
