import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/layout_feature/cubit_cubit/layout_cubit.dart';
import 'package:clean_arc/features/player_feature/domain/model/song_model.dart';
import 'package:clean_arc/features/player_feature/domain/services/songs_data.dart';
import 'package:clean_arc/features/profile_feature/presentation/view/profile_view.dart';
import 'package:clean_arc/features/user_layout/home_feature/presentation/view/my_fav.dart';
import 'package:clean_arc/features/user_layout/home_feature/presentation/view/my_lip.dart';
import 'package:clean_arc/features/user_layout/home_feature/presentation/view/user_home_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:google_nav_bar/google_nav_bar.dart';

@RoutePage()
class UserLayoutView extends StatefulWidget {
  static const String path = "/UserLayoutView";

  const UserLayoutView({super.key});

  @override
  State<UserLayoutView> createState() => _UserLayoutViewState();
}

class _UserLayoutViewState extends State<UserLayoutView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final List<SongModel> allsongs =
        songs.map((e) => SongModel.fromJson(e)).toList();
    late List<Widget> _children = [
      UserHomeView(),
      // FreeTryingView(),
      // ArtistProfile(
      //   title: '',
      //   soung: [
      //     SongModel(
      //         album:
      //             'https://images.unsplash.com/photo-1546707012-c46675f12716?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwyNjQwNTF8MHwxfHNlYXJjaHwyNHx8Y29uY2VydHxlbnwwfHx8fDE2MzI5MTA0OTM&ixlib=rb-1.2.1&q=80&w=1080',
      //         artist: 'ZakharValaha',
      //         song: 'Cinematic Fairy Tale Story (Main)1',
      //         url:
      //             'https://cdn.pixabay.com/download/audio/2021/09/25/audio_153f263349.mp3?filename=cinematic-fairy-tale-story-main-8697.mp3'),
      //     SongModel(
      //         album:
      //             'https://images.unsplash.com/photo-1546707012-c46675f12716?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwyNjQwNTF8MHwxfHNlYXJjaHwyNHx8Y29uY2VydHxlbnwwfHx8fDE2MzI5MTA0OTM&ixlib=rb-1.2.1&q=80&w=1080',
      //         artist: 'ZakharValaha',
      //         song: 'Cinematic Fairy Tale Story (Main)2',
      //         url:
      //             'https://cdn.pixabay.com/download/audio/2021/09/25/audio_153f263349.mp3?filename=cinematic-fairy-tale-story-main-8697.mp3'),
      //     SongModel(
      //         album:
      //             'https://images.unsplash.com/photo-1546707012-c46675f12716?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwyNjQwNTF8MHwxfHNlYXJjaHwyNHx8Y29uY2VydHxlbnwwfHx8fDE2MzI5MTA0OTM&ixlib=rb-1.2.1&q=80&w=1080',
      //         artist: 'ZakharValaha',
      //         song: 'Cinematic Fairy Tale Story (Main)3',
      //         url:
      //             'https://cdn.pixabay.com/download/audio/2021/09/25/audio_153f263349.mp3?filename=cinematic-fairy-tale-story-main-8697.mp3'),
      //     SongModel(
      //         album:
      //             'https://images.unsplash.com/photo-1546707012-c46675f12716?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwyNjQwNTF8MHwxfHNlYXJjaHwyNHx8Y29uY2VydHxlbnwwfHx8fDE2MzI5MTA0OTM&ixlib=rb-1.2.1&q=80&w=1080',
      //         artist: 'ZakharValaha',
      //         song: 'Cinematic Fairy Tale Story (Main)4',
      //         url:
      //             'https://cdn.pixabay.com/download/audio/2021/09/25/audio_153f263349.mp3?filename=cinematic-fairy-tale-story-main-8697.mp3'),
      //   ],
      //   // allsongs[1].artist??'',
      // ),

      MyLibrary(),
      MyFav(),
      // CheckoutView(),
      // DriverView(),
      // MyWalletView(),
      const ProfileView(),
      // const UserProfileView(),
    ];

    return BlocBuilder<LayoutCubit, LayoutState>(
      // bloc: ,
      builder: (BuildContext context, state) {
        final homeProvider = context.read<LayoutCubit>();
        return Scaffold(
          body: _children![homeProvider.currentIndex],
          bottomNavigationBar: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5), // Shadow color
                  spreadRadius: 2, // Spread radius
                  blurRadius: 10, // Blur radius
                  offset: Offset(0, 3), // Offset from top
                ),
              ],
            ),
            child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                child: GNav(
                  gap: 8,
                  tabBorderRadius: 15,
                  // المسافة بين الأيقونة والنص
                  backgroundColor: Colors.white,
                  color: context.color.hintColor,
                  // اللون الافتراضي للأيقونات
                  activeColor: context.color.primaryColor,
                  // اللون عند التحديد
                  iconSize: 24,
                  // حجم الأيقونة
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  // الهوامش الداخلية
                  tabBackgroundColor:
                      context.color.primaryColor!.withOpacity(0.1),
                  // لون الخلفية عند التحديد
                  selectedIndex: homeProvider.currentIndex,
                  // العنصر المحدد
                  onTabChange: homeProvider.changePageIndex,
                  // تغيير الصفحة عند التبديل
                  tabs: [
                    GButton(
                      leading: AppImages.images.core.home.image(
                          color: homeProvider.currentIndex == 0
                              ? context.color.primaryColor
                              : context.color.hintColor?.withOpacity(.5)),
                      icon: Icons.home,
                      text: 'الرئيسية',
                      iconColor: homeProvider.currentIndex == 0
                          ? context.color.primaryColor
                          : context.color.hintColor,
                    ),
                    GButton(
                      leading: Icon(IconlyBold.bookmark,
                          color: homeProvider.currentIndex == 1
                              ? context.color.primaryColor
                              : context.color.hintColor?.withOpacity(.5)),
                      // AppImages.images.core.video1.image(
                      //     color: homeProvider.currentIndex == 1
                      //         ? context.color.primaryColor
                      //         : context.color.hintColor?.withOpacity(.5))

                      // ,
                      icon: Icons.list_alt,
                      text: 'المكتبة',
                      iconColor: homeProvider.currentIndex == 2
                          ? context.color.primaryColor
                          : context.color.hintColor,
                    ),
                    GButton(
                      leading: Icon(IconlyBold.heart,
                          color: homeProvider.currentIndex == 2
                              ? context.color.primaryColor
                              : context.color.hintColor?.withOpacity(.5))
                      // AppImages.images.core.vector.image(
                      //     color: homeProvider.currentIndex == 2
                      //         ? context.color.primaryColor
                      //         : context.color.hintColor?.withOpacity(.5))
                      ,

                      // leading: Icon(Icons.add_circle),
                      icon: Icons.account_balance_wallet,
                      text: 'مفضلتي',
                      iconColor: homeProvider.currentIndex == 2
                          ? context.color.primaryColor
                          : context.color.hintColor,
                    ),
                    GButton(
                      leading: AppImages.images.core.profile.image(
                          color: homeProvider.currentIndex == 3
                              ? context.color.primaryColor
                              : context.color.hintColor?.withOpacity(.5)),
                      icon: Icons.settings,
                      text: 'الملف الشخصي',
                      iconColor: homeProvider.currentIndex == 3
                          ? context.color.primaryColor
                          : context.color.hintColor,
                    ),
                  ],
                )),
          ),
        );
      },
    );
  }
}
