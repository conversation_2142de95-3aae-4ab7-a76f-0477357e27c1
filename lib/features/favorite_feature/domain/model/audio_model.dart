import 'package:hive/hive.dart';

part 'audio_model.g.dart';

@HiveType(typeId: 0)
class AudioModel {
  @HiveField(0)
  final String ?id; // معرف فريد
  @HiveField(1)
  final String ?title; // اسم الملف الصوتي
  @HiveField(2)
  final String ? url; // رابط الأوديو
  // @HiveField(3)
  // final String duration; // المدة الزمنية

  AudioModel({
    required this.id,
    required this.title,
    required this.url,
    // required this.duration,
  });
}
