import 'package:clean_arc/features/favorite_feature/domain/model/audio_model.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';

class FavoritesManager {
  static late Box<AudioModel> _audioBox;
  static late Box<List<String>> _stringsBox;

  /// **تهيئة Hive**
  static Future<void> init() async {
    await Hive.initFlutter();
    Hive.registerAdapter(AudioModelAdapter());
    _audioBox = await Hive.openBox<AudioModel>('favorite_audios');
    _stringsBox = await Hive.openBox<List<String>>('favorite_strings');
  }

  /// **إضافة أوديو إلى المفضلة**
  static Future<void> addAudioToFavorites(AudioModel audio) async {
    await _audioBox.put(audio.id, audio);
  }

  /// **إزالة أوديو من المفضلة**
  static Future<void> removeAudioFromFavorites(String id) async {
    await _audioBox.delete(id);
  }

  /// **جلب جميع الملفات الصوتية المحفوظة**
  static List<AudioModel> getFavoriteAudios() {
    return _audioBox.values.toList();
  }

  /// **إضافة قائمة من الـ Strings إلى المفضلة**
  static Future<void> addStringListToFavorites(List<String> strings) async {
    await _stringsBox.put('fav_strings', strings);
  }

  /// **جلب قائمة الـ Strings من المفضلة**
  static List<String> getFavoriteStrings() {
    return _stringsBox.get('fav_strings', defaultValue: [])!;
  }

  /// **حذف قائمة الـ Strings من المفضلة**
  static Future<void> removeStringListFromFavorites() async {
    await _stringsBox.delete('fav_strings');
  }
}
