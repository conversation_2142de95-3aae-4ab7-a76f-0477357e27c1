import 'package:clean_arc/core/app/app_cubit/app_cubit.dart';
import 'package:clean_arc/core/app/connectivity_controller.dart';
import 'package:clean_arc/core/app/env.variable.dart';
import 'package:clean_arc/core/data/services/shared_prefs/shared_pref.dart';
import 'package:clean_arc/core/data/services/shared_prefs/shared_prefs_key.dart';
import 'package:clean_arc/core/presentation/screen/no_network_screen.dart';
import 'package:clean_arc/core/routing/app_router.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:clean_arc/features/book_feature/controller/books_cubit.dart';
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart';
import 'package:clean_arc/features/distraction_feature/controller/distraction_cubit.dart';
import 'package:clean_arc/features/layout_feature/cubit_cubit/layout_cubit.dart';
import 'package:clean_arc/features/lecture_feature/controller/lecture_cubit.dart';
import 'package:clean_arc/features/meditation_feature/controller/meditations_cubit.dart';
import 'package:clean_arc/features/player_feature/controller/now_playing_controller.dart';
import 'package:clean_arc/features/treatment_feature/controller/treatment_cubit.dart';
import 'package:clean_arc/features/user_layout/home_feature/controller/user_home_cubit.dart';
// import 'package:clean_arc/features/user_layout/order_feature/controller/user_order_cubit.dart';
import 'package:clean_arc/my_global_cubit/thought_cubit.dart';
import 'package:clean_arc/injection/injection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AgzgantyApp extends StatelessWidget {
  // const MyApp.AgzgantyApp({super.key});

  // This widget is the root of your application.

  final _appRouter = AppRouter();

  @override
  Widget build(BuildContext context) {
    // AppRouter appRouter=AppRouter();
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<AppCubit>()
            ..changeAppThemeMode(
              sharedMode:
                  SharedPref.preferences.getBoolean(PrefKeys.themeMode) ??
                      false,
            )
            ..getSavedLanguage(),
        ),
        BlocProvider(create: (context) => getIt<AuthCubit>()..getCurrentUser()),
        BlocProvider(create: (context) => getIt<LayoutCubit>()),
        BlocProvider(
            create: (context) => getIt<NowPlayingController>()..init()),
        BlocProvider(create: (context) => getIt<ConcernsCubit>()),
        BlocProvider(create: (context) => getIt<ThoughtCubit>()),
        BlocProvider(create: (context) => getIt<UserHomeCubit>()),
        BlocProvider(create: (context) => getIt<BooksCubit>()),
        BlocProvider(create: (context) => getIt<MeditationsCubit>()),
        BlocProvider(create: (context) => getIt<TreatmentCubit>()),
        BlocProvider(create: (context) => getIt<LectureCubit>()),
        BlocProvider(create: (context) => getIt<DistractionCubit>()),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<LayoutCubit, LayoutState>(listener: (context, state) {}),
        ],
        child: BlocBuilder<AuthCubit, AuthState>(
          builder: (context, state) => ValueListenableBuilder(
            valueListenable: ConnectivityController.instance.isConnected,
            builder: (_, isConnect, __) {
              if (isConnect) {
                return BlocBuilder<AppCubit, AppState>(
                  builder: (BuildContext context, state) {
                    final cubit = context.read<AppCubit>();
                    return ScreenUtilInit(
                      designSize: const Size(375, 812),
                      child: MaterialApp.router(
                        supportedLocales: Translations.supportedLocales,
                        localizationsDelegates: const [
                          ...Translations.localizationsDelegates
                        ],
                        locale: Locale(cubit.currentLangCode),
                        themeMode: ThemeMode.light,
                        theme: themeLight(),
                        darkTheme: themeLight(),
                        routerConfig: _appRouter.config(),
                        // _goRouterConfig,
                        debugShowCheckedModeBanner:
                            EnvVariable.instance.isDebugMode,
                        builder: (cnx, widget) {
                          return GestureDetector(
                            onTap: () {
                              FocusManager.instance.primaryFocus?.unfocus();
                            },
                            child: Scaffold(
                              body: SafeArea(
                                child: Builder(
                                  builder: (context) {
                                    ConnectivityController.instance.init();
                                    return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 0),
                                        child: widget!);
                                  },
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                );
              } else {
                return MaterialApp(
                  title: 'Flutter Demo',
                  debugShowCheckedModeBanner: EnvVariable.instance.isDebugMode,
                  theme: ThemeData(
                    colorScheme:
                        ColorScheme.fromSeed(seedColor: Colors.deepPurple),
                    useMaterial3: false,
                  ),
                  home: const NoNetworkScreen(),
                );
              }
            },
          ),
        ),
      ),
    );
  }
}

// final GoRouter _goRouterConfig = GoRouter(
//   initialLocation: SplashView.path,
//   // initialLocation: ConfirmOtpView.path ,
//   observers: [ObserverUtils.routeObserver],
//   routes: _getRoutes(Routes.I.routes),
// );

// List<GoRoute> _getRoutes(List<RouteInfo>? routes) => (routes ?? [])
//     .map(
//       (subRoute) => GoRoute(
//         path: subRoute.path,
//         name: subRoute.name ?? subRoute.path,
//         builder: (context, state) => subRoute.builder(context, state),
//         routes: _getRoutes(subRoute.routes),
//       ),
//     )
//     .toList();
