// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:clean_arc/core/app/app_cubit/app_cubit.dart' as _i421;
import 'package:clean_arc/core/data/services/api_services/injectable_module.dart'
    as _i890;
import 'package:clean_arc/core/data/utill/configration.dart' as _i790;
import 'package:clean_arc/core/utils_package/utils_package.dart' as _i845;
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart'
    as _i788;
import 'package:clean_arc/features/auth_feature/domain/repository/auth_repository.dart'
    as _i453;
import 'package:clean_arc/features/auth_feature/domain/services/remote/login_remote_data_source.dart'
    as _i275;
import 'package:clean_arc/features/book_feature/controller/books_cubit.dart'
    as _i172;
import 'package:clean_arc/features/book_feature/domain/repository/books_repository.dart'
    as _i490;
import 'package:clean_arc/features/book_feature/domain/services/remote/books_remote_data_source.dart'
    as _i692;
import 'package:clean_arc/features/concerns_feature/controller/concerns_cubit.dart'
    as _i772;
import 'package:clean_arc/features/concerns_feature/domain/repository/concerns_repository.dart'
    as _i1004;
import 'package:clean_arc/features/concerns_feature/domain/services/remote/concerns_services.dart'
    as _i341;
import 'package:clean_arc/features/distraction_feature/controller/distraction_cubit.dart'
    as _i295;
import 'package:clean_arc/features/distraction_feature/domain/repository/distraction_repository.dart'
    as _i728;
import 'package:clean_arc/features/distraction_feature/domain/services/remote/distraction_services.dart'
    as _i903;
import 'package:clean_arc/features/layout_feature/cubit_cubit/layout_cubit.dart'
    as _i732;
import 'package:clean_arc/features/lecture_feature/controller/lecture_cubit.dart'
    as _i570;
import 'package:clean_arc/features/lecture_feature/domain/repository/lecture_repository.dart'
    as _i769;
import 'package:clean_arc/features/lecture_feature/domain/services/remote/lecture_services.dart'
    as _i1048;
import 'package:clean_arc/features/meditation_feature/controller/meditations_cubit.dart'
    as _i6;
import 'package:clean_arc/features/meditation_feature/domain/repository/meditations_repository.dart'
    as _i100;
import 'package:clean_arc/features/meditation_feature/domain/services/remote/meditations_services.dart'
    as _i551;
import 'package:clean_arc/features/player_feature/controller/now_playing_controller.dart'
    as _i1002;
import 'package:clean_arc/features/treatment_feature/controller/treatment_cubit.dart'
    as _i768;
import 'package:clean_arc/features/treatment_feature/domain/repository/treatment_repository.dart'
    as _i914;
import 'package:clean_arc/features/treatment_feature/domain/services/remote/treatment_services.dart'
    as _i295;
import 'package:clean_arc/features/user_layout/home_feature/controller/user_home_cubit.dart'
    as _i997;
import 'package:clean_arc/features/user_layout/home_feature/domain/repository/home_repository.dart'
    as _i358;
import 'package:clean_arc/features/user_layout/home_feature/domain/services/remote/home_remote_data_source.dart'
    as _i1070;
import 'package:clean_arc/my_global_cubit/thought_cubit.dart' as _i614;
import 'package:dio/dio.dart' as _i361;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:logger/logger.dart' as _i974;
import 'package:shared_preferences/shared_preferences.dart' as _i460;

const String _staging = 'staging';
const String _dev = 'dev';
const String _prod = 'prod';

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final injectableModule = _$InjectableModule();
    gh.factory<_i421.AppCubit>(() => _i421.AppCubit());
    gh.factory<_i1002.NowPlayingController>(
        () => _i1002.NowPlayingController());
    gh.factory<_i732.LayoutCubit>(() => _i732.LayoutCubit());
    gh.factory<_i614.ThoughtCubit>(() => _i614.ThoughtCubit());
    await gh.lazySingletonAsync<_i460.SharedPreferences>(
      () => injectableModule.sharedPref,
      preResolve: true,
    );
    gh.lazySingleton<_i361.Dio>(() => injectableModule.dioInstance);
    gh.lazySingleton<_i974.Logger>(() => injectableModule.logger);
    gh.lazySingleton<_i790.Configuration>(
      () => _i790.StagingConfiguration(),
      registerFor: {_staging},
    );
    gh.lazySingleton<_i790.Configuration>(
      () => _i790.DevConfiguration(),
      registerFor: {_dev},
    );
    gh.lazySingleton<_i295.TreatmentServices>(() => _i295.TreatmentServices(
          gh<_i845.Dio>(),
          gh<_i790.Configuration>(),
        ));
    gh.lazySingleton<_i903.DistractionServices>(() => _i903.DistractionServices(
          gh<_i845.Dio>(),
          gh<_i790.Configuration>(),
        ));
    gh.lazySingleton<_i1048.LectureServices>(() => _i1048.LectureServices(
          gh<_i845.Dio>(),
          gh<_i790.Configuration>(),
        ));
    gh.lazySingleton<_i551.MeditationsServices>(() => _i551.MeditationsServices(
          gh<_i845.Dio>(),
          gh<_i790.Configuration>(),
        ));
    gh.lazySingleton<_i692.BooksServices>(() => _i692.BooksServices(
          gh<_i845.Dio>(),
          gh<_i790.Configuration>(),
        ));
    gh.lazySingleton<_i275.AuthServices>(() => _i275.AuthServices(
          gh<_i845.Dio>(),
          gh<_i790.Configuration>(),
        ));
    gh.lazySingleton<_i341.ConcernsServices>(() => _i341.ConcernsServices(
          gh<_i845.Dio>(),
          gh<_i790.Configuration>(),
        ));
    gh.lazySingleton<_i1070.HomeServices>(() => _i1070.HomeServices(
          gh<_i845.Dio>(),
          gh<_i790.Configuration>(),
        ));
    gh.lazySingleton<_i358.HomeRepository>(() => _i358.HomeRepositoryImpl(
          gh<_i974.Logger>(),
          gh<_i1070.HomeServices>(),
        ));
    gh.lazySingleton<_i790.Configuration>(
      () => _i790.ProdConfiguration(),
      registerFor: {_prod},
    );
    gh.lazySingleton<_i728.DistractionRepository>(
        () => _i728.DistractionRepositoryImpl(
              gh<_i974.Logger>(),
              gh<_i903.DistractionServices>(),
            ));
    gh.factory<_i997.UserHomeCubit>(
        () => _i997.UserHomeCubit(gh<_i358.HomeRepository>()));
    gh.lazySingleton<_i453.AuthRepository>(() => _i453.AuthRepositoryImpl(
          gh<_i974.Logger>(),
          gh<_i275.AuthServices>(),
        ));
    gh.lazySingleton<_i769.LectureRepository>(() => _i769.LectureRepositoryImpl(
          gh<_i974.Logger>(),
          gh<_i1048.LectureServices>(),
        ));
    gh.lazySingleton<_i100.MeditationsRepository>(
        () => _i100.MeditationsRepositoryImpl(
              gh<_i974.Logger>(),
              gh<_i551.MeditationsServices>(),
            ));
    gh.factory<_i788.AuthCubit>(
        () => _i788.AuthCubit(gh<_i453.AuthRepository>()));
    gh.lazySingleton<_i1004.ConcernsRepository>(
        () => _i1004.ConcernsRepositoryImpl(
              gh<_i974.Logger>(),
              gh<_i341.ConcernsServices>(),
            ));
    gh.factory<_i570.LectureCubit>(
        () => _i570.LectureCubit(gh<_i769.LectureRepository>()));
    gh.lazySingleton<_i914.TreatmentRepository>(
        () => _i914.TreatmentRepositoryImpl(
              gh<_i974.Logger>(),
              gh<_i295.TreatmentServices>(),
            ));
    gh.lazySingleton<_i490.BooksRepository>(() => _i490.BooksRepositoryImpl(
          gh<_i974.Logger>(),
          gh<_i692.BooksServices>(),
        ));
    gh.factory<_i295.DistractionCubit>(
        () => _i295.DistractionCubit(gh<_i728.DistractionRepository>()));
    gh.factory<_i768.TreatmentCubit>(
        () => _i768.TreatmentCubit(gh<_i914.TreatmentRepository>()));
    gh.factory<_i6.MeditationsCubit>(
        () => _i6.MeditationsCubit(gh<_i100.MeditationsRepository>()));
    gh.factory<_i772.ConcernsCubit>(
        () => _i772.ConcernsCubit(gh<_i1004.ConcernsRepository>()));
    gh.factory<_i172.BooksCubit>(
        () => _i172.BooksCubit(gh<_i490.BooksRepository>()));
    return this;
  }
}

class _$InjectableModule extends _i890.InjectableModule {}
