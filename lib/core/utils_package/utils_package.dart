library utils_package;

export 'package:clean_arc/core/language/translation/translations.dart';
export 'package:clean_arc/core/presentation/extintions/context_extintions.dart';
export 'package:clean_arc/core/presentation/extintions/string_extintion.dart';
export 'package:clean_arc/core/presentation/util/app_dimensions.dart';
export 'package:clean_arc/core/presentation/util/style/colors/color_dark.dart';
export 'package:clean_arc/core/presentation/util/style/colors/color_ligh.dart';
export 'package:clean_arc/core/presentation/util/style/fonts/font_family_helper.dart';
export 'package:clean_arc/core/presentation/util/style/fonts/font_weight_helper.dart';
export 'package:clean_arc/core/presentation/util/style/images/assets.gen.dart';
export 'package:clean_arc/core/presentation/util/style/theme/app_theme.dart';
export 'package:clean_arc/core/presentation/util/style/theme/asset_extintion.dart';
export 'package:clean_arc/core/presentation/util/style/theme/color_extintion.dart';
export 'package:clean_arc/core/presentation/widget/custom_appbar.dart';
export 'package:clean_arc/core/presentation/widget/custom_button_button.dart';
//---------------------------------------------------------------
//widgets,util

export 'package:clean_arc/core/presentation/widget/custom_text_field.dart';
export 'package:clean_arc/core/presentation/widget/text_app.dart';
//Packages
export 'package:dio/dio.dart';
export 'package:flutter_screenutil/flutter_screenutil.dart';
export 'package:flutter_svg/flutter_svg.dart';
// export 'package:go_router/go_router.dart';
export 'package:auto_route/auto_route.dart';


export 'package:shared_preferences/shared_preferences.dart';
