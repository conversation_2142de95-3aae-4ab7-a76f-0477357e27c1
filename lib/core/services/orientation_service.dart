import 'package:flutter/services.dart';

class OrientationService {
  static OrientationService? _instance;
  static OrientationService get instance => _instance ??= OrientationService._();
  
  OrientationService._();

  /// Lock orientation to portrait mode (default app behavior)
  static Future<void> lockPortrait() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// Allow all orientations (for fullscreen video)
  static Future<void> allowAllOrientations() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Force landscape orientation (for fullscreen video)
  static Future<void> forceLandscape() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Hide system UI for fullscreen experience
  static Future<void> hideSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  /// Show system UI (normal mode)
  static Future<void> showSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  /// Enter fullscreen mode (hide UI + allow rotation)
  static Future<void> enterFullscreen() async {
    await hideSystemUI();
    await allowAllOrientations();
  }

  /// Exit fullscreen mode (show UI + lock portrait)
  static Future<void> exitFullscreen() async {
    await showSystemUI();
    await lockPortrait();
  }
}
