import 'dart:developer';

import 'package:get_storage/get_storage.dart';

class GetStorageService {
  static final _box = GetStorage();

  static Future<void> init() async {
    await GetStorage.init();
  }

  static Future<bool> setLocalData(
      {required String key, required dynamic value}) async {
    try {
      await _box.write(key, value);

      log("Local data saved successfully");
      return true;
    } on Exception catch (e) {
      log("Error while saving local data $e");
      return false;
    }
  }

  static dynamic getLocalData({required dynamic key}) {
    return _box.read(key);
  }

  Future<bool> removeLocalData({required String key}) async {
    try {
      await _box.remove(key);

      log("Local data removed successfully");
      return true;
    } on Exception catch (e) {
      log("Error while removing local data $e");
      return false;
    }
  }

  static bool hasData({required String key}) {
    return _box.hasData(key);
  }

  static Future<bool> clearLocalData() async {
    try {
      await _box.erase();

      log("Local data cleared successfully");
      return true;
    } on Exception catch (e) {
      log("Error while clearing local data $e");
      return false;
    }
  }

  //? Remove Key
  static Future<bool> removeKey({required String key}) async {
    try {
      await _box.remove(key);

      log("Local data removed successfully");
      return true;
    } on Exception catch (e) {
      log("Error while removing local data $e");
      return false;
    }
  }

  // Question summaries management
  static const String _questionSummariesKey = 'question_summaries';

  static List<Map<String, String>> getQuestionSummaries() {
    try {
      List<dynamic>? data = _box.read(_questionSummariesKey);
      if (data != null) {
        return data.map((item) => Map<String, String>.from(item)).toList();
      }
      return [];
    } catch (e) {
      log("Error while getting question summaries: $e");
      return [];
    }
  }

  static Future<bool> saveQuestionSummaries(
      List<Map<String, String>> summaries) async {
    try {
      await _box.write(_questionSummariesKey, summaries);
      log("Question summaries saved successfully");
      return true;
    } catch (e) {
      log("Error while saving question summaries: $e");
      return false;
    }
  }

  static Future<bool> clearQuestionSummaries() async {
    try {
      await _box.remove(_questionSummariesKey);
      log("Question summaries cleared successfully");
      return true;
    } catch (e) {
      log("Error while clearing question summaries: $e");
      return false;
    }
  }
}
