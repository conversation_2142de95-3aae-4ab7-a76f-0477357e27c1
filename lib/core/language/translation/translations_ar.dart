// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'translations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class TranslationsAr extends Translations {
  TranslationsAr([String locale = 'ar']) : super(locale);

  @override
  String get splash => 'splash';

  @override
  String get jj => 'ennnnnnnnn';

  @override
  String get seeAll => 'See All';

  @override
  String get bestSeller => 'best Seller';

  @override
  String get categories => 'categories';

  @override
  String get start => 'Sٍtart';

  @override
  String get phoneOrEmail => 'Phone number or email';

  @override
  String get enterPhone => 'Enter Phone number';

  @override
  String get enterAValidPhoneNumber => 'Enter a valid phone number';

  @override
  String get enterAValidEmail => 'Enter a valid email';

  @override
  String get enterAValidPhoneOrEmail => 'Enter a valid phone number or email';

  @override
  String get password => 'كلمة المرور';

  @override
  String get rememberMe => 'تذكرني';

  @override
  String get forgetPassword => 'Forget Password';

  @override
  String get forgetPassword2 => 'forget Password2';

  @override
  String get dontWorry =>
      'Don\'t worry, we will send you account recovery instructions';

  @override
  String get resetPassword => 'reset Password';

  @override
  String get returnToSignIn => 'return To SignIn';

  @override
  String get resetYourPassword => 'Reset Your Password';

  @override
  String get confirmVerificationCode => 'Confirm Verification Code';

  @override
  String get codeSent =>
      'We sent you a activation code to your phone consisting of 6 digits';

  @override
  String get resendCode => 'Resend the code';

  @override
  String get newPasswordMustDiffer =>
      'Your new password must be different from the previous one';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get passwordCondition1 =>
      'Must be 8 characters long and include some numbers';

  @override
  String get passwordCondition2 =>
      'Must contain at least one special character';

  @override
  String get noInternetConnection => 'connectionTimeOut';

  @override
  String get tryAgain => 'Try again';

  @override
  String get errorMessage => 'error occured';

  @override
  String get connectionTimeOut => 'connectionTimeOut';

  @override
  String get success => 'success';

  @override
  String get personalAccount => 'My Account';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get sign_in => 'Sign In';

  @override
  String get forget_password => 'Forget Password?';

  @override
  String get write_your_name => 'Write your name';

  @override
  String get write_your_password => 'Write your password';

  @override
  String get order_list => 'Order List';

  @override
  String get live_orders => 'Live Orders';

  @override
  String get history_orders => 'History Orders';

  @override
  String get date => 'Date';

  @override
  String get status => 'Status';

  @override
  String get qr_code => 'QR Code';

  @override
  String get bulk_scan => 'Bulk Scan';

  @override
  String get sku => 'SKU';

  @override
  String get product_stock => 'Product Stock';

  @override
  String get search_product => 'Search Product';

  @override
  String get search => 'Search';

  @override
  String get warehouse => 'Warehouse';

  @override
  String get customer => 'Customer';

  @override
  String get name => 'Name';

  @override
  String get quantity => 'Quantity';

  @override
  String get time => 'Time';

  @override
  String get import_export => 'Import_Export';

  @override
  String get your_name => 'Your Name';

  @override
  String get your_password => 'Your Password';

  @override
  String get sign_out => 'Sign Out';

  @override
  String get hello => 'hello';

  @override
  String get profile => 'Profile';

  @override
  String get ready_to_pickup => 'Ready to pickup';

  @override
  String get ready_to_return => 'Ready to return';

  @override
  String get product_list => 'Product List';

  @override
  String get ready_list => 'Ready List';

  @override
  String get search_sku_name => 'Search SKU/Name';

  @override
  String get scanner => 'Scanner';

  @override
  String get summary => 'Summary';

  @override
  String get order_number => 'Order Number: ';

  @override
  String get customer_details => 'Customer details';

  @override
  String get barcode => 'Barcode';

  @override
  String get picked_item => 'Picked Item';

  @override
  String get picked => 'Picked';

  @override
  String get picked_up => 'Picked Up';

  @override
  String get code => 'Code';

  @override
  String get item_count => 'Item Count';

  @override
  String get product_count => 'Product Count';

  @override
  String get product => 'Product';

  @override
  String get item => 'Item';

  @override
  String get email_or_phone_number => 'Email or phone number';

  @override
  String get email_error_message => 'You must fill username input';

  @override
  String get password_error_message => 'You must fill password input';

  @override
  String get send => 'Send';

  @override
  String get forget_password_error_message =>
      'You must enter phone number or valid email input';

  @override
  String get check_sms => 'Check The SMS';

  @override
  String get phone_number_error_message => 'You must enter phone number input';

  @override
  String get verification_code => 'Verification Code';

  @override
  String get write_phone_number => 'Write your phone number';

  @override
  String get write_new_password => 'Write the new password';

  @override
  String get confirm_new_password => 'Confirm the new password';

  @override
  String get confirm_new_password_error_message =>
      'You must fill confirm new password input';

  @override
  String get point_your_device => 'Point your device at the product to scanned';

  @override
  String get you_can_easily_scan =>
      'You can easily scan your item to check availiability';

  @override
  String get order_must_be_scanned =>
      'All products for this order must be scanned';

  @override
  String get confirm => 'Confirm';

  @override
  String get scan => 'Scan';

  @override
  String get home => 'Home';

  @override
  String get check => 'Check';

  @override
  String get address => 'Address';

  @override
  String get warning => 'Warning';

  @override
  String get over_quantity =>
      'You erased an extra piece of the required quantity of the product';

  @override
  String get returned_done => 'Returned done.';

  @override
  String get return_it => 'Return it';

  @override
  String get scan_more => 'Scan More';

  @override
  String get saveChanges => 'حفظ';

  @override
  String get language => 'Language';

  @override
  String get curLang => 'English';

  @override
  String get profileDetails => 'Profile details';

  @override
  String get userName => 'User name';

  @override
  String get email => 'بريد إلكتروني';

  @override
  String get requiredField => 'Required field';

  @override
  String get enterAValidPhoneNumberOrMail =>
      'Enter a valid phone number or email';

  @override
  String get pleaseEnterCode => 'Please enter code';

  @override
  String get confirmPasswordError => 'Confirm password does not match password';

  @override
  String get passwordError => 'Password must be more than 8 characters';

  @override
  String get shelf => 'Shelf';

  @override
  String get hiWelcomeBaCK => 'Hi! Welcome back';

  @override
  String get signInToYourAccount => 'Sign In to your account';

  @override
  String get normalUser => 'Normal User';

  @override
  String get company => 'Company';

  @override
  String get trucOwner => 'Truck Owner';

  @override
  String get next => 'التالي';

  @override
  String get signIn => 'Sign In';

  @override
  String get dontHaveAnAccount => 'ليس لديك حساب؟';

  @override
  String get signUp => 'Sign Up';

  @override
  String get byUsingOurServicesYouAreAgreeingToOur =>
      'By using our services you are agreeing to our';

  @override
  String get terms => 'Terms';

  @override
  String get and => 'and';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get pleaseFillOutTheFormBelow => 'Please fill out the form below!';

  @override
  String get userInfo => 'User Info';

  @override
  String get welcomeBack => 'Welcome back.';

  @override
  String get bestDeliveryTrucks => 'Best delivery trucks';

  @override
  String get follow_your_current_route => 'Follow your current route!';

  @override
  String get createANewTripNow => 'Create a new trip now!';

  @override
  String get more => 'More';

  @override
  String get select => 'اختار';

  @override
  String get pleaseSelectNearbyPlacesToYou =>
      'Please Select nearby places to you';

  @override
  String get searchForPlace => 'Search for a place';

  @override
  String get enterDeliveryAddress => 'Enter delivery address';

  @override
  String get thisFieldIsRequired => 'This field is required';

  @override
  String get truckType => 'Truck Type';

  @override
  String get save => 'save';

  @override
  String get cancel => 'cancel';

  @override
  String get dataAndTime => 'Data And Time';

  @override
  String get weight => 'Weight ( Kg )';

  @override
  String get typeOfShipment => 'Type of Shipment';

  @override
  String get clickToUpload => 'Click to upload';

  @override
  String get orDragAndDrop => 'or drag and drop';

  @override
  String get fileTypes => '.xls  PDF. JPG, GIF. PNG (max. 3 mega)';

  @override
  String get lblGallery => 'Gallery';

  @override
  String get camera => 'Camera';

  @override
  String get document => 'Document';

  @override
  String get addNote => 'Add Note';

  @override
  String get myOrders => 'My Orders';

  @override
  String get active => 'Active';

  @override
  String get history => 'History';

  @override
  String get offers => 'Offers';

  @override
  String get noData => 'No Data';

  @override
  String get orderDetails => 'Order Details';

  @override
  String get map => 'Map';

  @override
  String get view => 'View';

  @override
  String get kg => 'Kg';

  @override
  String get shipmentPhoto => 'Shipment photo';

  @override
  String get trucksOffers => 'Trucks Offers';

  @override
  String get priceToDeliver => 'Price to deliver';

  @override
  String get startShipping => 'Start Shipping';

  @override
  String get checkout => 'Checkout';

  @override
  String get promo => 'Promo';

  @override
  String get addCode => 'add code';

  @override
  String get orderSummary => 'Order Summary';

  @override
  String get price => 'Price';

  @override
  String get appFees => 'App Fees';

  @override
  String get total => 'Total';

  @override
  String get dropOff => 'Drop off';

  @override
  String get checkoutPayment => 'Checkout Payment';

  @override
  String get updateProfile => 'Update Profile';

  @override
  String get changeLanguage => 'change Language';

  @override
  String get myCreditDebitCards => 'My credit / debit cards';

  @override
  String get paymentHistory => 'Payment History';

  @override
  String get supportPage => 'Support page';

  @override
  String get shareApplication => 'Share application';

  @override
  String get logOutCurrentAccount => 'Logout Current Account';

  @override
  String get changeProfile => 'Change Profile';

  @override
  String get addCard => 'Add Card';

  @override
  String get pleasEnterValidCardNumber => 'Please input a valid card number';

  @override
  String get pleasEnterValidExpiryDate => 'Please input a valid expiry date';

  @override
  String get pleasEnterValidCvv => 'Please input a valid CVV';

  @override
  String get cardHolder => 'card Holder';

  @override
  String get cardNumber => 'Card Number';

  @override
  String get expirationDate => 'Expiration Date';

  @override
  String get cvv => 'CVV';

  @override
  String get continues => 'continue';

  @override
  String get expiryDate => 'Expiration Date : ';

  @override
  String get visaCard => 'Visa Card';

  @override
  String get myCards => 'My Cards';

  @override
  String get support => 'Support';

  @override
  String get selectWithContactDetailsShouldWeUseToContactSupport =>
      'Select with contact details should we use to contact Support';

  @override
  String get whatsapp => 'Whatsapp';

  @override
  String get new2 => 'New';

  @override
  String get today => 'Today';

  @override
  String get markAllAsRead => 'Mark all as read';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get myWallet => 'My Wallet';

  @override
  String get yourBalance => 'Your Balance';

  @override
  String get deposit => 'Deposit';

  @override
  String get withdraw => 'Withdraw';

  @override
  String get transaction => 'Transaction';

  @override
  String get saveCard => 'Save Card';

  @override
  String get enterValidOtp => 'enter valid Otp';

  @override
  String get getOffers => 'Get Offers';

  @override
  String get close => 'Close';

  @override
  String get post => 'Post';

  @override
  String get pleaseCompleteOrderData => 'Please complete order data';

  @override
  String get currentLocation => 'Current Location';

  @override
  String get chat => 'Chat';

  @override
  String get cancelReport => 'Cancel,Report';

  @override
  String get done => 'done';

  @override
  String get truckTypeIsRequired => 'Truck type is required';

  @override
  String get pickedIsRequired => 'Picked Date is required';

  @override
  String get weightIsRequired => 'weight is required';

  @override
  String get shipmentTypeIsRequired => 'shipment Type is required';

  @override
  String get shipmentImageIsRequired => 'shipment image is required';

  @override
  String get onBoard1 => 'علاج';

  @override
  String get onBoard1color => 'نهائي';

  @override
  String get onBoard1AfterColor => 'لنوبات الهلع';

  @override
  String get onBoard2 => 'راحة نفسية';

  @override
  String get onBoard2color => 'دائمة';

  @override
  String get onBoard2AfterColor => '';

  @override
  String get onBoard3 => 'المعالج النفسي';

  @override
  String get onBoard3color => 'سليم كيال';

  @override
  String get onBoard3AfterColor => '';

  @override
  String get onBoard4 => 'مرحبًا بك في ';

  @override
  String get onBoard4color1 => 'Arab';

  @override
  String get onBoard4color2 => 'CBT';

  @override
  String get onBoard4AfterColor => 'الطفرة الجديدة للعلاج النفسي';

  @override
  String get onboardingDescription1 =>
      'ستتخلص من الخوف ومن جميع الافكار السلبية المخيفة والتفكير المفرط. وستسيطر على جسدك وردود افعاله.';

  @override
  String get onboardingDescription2 =>
      'هذا التطبيق سيجعلك تتمتّع بالهدوء النفسي والسلام الداخلي والتفكير الايجابي في كل مكان, على مدار اليوم وفي جميع أيام السنة.';

  @override
  String get onboardingDescription3 =>
      ' سيرافقك أفضل المعالجين في كل دقيقة، وسيساعدك في التفكير بشكل ايجابي، ويخلصك من جميع افكارك السلبية.';

  @override
  String get yourPerfectMentalHealthCompanion => 'رفيقك المناسب للصحة النفسية،';

  @override
  String get anytimeAnywhere => ' في كل زمان وفي أي مكان 🍃';

  @override
  String get doYouAlreadyHaveAnAccount => 'هل لديك حساب بالفعل؟';

  @override
  String get logIn => 'تسجيل الدخول.';

  @override
  String get logIn2 => 'تسجيل الدخول';

  @override
  String get startNow => 'ابدا الان';

  @override
  String get logInToYourAccount => 'تسجيل الدخول إلى حسابك';

  @override
  String get enterYourEmailAndPasswordToLogIn =>
      'أدخل بريدك الإلكتروني وكلمة المرور لتسجيل الدخول';

  @override
  String get continueWithGoogle => 'متابعة مع جوجل';

  @override
  String get orLogInUsing => 'أو قم بتسجيل الدخول باستخدام';

  @override
  String get forgotPassword => 'هل نسيت كلمة السر؟';

  @override
  String get registerNew => 'تسجيل جديد';

  @override
  String get enterYourNameEmailAndPasswordToSubscribe =>
      'أدخل اسمك وبريدك الإلكتروني وكلمة المرور للاشتراك';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get surname => 'الكنية';

  @override
  String get enterSurname => 'أدخل الكنية';

  @override
  String get enterFullName => 'أدخل الاسم الكامل';

  @override
  String get passwordTooShort => 'كلمة المرور قصيرة';

  @override
  String get orCreateANewAccount => 'أو قم بإنشاء حساب جديد';

  @override
  String get registerWithGoogle => 'تسجيل عن طريق جوجل';

  @override
  String get whatIsYourGender => 'ما هو جنسك؟';

  @override
  String get male => 'ذكر';

  @override
  String get female => 'أنثى';

  @override
  String get whatIsYourAge => 'ما هو عمرك؟';

  @override
  String get chooseTheTopicsYouWantUsToHelpYouWith =>
      'اختر أهم المواضيع التي تريد أن نساعدك فيها';

  @override
  String get chooseTheNumberYouWant => 'اختر العدد الذي تريده';

  @override
  String get dayFreeTrial => '14 يوم تجربة مجانية';

  @override
  String get oneOnOneLiveSessions => 'جلسات فردية مباشرة';

  @override
  String get continuousFollowUp => 'متابعة مستمرة';

  @override
  String get hourSupport => 'دعم على مدار الساعة';

  @override
  String get quickConsultations => 'استشارات سريعة';

  @override
  String get customContent => 'محتوى مخصص';

  @override
  String get lifetime => 'مدى الحياة';

  @override
  String get try14DaysForFreeThenPay12999OnceAndUseForeverYouCanCancelAnyTime =>
      'جرب 14 يومًا مجانًا، ثم ادفع مرة واحدة 129.99 واستخدمها إلى الأبد. يمكنك إلغاء الاشتراك في أي وقت';

  @override
  String get start14DayFreeTrial => 'ابدأ تجربة مجانية لمدة 14 يومًا';

  @override
  String get subscribeLater => 'اشترك لاحقًا';

  @override
  String get becauseYouDeserveHappinessAndPeaceOfMind =>
      'لأنك تستحق السعادة والهدوء النفسي';

  @override
  String get skip => 'تخطي';

  @override
  String get women => 'انثى';

  @override
  String get continue1 => 'التالي';

  @override
  String get chooseTheMostImportant =>
      'اختر اهم المواضيع التي تريد ان نساعدك فيها';

  @override
  String get finished => 'انتهيت';

  @override
  String get openingSalimCbt => 'فتح حساب Salim CBT المميز';

  @override
  String get bestDeal => 'افضل صفقة';

  @override
  String get howYouFeelToday => 'كيف تشعر اليوم؟';

  @override
  String get getRidOfYourFearNow => 'تخلّص من خوفك الآن';

  @override
  String get areYourFearsBlockingYourGoals =>
      'هل تعيقك مخاوفك عن تحقيق أهدافك؟انضم إلينا الآن وتعلم كيفية التغلب عليها.ابدأ رحلتك وكن قائد مصيرك.لا تدع الخوف يتحكم بك بعد اليوم!';

  @override
  String get receiveRecordedTherapySessions =>
      'تلقى جلسات علاج نفسي مسجلة مع المعالج النفسي سليم كيال';

  @override
  String get subscribeNow => 'اشترك الآن';

  @override
  String get prioritizeYourMentalHealthToday =>
      'أعطِ صحتك العقلية الأولوية اليوم!';

  @override
  String get bookGoodbyeToNegativeThoughts => 'كتاب وداعًا للأفكار السلبية';

  @override
  String get yourCompleteGuideToGettingRidOfNegativeThoughts =>
      'دليلك الكامل للتخلص من أفكارك السلبية';

  @override
  String get chooseConcernsToEliminate =>
      'قم باختيار المخاوف التي ترغب في التخلص منها';

  @override
  String get whatThoughtsDoYouExperienceDuringPanicAttack =>
      'ما هي الأفكار التي تواجهها من نوبة الهلع؟';

  @override
  String get howDoYouWantToEliminateYourFears => 'كيف تريد أن تتخلص من مخاوفك؟';

  @override
  String get beforeProceedingToWaysToEliminateNegativeThoughts =>
      'قبل الانتقال لطرق التخلص من الأفكار السلبية، شاهد فيديو مهم وضروري في شرح القلق ونوبات الهلع';

  @override
  String get fearOfPanicAttack => 'الخوف من نوبة الهلع';

  @override
  String get youCanChoseTwice => 'يمكنك اختيار الطريقتين';

  @override
  String get byVoiceRecorder => 'سماع تسجيل صوتي للمعالج النفسي';

  @override
  String get destroyBadThinking => 'استجواب التخلص من الافكار السلبية والخوف';

  @override
  String get recordings => 'التسجيلات';

  @override
  String get form => 'الاستمارة';

  @override
  String get formSummary => 'ملخص الاستمارة';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get nextQuestion => 'السؤال التالي';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get personalInformation => 'المعلومات الشخصية';

  @override
  String get username => 'اسم المستخدم:';

  @override
  String get email2 => 'البريد الإلكتروني:';

  @override
  String get password2 => 'كلمة المرور:';

  @override
  String get preferences => 'التفضيلات';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get paymentNotification => 'إشعار الدفع';

  @override
  String get deleteAccount => 'حذف الحساب';

  @override
  String get proceedToPayment => 'انتقل إلى الدفع';

  @override
  String get applyCoupons => 'تطبيق القسائم';

  @override
  String get orderPaymentDetails => 'تفاصيل دفع الطلب';

  @override
  String get orderPrice => 'سعر الطلب';

  @override
  String get deliveryFee => 'رسوم التوصيل';

  @override
  String get free => 'مجاني';

  @override
  String get payments => 'مدفوعاتك';

  @override
  String get notifications => 'الاشعارات';

  @override
  String get enterEmail => 'ادخل البريد الالكتروني';

  @override
  String get enterPassword => 'ادخل كلمة المرور';

  @override
  String get enterPhoneNumber => 'ادخل رقم الهاتف';

  @override
  String get phoneNumber => 'رقم الهاتف:';

  @override
  String get updateApp => 'تحديث التطبيق';

  @override
  String get thereIsAnewVersion => 'هناك نسخة جديدة متاحة';

  @override
  String get previous => 'السابق';

  @override
  String get addToBookmark => 'إضافة إلى المفضلة';

  @override
  String get summaryTitle => 'ملخص الأسئلة';

  @override
  String get noSummariesAvailable => 'لا توجد ملخصات متاحة';
}
