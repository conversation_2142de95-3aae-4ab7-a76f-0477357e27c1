import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'translations_ar.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of Translations
/// returned by `Translations.of(context)`.
///
/// Applications need to include `Translations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'translation/translations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: Translations.localizationsDelegates,
///   supportedLocales: Translations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the Translations.supportedLocales
/// property.
abstract class Translations {
  Translations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static Translations? of(BuildContext context) {
    return Localizations.of<Translations>(context, Translations);
  }

  static const LocalizationsDelegate<Translations> delegate =
      _TranslationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('ar')];

  /// No description provided for @splash.
  ///
  /// In ar, this message translates to:
  /// **'splash'**
  String get splash;

  /// No description provided for @jj.
  ///
  /// In ar, this message translates to:
  /// **'ennnnnnnnn'**
  String get jj;

  /// No description provided for @seeAll.
  ///
  /// In ar, this message translates to:
  /// **'See All'**
  String get seeAll;

  /// No description provided for @bestSeller.
  ///
  /// In ar, this message translates to:
  /// **'best Seller'**
  String get bestSeller;

  /// No description provided for @categories.
  ///
  /// In ar, this message translates to:
  /// **'categories'**
  String get categories;

  /// No description provided for @start.
  ///
  /// In ar, this message translates to:
  /// **'Sٍtart'**
  String get start;

  /// No description provided for @phoneOrEmail.
  ///
  /// In ar, this message translates to:
  /// **'Phone number or email'**
  String get phoneOrEmail;

  /// No description provided for @enterPhone.
  ///
  /// In ar, this message translates to:
  /// **'Enter Phone number'**
  String get enterPhone;

  /// No description provided for @enterAValidPhoneNumber.
  ///
  /// In ar, this message translates to:
  /// **'Enter a valid phone number'**
  String get enterAValidPhoneNumber;

  /// No description provided for @enterAValidEmail.
  ///
  /// In ar, this message translates to:
  /// **'Enter a valid email'**
  String get enterAValidEmail;

  /// No description provided for @enterAValidPhoneOrEmail.
  ///
  /// In ar, this message translates to:
  /// **'Enter a valid phone number or email'**
  String get enterAValidPhoneOrEmail;

  /// No description provided for @password.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور'**
  String get password;

  /// No description provided for @rememberMe.
  ///
  /// In ar, this message translates to:
  /// **'تذكرني'**
  String get rememberMe;

  /// No description provided for @forgetPassword.
  ///
  /// In ar, this message translates to:
  /// **'Forget Password'**
  String get forgetPassword;

  /// No description provided for @forgetPassword2.
  ///
  /// In ar, this message translates to:
  /// **'forget Password2'**
  String get forgetPassword2;

  /// No description provided for @dontWorry.
  ///
  /// In ar, this message translates to:
  /// **'Don\'t worry, we will send you account recovery instructions'**
  String get dontWorry;

  /// No description provided for @resetPassword.
  ///
  /// In ar, this message translates to:
  /// **'reset Password'**
  String get resetPassword;

  /// No description provided for @returnToSignIn.
  ///
  /// In ar, this message translates to:
  /// **'return To SignIn'**
  String get returnToSignIn;

  /// No description provided for @resetYourPassword.
  ///
  /// In ar, this message translates to:
  /// **'Reset Your Password'**
  String get resetYourPassword;

  /// No description provided for @confirmVerificationCode.
  ///
  /// In ar, this message translates to:
  /// **'Confirm Verification Code'**
  String get confirmVerificationCode;

  /// No description provided for @codeSent.
  ///
  /// In ar, this message translates to:
  /// **'We sent you a activation code to your phone consisting of 6 digits'**
  String get codeSent;

  /// No description provided for @resendCode.
  ///
  /// In ar, this message translates to:
  /// **'Resend the code'**
  String get resendCode;

  /// No description provided for @newPasswordMustDiffer.
  ///
  /// In ar, this message translates to:
  /// **'Your new password must be different from the previous one'**
  String get newPasswordMustDiffer;

  /// No description provided for @confirmPassword.
  ///
  /// In ar, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @passwordCondition1.
  ///
  /// In ar, this message translates to:
  /// **'Must be 8 characters long and include some numbers'**
  String get passwordCondition1;

  /// No description provided for @passwordCondition2.
  ///
  /// In ar, this message translates to:
  /// **'Must contain at least one special character'**
  String get passwordCondition2;

  /// No description provided for @noInternetConnection.
  ///
  /// In ar, this message translates to:
  /// **'connectionTimeOut'**
  String get noInternetConnection;

  /// No description provided for @tryAgain.
  ///
  /// In ar, this message translates to:
  /// **'Try again'**
  String get tryAgain;

  /// No description provided for @errorMessage.
  ///
  /// In ar, this message translates to:
  /// **'error occured'**
  String get errorMessage;

  /// No description provided for @connectionTimeOut.
  ///
  /// In ar, this message translates to:
  /// **'connectionTimeOut'**
  String get connectionTimeOut;

  /// No description provided for @success.
  ///
  /// In ar, this message translates to:
  /// **'success'**
  String get success;

  /// No description provided for @personalAccount.
  ///
  /// In ar, this message translates to:
  /// **'My Account'**
  String get personalAccount;

  /// No description provided for @logout.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الخروج'**
  String get logout;

  /// No description provided for @sign_in.
  ///
  /// In ar, this message translates to:
  /// **'Sign In'**
  String get sign_in;

  /// No description provided for @forget_password.
  ///
  /// In ar, this message translates to:
  /// **'Forget Password?'**
  String get forget_password;

  /// No description provided for @write_your_name.
  ///
  /// In ar, this message translates to:
  /// **'Write your name'**
  String get write_your_name;

  /// No description provided for @write_your_password.
  ///
  /// In ar, this message translates to:
  /// **'Write your password'**
  String get write_your_password;

  /// No description provided for @order_list.
  ///
  /// In ar, this message translates to:
  /// **'Order List'**
  String get order_list;

  /// No description provided for @live_orders.
  ///
  /// In ar, this message translates to:
  /// **'Live Orders'**
  String get live_orders;

  /// No description provided for @history_orders.
  ///
  /// In ar, this message translates to:
  /// **'History Orders'**
  String get history_orders;

  /// No description provided for @date.
  ///
  /// In ar, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @status.
  ///
  /// In ar, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @qr_code.
  ///
  /// In ar, this message translates to:
  /// **'QR Code'**
  String get qr_code;

  /// No description provided for @bulk_scan.
  ///
  /// In ar, this message translates to:
  /// **'Bulk Scan'**
  String get bulk_scan;

  /// No description provided for @sku.
  ///
  /// In ar, this message translates to:
  /// **'SKU'**
  String get sku;

  /// No description provided for @product_stock.
  ///
  /// In ar, this message translates to:
  /// **'Product Stock'**
  String get product_stock;

  /// No description provided for @search_product.
  ///
  /// In ar, this message translates to:
  /// **'Search Product'**
  String get search_product;

  /// No description provided for @search.
  ///
  /// In ar, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @warehouse.
  ///
  /// In ar, this message translates to:
  /// **'Warehouse'**
  String get warehouse;

  /// No description provided for @customer.
  ///
  /// In ar, this message translates to:
  /// **'Customer'**
  String get customer;

  /// No description provided for @name.
  ///
  /// In ar, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @quantity.
  ///
  /// In ar, this message translates to:
  /// **'Quantity'**
  String get quantity;

  /// No description provided for @time.
  ///
  /// In ar, this message translates to:
  /// **'Time'**
  String get time;

  /// No description provided for @import_export.
  ///
  /// In ar, this message translates to:
  /// **'Import_Export'**
  String get import_export;

  /// No description provided for @your_name.
  ///
  /// In ar, this message translates to:
  /// **'Your Name'**
  String get your_name;

  /// No description provided for @your_password.
  ///
  /// In ar, this message translates to:
  /// **'Your Password'**
  String get your_password;

  /// No description provided for @sign_out.
  ///
  /// In ar, this message translates to:
  /// **'Sign Out'**
  String get sign_out;

  /// No description provided for @hello.
  ///
  /// In ar, this message translates to:
  /// **'hello'**
  String get hello;

  /// No description provided for @profile.
  ///
  /// In ar, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @ready_to_pickup.
  ///
  /// In ar, this message translates to:
  /// **'Ready to pickup'**
  String get ready_to_pickup;

  /// No description provided for @ready_to_return.
  ///
  /// In ar, this message translates to:
  /// **'Ready to return'**
  String get ready_to_return;

  /// No description provided for @product_list.
  ///
  /// In ar, this message translates to:
  /// **'Product List'**
  String get product_list;

  /// No description provided for @ready_list.
  ///
  /// In ar, this message translates to:
  /// **'Ready List'**
  String get ready_list;

  /// No description provided for @search_sku_name.
  ///
  /// In ar, this message translates to:
  /// **'Search SKU/Name'**
  String get search_sku_name;

  /// No description provided for @scanner.
  ///
  /// In ar, this message translates to:
  /// **'Scanner'**
  String get scanner;

  /// No description provided for @summary.
  ///
  /// In ar, this message translates to:
  /// **'Summary'**
  String get summary;

  /// No description provided for @order_number.
  ///
  /// In ar, this message translates to:
  /// **'Order Number: '**
  String get order_number;

  /// No description provided for @customer_details.
  ///
  /// In ar, this message translates to:
  /// **'Customer details'**
  String get customer_details;

  /// No description provided for @barcode.
  ///
  /// In ar, this message translates to:
  /// **'Barcode'**
  String get barcode;

  /// No description provided for @picked_item.
  ///
  /// In ar, this message translates to:
  /// **'Picked Item'**
  String get picked_item;

  /// No description provided for @picked.
  ///
  /// In ar, this message translates to:
  /// **'Picked'**
  String get picked;

  /// No description provided for @picked_up.
  ///
  /// In ar, this message translates to:
  /// **'Picked Up'**
  String get picked_up;

  /// No description provided for @code.
  ///
  /// In ar, this message translates to:
  /// **'Code'**
  String get code;

  /// No description provided for @item_count.
  ///
  /// In ar, this message translates to:
  /// **'Item Count'**
  String get item_count;

  /// No description provided for @product_count.
  ///
  /// In ar, this message translates to:
  /// **'Product Count'**
  String get product_count;

  /// No description provided for @product.
  ///
  /// In ar, this message translates to:
  /// **'Product'**
  String get product;

  /// No description provided for @item.
  ///
  /// In ar, this message translates to:
  /// **'Item'**
  String get item;

  /// No description provided for @email_or_phone_number.
  ///
  /// In ar, this message translates to:
  /// **'Email or phone number'**
  String get email_or_phone_number;

  /// No description provided for @email_error_message.
  ///
  /// In ar, this message translates to:
  /// **'You must fill username input'**
  String get email_error_message;

  /// No description provided for @password_error_message.
  ///
  /// In ar, this message translates to:
  /// **'You must fill password input'**
  String get password_error_message;

  /// No description provided for @send.
  ///
  /// In ar, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @forget_password_error_message.
  ///
  /// In ar, this message translates to:
  /// **'You must enter phone number or valid email input'**
  String get forget_password_error_message;

  /// No description provided for @check_sms.
  ///
  /// In ar, this message translates to:
  /// **'Check The SMS'**
  String get check_sms;

  /// No description provided for @phone_number_error_message.
  ///
  /// In ar, this message translates to:
  /// **'You must enter phone number input'**
  String get phone_number_error_message;

  /// No description provided for @verification_code.
  ///
  /// In ar, this message translates to:
  /// **'Verification Code'**
  String get verification_code;

  /// No description provided for @write_phone_number.
  ///
  /// In ar, this message translates to:
  /// **'Write your phone number'**
  String get write_phone_number;

  /// No description provided for @write_new_password.
  ///
  /// In ar, this message translates to:
  /// **'Write the new password'**
  String get write_new_password;

  /// No description provided for @confirm_new_password.
  ///
  /// In ar, this message translates to:
  /// **'Confirm the new password'**
  String get confirm_new_password;

  /// No description provided for @confirm_new_password_error_message.
  ///
  /// In ar, this message translates to:
  /// **'You must fill confirm new password input'**
  String get confirm_new_password_error_message;

  /// No description provided for @point_your_device.
  ///
  /// In ar, this message translates to:
  /// **'Point your device at the product to scanned'**
  String get point_your_device;

  /// No description provided for @you_can_easily_scan.
  ///
  /// In ar, this message translates to:
  /// **'You can easily scan your item to check availiability'**
  String get you_can_easily_scan;

  /// No description provided for @order_must_be_scanned.
  ///
  /// In ar, this message translates to:
  /// **'All products for this order must be scanned'**
  String get order_must_be_scanned;

  /// No description provided for @confirm.
  ///
  /// In ar, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @scan.
  ///
  /// In ar, this message translates to:
  /// **'Scan'**
  String get scan;

  /// No description provided for @home.
  ///
  /// In ar, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @check.
  ///
  /// In ar, this message translates to:
  /// **'Check'**
  String get check;

  /// No description provided for @address.
  ///
  /// In ar, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @warning.
  ///
  /// In ar, this message translates to:
  /// **'Warning'**
  String get warning;

  /// No description provided for @over_quantity.
  ///
  /// In ar, this message translates to:
  /// **'You erased an extra piece of the required quantity of the product'**
  String get over_quantity;

  /// No description provided for @returned_done.
  ///
  /// In ar, this message translates to:
  /// **'Returned done.'**
  String get returned_done;

  /// No description provided for @return_it.
  ///
  /// In ar, this message translates to:
  /// **'Return it'**
  String get return_it;

  /// No description provided for @scan_more.
  ///
  /// In ar, this message translates to:
  /// **'Scan More'**
  String get scan_more;

  /// No description provided for @saveChanges.
  ///
  /// In ar, this message translates to:
  /// **'حفظ'**
  String get saveChanges;

  /// No description provided for @language.
  ///
  /// In ar, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @curLang.
  ///
  /// In ar, this message translates to:
  /// **'English'**
  String get curLang;

  /// No description provided for @profileDetails.
  ///
  /// In ar, this message translates to:
  /// **'Profile details'**
  String get profileDetails;

  /// No description provided for @userName.
  ///
  /// In ar, this message translates to:
  /// **'User name'**
  String get userName;

  /// No description provided for @email.
  ///
  /// In ar, this message translates to:
  /// **'بريد إلكتروني'**
  String get email;

  /// No description provided for @requiredField.
  ///
  /// In ar, this message translates to:
  /// **'Required field'**
  String get requiredField;

  /// No description provided for @enterAValidPhoneNumberOrMail.
  ///
  /// In ar, this message translates to:
  /// **'Enter a valid phone number or email'**
  String get enterAValidPhoneNumberOrMail;

  /// No description provided for @pleaseEnterCode.
  ///
  /// In ar, this message translates to:
  /// **'Please enter code'**
  String get pleaseEnterCode;

  /// No description provided for @confirmPasswordError.
  ///
  /// In ar, this message translates to:
  /// **'Confirm password does not match password'**
  String get confirmPasswordError;

  /// No description provided for @passwordError.
  ///
  /// In ar, this message translates to:
  /// **'Password must be more than 8 characters'**
  String get passwordError;

  /// No description provided for @shelf.
  ///
  /// In ar, this message translates to:
  /// **'Shelf'**
  String get shelf;

  /// No description provided for @hiWelcomeBaCK.
  ///
  /// In ar, this message translates to:
  /// **'Hi! Welcome back'**
  String get hiWelcomeBaCK;

  /// No description provided for @signInToYourAccount.
  ///
  /// In ar, this message translates to:
  /// **'Sign In to your account'**
  String get signInToYourAccount;

  /// No description provided for @normalUser.
  ///
  /// In ar, this message translates to:
  /// **'Normal User'**
  String get normalUser;

  /// No description provided for @company.
  ///
  /// In ar, this message translates to:
  /// **'Company'**
  String get company;

  /// No description provided for @trucOwner.
  ///
  /// In ar, this message translates to:
  /// **'Truck Owner'**
  String get trucOwner;

  /// No description provided for @next.
  ///
  /// In ar, this message translates to:
  /// **'التالي'**
  String get next;

  /// No description provided for @signIn.
  ///
  /// In ar, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// No description provided for @dontHaveAnAccount.
  ///
  /// In ar, this message translates to:
  /// **'ليس لديك حساب؟'**
  String get dontHaveAnAccount;

  /// No description provided for @signUp.
  ///
  /// In ar, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// No description provided for @byUsingOurServicesYouAreAgreeingToOur.
  ///
  /// In ar, this message translates to:
  /// **'By using our services you are agreeing to our'**
  String get byUsingOurServicesYouAreAgreeingToOur;

  /// No description provided for @terms.
  ///
  /// In ar, this message translates to:
  /// **'Terms'**
  String get terms;

  /// No description provided for @and.
  ///
  /// In ar, this message translates to:
  /// **'and'**
  String get and;

  /// No description provided for @privacyPolicy.
  ///
  /// In ar, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @pleaseFillOutTheFormBelow.
  ///
  /// In ar, this message translates to:
  /// **'Please fill out the form below!'**
  String get pleaseFillOutTheFormBelow;

  /// No description provided for @userInfo.
  ///
  /// In ar, this message translates to:
  /// **'User Info'**
  String get userInfo;

  /// No description provided for @welcomeBack.
  ///
  /// In ar, this message translates to:
  /// **'Welcome back.'**
  String get welcomeBack;

  /// No description provided for @bestDeliveryTrucks.
  ///
  /// In ar, this message translates to:
  /// **'Best delivery trucks'**
  String get bestDeliveryTrucks;

  /// No description provided for @follow_your_current_route.
  ///
  /// In ar, this message translates to:
  /// **'Follow your current route!'**
  String get follow_your_current_route;

  /// No description provided for @createANewTripNow.
  ///
  /// In ar, this message translates to:
  /// **'Create a new trip now!'**
  String get createANewTripNow;

  /// No description provided for @more.
  ///
  /// In ar, this message translates to:
  /// **'More'**
  String get more;

  /// No description provided for @select.
  ///
  /// In ar, this message translates to:
  /// **'اختار'**
  String get select;

  /// No description provided for @pleaseSelectNearbyPlacesToYou.
  ///
  /// In ar, this message translates to:
  /// **'Please Select nearby places to you'**
  String get pleaseSelectNearbyPlacesToYou;

  /// No description provided for @searchForPlace.
  ///
  /// In ar, this message translates to:
  /// **'Search for a place'**
  String get searchForPlace;

  /// No description provided for @enterDeliveryAddress.
  ///
  /// In ar, this message translates to:
  /// **'Enter delivery address'**
  String get enterDeliveryAddress;

  /// No description provided for @thisFieldIsRequired.
  ///
  /// In ar, this message translates to:
  /// **'This field is required'**
  String get thisFieldIsRequired;

  /// No description provided for @truckType.
  ///
  /// In ar, this message translates to:
  /// **'Truck Type'**
  String get truckType;

  /// No description provided for @save.
  ///
  /// In ar, this message translates to:
  /// **'save'**
  String get save;

  /// No description provided for @cancel.
  ///
  /// In ar, this message translates to:
  /// **'cancel'**
  String get cancel;

  /// No description provided for @dataAndTime.
  ///
  /// In ar, this message translates to:
  /// **'Data And Time'**
  String get dataAndTime;

  /// No description provided for @weight.
  ///
  /// In ar, this message translates to:
  /// **'Weight ( Kg )'**
  String get weight;

  /// No description provided for @typeOfShipment.
  ///
  /// In ar, this message translates to:
  /// **'Type of Shipment'**
  String get typeOfShipment;

  /// No description provided for @clickToUpload.
  ///
  /// In ar, this message translates to:
  /// **'Click to upload'**
  String get clickToUpload;

  /// No description provided for @orDragAndDrop.
  ///
  /// In ar, this message translates to:
  /// **'or drag and drop'**
  String get orDragAndDrop;

  /// No description provided for @fileTypes.
  ///
  /// In ar, this message translates to:
  /// **'.xls  PDF. JPG, GIF. PNG (max. 3 mega)'**
  String get fileTypes;

  /// No description provided for @lblGallery.
  ///
  /// In ar, this message translates to:
  /// **'Gallery'**
  String get lblGallery;

  /// No description provided for @camera.
  ///
  /// In ar, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @document.
  ///
  /// In ar, this message translates to:
  /// **'Document'**
  String get document;

  /// No description provided for @addNote.
  ///
  /// In ar, this message translates to:
  /// **'Add Note'**
  String get addNote;

  /// No description provided for @myOrders.
  ///
  /// In ar, this message translates to:
  /// **'My Orders'**
  String get myOrders;

  /// No description provided for @active.
  ///
  /// In ar, this message translates to:
  /// **'Active'**
  String get active;

  /// No description provided for @history.
  ///
  /// In ar, this message translates to:
  /// **'History'**
  String get history;

  /// No description provided for @offers.
  ///
  /// In ar, this message translates to:
  /// **'Offers'**
  String get offers;

  /// No description provided for @noData.
  ///
  /// In ar, this message translates to:
  /// **'No Data'**
  String get noData;

  /// No description provided for @orderDetails.
  ///
  /// In ar, this message translates to:
  /// **'Order Details'**
  String get orderDetails;

  /// No description provided for @map.
  ///
  /// In ar, this message translates to:
  /// **'Map'**
  String get map;

  /// No description provided for @view.
  ///
  /// In ar, this message translates to:
  /// **'View'**
  String get view;

  /// No description provided for @kg.
  ///
  /// In ar, this message translates to:
  /// **'Kg'**
  String get kg;

  /// No description provided for @shipmentPhoto.
  ///
  /// In ar, this message translates to:
  /// **'Shipment photo'**
  String get shipmentPhoto;

  /// No description provided for @trucksOffers.
  ///
  /// In ar, this message translates to:
  /// **'Trucks Offers'**
  String get trucksOffers;

  /// No description provided for @priceToDeliver.
  ///
  /// In ar, this message translates to:
  /// **'Price to deliver'**
  String get priceToDeliver;

  /// No description provided for @startShipping.
  ///
  /// In ar, this message translates to:
  /// **'Start Shipping'**
  String get startShipping;

  /// No description provided for @checkout.
  ///
  /// In ar, this message translates to:
  /// **'Checkout'**
  String get checkout;

  /// No description provided for @promo.
  ///
  /// In ar, this message translates to:
  /// **'Promo'**
  String get promo;

  /// No description provided for @addCode.
  ///
  /// In ar, this message translates to:
  /// **'add code'**
  String get addCode;

  /// No description provided for @orderSummary.
  ///
  /// In ar, this message translates to:
  /// **'Order Summary'**
  String get orderSummary;

  /// No description provided for @price.
  ///
  /// In ar, this message translates to:
  /// **'Price'**
  String get price;

  /// No description provided for @appFees.
  ///
  /// In ar, this message translates to:
  /// **'App Fees'**
  String get appFees;

  /// No description provided for @total.
  ///
  /// In ar, this message translates to:
  /// **'Total'**
  String get total;

  /// No description provided for @dropOff.
  ///
  /// In ar, this message translates to:
  /// **'Drop off'**
  String get dropOff;

  /// No description provided for @checkoutPayment.
  ///
  /// In ar, this message translates to:
  /// **'Checkout Payment'**
  String get checkoutPayment;

  /// No description provided for @updateProfile.
  ///
  /// In ar, this message translates to:
  /// **'Update Profile'**
  String get updateProfile;

  /// No description provided for @changeLanguage.
  ///
  /// In ar, this message translates to:
  /// **'change Language'**
  String get changeLanguage;

  /// No description provided for @myCreditDebitCards.
  ///
  /// In ar, this message translates to:
  /// **'My credit / debit cards'**
  String get myCreditDebitCards;

  /// No description provided for @paymentHistory.
  ///
  /// In ar, this message translates to:
  /// **'Payment History'**
  String get paymentHistory;

  /// No description provided for @supportPage.
  ///
  /// In ar, this message translates to:
  /// **'Support page'**
  String get supportPage;

  /// No description provided for @shareApplication.
  ///
  /// In ar, this message translates to:
  /// **'Share application'**
  String get shareApplication;

  /// No description provided for @logOutCurrentAccount.
  ///
  /// In ar, this message translates to:
  /// **'Logout Current Account'**
  String get logOutCurrentAccount;

  /// No description provided for @changeProfile.
  ///
  /// In ar, this message translates to:
  /// **'Change Profile'**
  String get changeProfile;

  /// No description provided for @addCard.
  ///
  /// In ar, this message translates to:
  /// **'Add Card'**
  String get addCard;

  /// No description provided for @pleasEnterValidCardNumber.
  ///
  /// In ar, this message translates to:
  /// **'Please input a valid card number'**
  String get pleasEnterValidCardNumber;

  /// No description provided for @pleasEnterValidExpiryDate.
  ///
  /// In ar, this message translates to:
  /// **'Please input a valid expiry date'**
  String get pleasEnterValidExpiryDate;

  /// No description provided for @pleasEnterValidCvv.
  ///
  /// In ar, this message translates to:
  /// **'Please input a valid CVV'**
  String get pleasEnterValidCvv;

  /// No description provided for @cardHolder.
  ///
  /// In ar, this message translates to:
  /// **'card Holder'**
  String get cardHolder;

  /// No description provided for @cardNumber.
  ///
  /// In ar, this message translates to:
  /// **'Card Number'**
  String get cardNumber;

  /// No description provided for @expirationDate.
  ///
  /// In ar, this message translates to:
  /// **'Expiration Date'**
  String get expirationDate;

  /// No description provided for @cvv.
  ///
  /// In ar, this message translates to:
  /// **'CVV'**
  String get cvv;

  /// No description provided for @continues.
  ///
  /// In ar, this message translates to:
  /// **'continue'**
  String get continues;

  /// No description provided for @expiryDate.
  ///
  /// In ar, this message translates to:
  /// **'Expiration Date : '**
  String get expiryDate;

  /// No description provided for @visaCard.
  ///
  /// In ar, this message translates to:
  /// **'Visa Card'**
  String get visaCard;

  /// No description provided for @myCards.
  ///
  /// In ar, this message translates to:
  /// **'My Cards'**
  String get myCards;

  /// No description provided for @support.
  ///
  /// In ar, this message translates to:
  /// **'Support'**
  String get support;

  /// No description provided for @selectWithContactDetailsShouldWeUseToContactSupport.
  ///
  /// In ar, this message translates to:
  /// **'Select with contact details should we use to contact Support'**
  String get selectWithContactDetailsShouldWeUseToContactSupport;

  /// No description provided for @whatsapp.
  ///
  /// In ar, this message translates to:
  /// **'Whatsapp'**
  String get whatsapp;

  /// No description provided for @new2.
  ///
  /// In ar, this message translates to:
  /// **'New'**
  String get new2;

  /// No description provided for @today.
  ///
  /// In ar, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @markAllAsRead.
  ///
  /// In ar, this message translates to:
  /// **'Mark all as read'**
  String get markAllAsRead;

  /// No description provided for @yesterday.
  ///
  /// In ar, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @myWallet.
  ///
  /// In ar, this message translates to:
  /// **'My Wallet'**
  String get myWallet;

  /// No description provided for @yourBalance.
  ///
  /// In ar, this message translates to:
  /// **'Your Balance'**
  String get yourBalance;

  /// No description provided for @deposit.
  ///
  /// In ar, this message translates to:
  /// **'Deposit'**
  String get deposit;

  /// No description provided for @withdraw.
  ///
  /// In ar, this message translates to:
  /// **'Withdraw'**
  String get withdraw;

  /// No description provided for @transaction.
  ///
  /// In ar, this message translates to:
  /// **'Transaction'**
  String get transaction;

  /// No description provided for @saveCard.
  ///
  /// In ar, this message translates to:
  /// **'Save Card'**
  String get saveCard;

  /// No description provided for @enterValidOtp.
  ///
  /// In ar, this message translates to:
  /// **'enter valid Otp'**
  String get enterValidOtp;

  /// No description provided for @getOffers.
  ///
  /// In ar, this message translates to:
  /// **'Get Offers'**
  String get getOffers;

  /// No description provided for @close.
  ///
  /// In ar, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @post.
  ///
  /// In ar, this message translates to:
  /// **'Post'**
  String get post;

  /// No description provided for @pleaseCompleteOrderData.
  ///
  /// In ar, this message translates to:
  /// **'Please complete order data'**
  String get pleaseCompleteOrderData;

  /// No description provided for @currentLocation.
  ///
  /// In ar, this message translates to:
  /// **'Current Location'**
  String get currentLocation;

  /// No description provided for @chat.
  ///
  /// In ar, this message translates to:
  /// **'Chat'**
  String get chat;

  /// No description provided for @cancelReport.
  ///
  /// In ar, this message translates to:
  /// **'Cancel,Report'**
  String get cancelReport;

  /// No description provided for @done.
  ///
  /// In ar, this message translates to:
  /// **'done'**
  String get done;

  /// No description provided for @truckTypeIsRequired.
  ///
  /// In ar, this message translates to:
  /// **'Truck type is required'**
  String get truckTypeIsRequired;

  /// No description provided for @pickedIsRequired.
  ///
  /// In ar, this message translates to:
  /// **'Picked Date is required'**
  String get pickedIsRequired;

  /// No description provided for @weightIsRequired.
  ///
  /// In ar, this message translates to:
  /// **'weight is required'**
  String get weightIsRequired;

  /// No description provided for @shipmentTypeIsRequired.
  ///
  /// In ar, this message translates to:
  /// **'shipment Type is required'**
  String get shipmentTypeIsRequired;

  /// No description provided for @shipmentImageIsRequired.
  ///
  /// In ar, this message translates to:
  /// **'shipment image is required'**
  String get shipmentImageIsRequired;

  /// No description provided for @onBoard1.
  ///
  /// In ar, this message translates to:
  /// **'علاج'**
  String get onBoard1;

  /// No description provided for @onBoard1color.
  ///
  /// In ar, this message translates to:
  /// **'نهائي'**
  String get onBoard1color;

  /// No description provided for @onBoard1AfterColor.
  ///
  /// In ar, this message translates to:
  /// **'لنوبات الهلع'**
  String get onBoard1AfterColor;

  /// No description provided for @onBoard2.
  ///
  /// In ar, this message translates to:
  /// **'راحة نفسية'**
  String get onBoard2;

  /// No description provided for @onBoard2color.
  ///
  /// In ar, this message translates to:
  /// **'دائمة'**
  String get onBoard2color;

  /// No description provided for @onBoard2AfterColor.
  ///
  /// In ar, this message translates to:
  /// **''**
  String get onBoard2AfterColor;

  /// No description provided for @onBoard3.
  ///
  /// In ar, this message translates to:
  /// **'المعالج النفسي'**
  String get onBoard3;

  /// No description provided for @onBoard3color.
  ///
  /// In ar, this message translates to:
  /// **'سليم كيال'**
  String get onBoard3color;

  /// No description provided for @onBoard3AfterColor.
  ///
  /// In ar, this message translates to:
  /// **''**
  String get onBoard3AfterColor;

  /// No description provided for @onBoard4.
  ///
  /// In ar, this message translates to:
  /// **'مرحبًا بك في '**
  String get onBoard4;

  /// No description provided for @onBoard4color1.
  ///
  /// In ar, this message translates to:
  /// **'Arab'**
  String get onBoard4color1;

  /// No description provided for @onBoard4color2.
  ///
  /// In ar, this message translates to:
  /// **'CBT'**
  String get onBoard4color2;

  /// No description provided for @onBoard4AfterColor.
  ///
  /// In ar, this message translates to:
  /// **'الطفرة الجديدة للعلاج النفسي'**
  String get onBoard4AfterColor;

  /// No description provided for @onboardingDescription1.
  ///
  /// In ar, this message translates to:
  /// **'ستتخلص من الخوف ومن جميع الافكار السلبية المخيفة والتفكير المفرط. وستسيطر على جسدك وردود افعاله.'**
  String get onboardingDescription1;

  /// No description provided for @onboardingDescription2.
  ///
  /// In ar, this message translates to:
  /// **'هذا التطبيق سيجعلك تتمتّع بالهدوء النفسي والسلام الداخلي والتفكير الايجابي في كل مكان, على مدار اليوم وفي جميع أيام السنة.'**
  String get onboardingDescription2;

  /// No description provided for @onboardingDescription3.
  ///
  /// In ar, this message translates to:
  /// **' سيرافقك أفضل المعالجين في كل دقيقة، وسيساعدك في التفكير بشكل ايجابي، ويخلصك من جميع افكارك السلبية.'**
  String get onboardingDescription3;

  /// No description provided for @yourPerfectMentalHealthCompanion.
  ///
  /// In ar, this message translates to:
  /// **'رفيقك المناسب للصحة النفسية،'**
  String get yourPerfectMentalHealthCompanion;

  /// No description provided for @anytimeAnywhere.
  ///
  /// In ar, this message translates to:
  /// **' في كل زمان وفي أي مكان 🍃'**
  String get anytimeAnywhere;

  /// No description provided for @doYouAlreadyHaveAnAccount.
  ///
  /// In ar, this message translates to:
  /// **'هل لديك حساب بالفعل؟'**
  String get doYouAlreadyHaveAnAccount;

  /// No description provided for @logIn.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول.'**
  String get logIn;

  /// No description provided for @logIn2.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول'**
  String get logIn2;

  /// No description provided for @startNow.
  ///
  /// In ar, this message translates to:
  /// **'ابدا الان'**
  String get startNow;

  /// No description provided for @logInToYourAccount.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول إلى حسابك'**
  String get logInToYourAccount;

  /// No description provided for @enterYourEmailAndPasswordToLogIn.
  ///
  /// In ar, this message translates to:
  /// **'أدخل بريدك الإلكتروني وكلمة المرور لتسجيل الدخول'**
  String get enterYourEmailAndPasswordToLogIn;

  /// No description provided for @continueWithGoogle.
  ///
  /// In ar, this message translates to:
  /// **'متابعة مع جوجل'**
  String get continueWithGoogle;

  /// No description provided for @orLogInUsing.
  ///
  /// In ar, this message translates to:
  /// **'أو قم بتسجيل الدخول باستخدام'**
  String get orLogInUsing;

  /// No description provided for @forgotPassword.
  ///
  /// In ar, this message translates to:
  /// **'هل نسيت كلمة السر؟'**
  String get forgotPassword;

  /// No description provided for @registerNew.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل جديد'**
  String get registerNew;

  /// No description provided for @enterYourNameEmailAndPasswordToSubscribe.
  ///
  /// In ar, this message translates to:
  /// **'أدخل اسمك وبريدك الإلكتروني وكلمة المرور للاشتراك'**
  String get enterYourNameEmailAndPasswordToSubscribe;

  /// No description provided for @fullName.
  ///
  /// In ar, this message translates to:
  /// **'الاسم الكامل'**
  String get fullName;

  /// No description provided for @surname.
  ///
  /// In ar, this message translates to:
  /// **'الكنية'**
  String get surname;

  /// No description provided for @enterSurname.
  ///
  /// In ar, this message translates to:
  /// **'أدخل الكنية'**
  String get enterSurname;

  /// No description provided for @enterFullName.
  ///
  /// In ar, this message translates to:
  /// **'أدخل الاسم الكامل'**
  String get enterFullName;

  /// No description provided for @passwordTooShort.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور قصيرة'**
  String get passwordTooShort;

  /// No description provided for @orCreateANewAccount.
  ///
  /// In ar, this message translates to:
  /// **'أو قم بإنشاء حساب جديد'**
  String get orCreateANewAccount;

  /// No description provided for @registerWithGoogle.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل عن طريق جوجل'**
  String get registerWithGoogle;

  /// No description provided for @whatIsYourGender.
  ///
  /// In ar, this message translates to:
  /// **'ما هو جنسك؟'**
  String get whatIsYourGender;

  /// No description provided for @male.
  ///
  /// In ar, this message translates to:
  /// **'ذكر'**
  String get male;

  /// No description provided for @female.
  ///
  /// In ar, this message translates to:
  /// **'أنثى'**
  String get female;

  /// No description provided for @whatIsYourAge.
  ///
  /// In ar, this message translates to:
  /// **'ما هو عمرك؟'**
  String get whatIsYourAge;

  /// No description provided for @chooseTheTopicsYouWantUsToHelpYouWith.
  ///
  /// In ar, this message translates to:
  /// **'اختر أهم المواضيع التي تريد أن نساعدك فيها'**
  String get chooseTheTopicsYouWantUsToHelpYouWith;

  /// No description provided for @chooseTheNumberYouWant.
  ///
  /// In ar, this message translates to:
  /// **'اختر العدد الذي تريده'**
  String get chooseTheNumberYouWant;

  /// No description provided for @dayFreeTrial.
  ///
  /// In ar, this message translates to:
  /// **'14 يوم تجربة مجانية'**
  String get dayFreeTrial;

  /// No description provided for @oneOnOneLiveSessions.
  ///
  /// In ar, this message translates to:
  /// **'جلسات فردية مباشرة'**
  String get oneOnOneLiveSessions;

  /// No description provided for @continuousFollowUp.
  ///
  /// In ar, this message translates to:
  /// **'متابعة مستمرة'**
  String get continuousFollowUp;

  /// No description provided for @hourSupport.
  ///
  /// In ar, this message translates to:
  /// **'دعم على مدار الساعة'**
  String get hourSupport;

  /// No description provided for @quickConsultations.
  ///
  /// In ar, this message translates to:
  /// **'استشارات سريعة'**
  String get quickConsultations;

  /// No description provided for @customContent.
  ///
  /// In ar, this message translates to:
  /// **'محتوى مخصص'**
  String get customContent;

  /// No description provided for @lifetime.
  ///
  /// In ar, this message translates to:
  /// **'مدى الحياة'**
  String get lifetime;

  /// No description provided for @try14DaysForFreeThenPay12999OnceAndUseForeverYouCanCancelAnyTime.
  ///
  /// In ar, this message translates to:
  /// **'جرب 14 يومًا مجانًا، ثم ادفع مرة واحدة 129.99 واستخدمها إلى الأبد. يمكنك إلغاء الاشتراك في أي وقت'**
  String get try14DaysForFreeThenPay12999OnceAndUseForeverYouCanCancelAnyTime;

  /// No description provided for @start14DayFreeTrial.
  ///
  /// In ar, this message translates to:
  /// **'ابدأ تجربة مجانية لمدة 14 يومًا'**
  String get start14DayFreeTrial;

  /// No description provided for @subscribeLater.
  ///
  /// In ar, this message translates to:
  /// **'اشترك لاحقًا'**
  String get subscribeLater;

  /// No description provided for @becauseYouDeserveHappinessAndPeaceOfMind.
  ///
  /// In ar, this message translates to:
  /// **'لأنك تستحق السعادة والهدوء النفسي'**
  String get becauseYouDeserveHappinessAndPeaceOfMind;

  /// No description provided for @skip.
  ///
  /// In ar, this message translates to:
  /// **'تخطي'**
  String get skip;

  /// No description provided for @women.
  ///
  /// In ar, this message translates to:
  /// **'انثى'**
  String get women;

  /// No description provided for @continue1.
  ///
  /// In ar, this message translates to:
  /// **'التالي'**
  String get continue1;

  /// No description provided for @chooseTheMostImportant.
  ///
  /// In ar, this message translates to:
  /// **'اختر اهم المواضيع التي تريد ان نساعدك فيها'**
  String get chooseTheMostImportant;

  /// No description provided for @finished.
  ///
  /// In ar, this message translates to:
  /// **'انتهيت'**
  String get finished;

  /// No description provided for @openingSalimCbt.
  ///
  /// In ar, this message translates to:
  /// **'فتح حساب Salim CBT المميز'**
  String get openingSalimCbt;

  /// No description provided for @bestDeal.
  ///
  /// In ar, this message translates to:
  /// **'افضل صفقة'**
  String get bestDeal;

  /// No description provided for @howYouFeelToday.
  ///
  /// In ar, this message translates to:
  /// **'كيف تشعر اليوم؟'**
  String get howYouFeelToday;

  /// No description provided for @getRidOfYourFearNow.
  ///
  /// In ar, this message translates to:
  /// **'تخلّص من خوفك الآن'**
  String get getRidOfYourFearNow;

  /// No description provided for @areYourFearsBlockingYourGoals.
  ///
  /// In ar, this message translates to:
  /// **'هل تعيقك مخاوفك عن تحقيق أهدافك؟انضم إلينا الآن وتعلم كيفية التغلب عليها.ابدأ رحلتك وكن قائد مصيرك.لا تدع الخوف يتحكم بك بعد اليوم!'**
  String get areYourFearsBlockingYourGoals;

  /// No description provided for @receiveRecordedTherapySessions.
  ///
  /// In ar, this message translates to:
  /// **'تلقى جلسات علاج نفسي مسجلة مع المعالج النفسي سليم كيال'**
  String get receiveRecordedTherapySessions;

  /// No description provided for @subscribeNow.
  ///
  /// In ar, this message translates to:
  /// **'اشترك الآن'**
  String get subscribeNow;

  /// No description provided for @prioritizeYourMentalHealthToday.
  ///
  /// In ar, this message translates to:
  /// **'أعطِ صحتك العقلية الأولوية اليوم!'**
  String get prioritizeYourMentalHealthToday;

  /// No description provided for @bookGoodbyeToNegativeThoughts.
  ///
  /// In ar, this message translates to:
  /// **'كتاب وداعًا للأفكار السلبية'**
  String get bookGoodbyeToNegativeThoughts;

  /// No description provided for @yourCompleteGuideToGettingRidOfNegativeThoughts.
  ///
  /// In ar, this message translates to:
  /// **'دليلك الكامل للتخلص من أفكارك السلبية'**
  String get yourCompleteGuideToGettingRidOfNegativeThoughts;

  /// No description provided for @chooseConcernsToEliminate.
  ///
  /// In ar, this message translates to:
  /// **'قم باختيار المخاوف التي ترغب في التخلص منها'**
  String get chooseConcernsToEliminate;

  /// No description provided for @whatThoughtsDoYouExperienceDuringPanicAttack.
  ///
  /// In ar, this message translates to:
  /// **'ما هي الأفكار التي تواجهها من نوبة الهلع؟'**
  String get whatThoughtsDoYouExperienceDuringPanicAttack;

  /// No description provided for @howDoYouWantToEliminateYourFears.
  ///
  /// In ar, this message translates to:
  /// **'كيف تريد أن تتخلص من مخاوفك؟'**
  String get howDoYouWantToEliminateYourFears;

  /// No description provided for @beforeProceedingToWaysToEliminateNegativeThoughts.
  ///
  /// In ar, this message translates to:
  /// **'قبل الانتقال لطرق التخلص من الأفكار السلبية، شاهد فيديو مهم وضروري في شرح القلق ونوبات الهلع'**
  String get beforeProceedingToWaysToEliminateNegativeThoughts;

  /// No description provided for @fearOfPanicAttack.
  ///
  /// In ar, this message translates to:
  /// **'الخوف من نوبة الهلع'**
  String get fearOfPanicAttack;

  /// No description provided for @youCanChoseTwice.
  ///
  /// In ar, this message translates to:
  /// **'يمكنك اختيار الطريقتين'**
  String get youCanChoseTwice;

  /// No description provided for @byVoiceRecorder.
  ///
  /// In ar, this message translates to:
  /// **'سماع تسجيل صوتي للمعالج النفسي'**
  String get byVoiceRecorder;

  /// No description provided for @destroyBadThinking.
  ///
  /// In ar, this message translates to:
  /// **'استجواب التخلص من الافكار السلبية والخوف'**
  String get destroyBadThinking;

  /// No description provided for @recordings.
  ///
  /// In ar, this message translates to:
  /// **'التسجيلات'**
  String get recordings;

  /// No description provided for @form.
  ///
  /// In ar, this message translates to:
  /// **'الاستمارة'**
  String get form;

  /// No description provided for @formSummary.
  ///
  /// In ar, this message translates to:
  /// **'ملخص الاستمارة'**
  String get formSummary;

  /// No description provided for @yes.
  ///
  /// In ar, this message translates to:
  /// **'نعم'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In ar, this message translates to:
  /// **'لا'**
  String get no;

  /// No description provided for @nextQuestion.
  ///
  /// In ar, this message translates to:
  /// **'السؤال التالي'**
  String get nextQuestion;

  /// No description provided for @editProfile.
  ///
  /// In ar, this message translates to:
  /// **'تعديل الملف الشخصي'**
  String get editProfile;

  /// No description provided for @personalInformation.
  ///
  /// In ar, this message translates to:
  /// **'المعلومات الشخصية'**
  String get personalInformation;

  /// No description provided for @username.
  ///
  /// In ar, this message translates to:
  /// **'اسم المستخدم:'**
  String get username;

  /// No description provided for @email2.
  ///
  /// In ar, this message translates to:
  /// **'البريد الإلكتروني:'**
  String get email2;

  /// No description provided for @password2.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور:'**
  String get password2;

  /// No description provided for @preferences.
  ///
  /// In ar, this message translates to:
  /// **'التفضيلات'**
  String get preferences;

  /// No description provided for @darkMode.
  ///
  /// In ar, this message translates to:
  /// **'الوضع المظلم'**
  String get darkMode;

  /// No description provided for @paymentNotification.
  ///
  /// In ar, this message translates to:
  /// **'إشعار الدفع'**
  String get paymentNotification;

  /// No description provided for @deleteAccount.
  ///
  /// In ar, this message translates to:
  /// **'حذف الحساب'**
  String get deleteAccount;

  /// No description provided for @proceedToPayment.
  ///
  /// In ar, this message translates to:
  /// **'انتقل إلى الدفع'**
  String get proceedToPayment;

  /// No description provided for @applyCoupons.
  ///
  /// In ar, this message translates to:
  /// **'تطبيق القسائم'**
  String get applyCoupons;

  /// No description provided for @orderPaymentDetails.
  ///
  /// In ar, this message translates to:
  /// **'تفاصيل دفع الطلب'**
  String get orderPaymentDetails;

  /// No description provided for @orderPrice.
  ///
  /// In ar, this message translates to:
  /// **'سعر الطلب'**
  String get orderPrice;

  /// No description provided for @deliveryFee.
  ///
  /// In ar, this message translates to:
  /// **'رسوم التوصيل'**
  String get deliveryFee;

  /// No description provided for @free.
  ///
  /// In ar, this message translates to:
  /// **'مجاني'**
  String get free;

  /// No description provided for @payments.
  ///
  /// In ar, this message translates to:
  /// **'مدفوعاتك'**
  String get payments;

  /// No description provided for @notifications.
  ///
  /// In ar, this message translates to:
  /// **'الاشعارات'**
  String get notifications;

  /// No description provided for @enterEmail.
  ///
  /// In ar, this message translates to:
  /// **'ادخل البريد الالكتروني'**
  String get enterEmail;

  /// No description provided for @enterPassword.
  ///
  /// In ar, this message translates to:
  /// **'ادخل كلمة المرور'**
  String get enterPassword;

  /// No description provided for @enterPhoneNumber.
  ///
  /// In ar, this message translates to:
  /// **'ادخل رقم الهاتف'**
  String get enterPhoneNumber;

  /// No description provided for @phoneNumber.
  ///
  /// In ar, this message translates to:
  /// **'رقم الهاتف:'**
  String get phoneNumber;

  /// No description provided for @updateApp.
  ///
  /// In ar, this message translates to:
  /// **'تحديث التطبيق'**
  String get updateApp;

  /// No description provided for @thereIsAnewVersion.
  ///
  /// In ar, this message translates to:
  /// **'هناك نسخة جديدة متاحة'**
  String get thereIsAnewVersion;

  /// No description provided for @previous.
  ///
  /// In ar, this message translates to:
  /// **'السابق'**
  String get previous;

  /// No description provided for @addToBookmark.
  ///
  /// In ar, this message translates to:
  /// **'إضافة إلى المفضلة'**
  String get addToBookmark;

  /// No description provided for @summaryTitle.
  ///
  /// In ar, this message translates to:
  /// **'ملخص الأسئلة'**
  String get summaryTitle;

  /// No description provided for @noSummariesAvailable.
  ///
  /// In ar, this message translates to:
  /// **'لا توجد ملخصات متاحة'**
  String get noSummariesAvailable;
}

class _TranslationsDelegate extends LocalizationsDelegate<Translations> {
  const _TranslationsDelegate();

  @override
  Future<Translations> load(Locale locale) {
    return SynchronousFuture<Translations>(lookupTranslations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar'].contains(locale.languageCode);

  @override
  bool shouldReload(_TranslationsDelegate old) => false;
}

Translations lookupTranslations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return TranslationsAr();
  }

  throw FlutterError(
      'Translations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
