import 'package:json_annotation/json_annotation.dart';

part 'base_response.g.dart';

@JsonSerializable(genericArgumentFactories: true, createToJson: true)
class BaseResponseModel<T> {
  @JsonKey(name: 'data')
  T? data;
  String? message;
  bool? check;
  int? total;
  @JsonKey(
    name: 'page',
    fromJson: _parsePage,
  )
  int? currentPage;
  int? pageSize;

  // New variable to store last page
  int? lastPage;

  BaseResponseModel(
    this.data, [
    this.message,
    this.check,
    this.total,
    this.currentPage,
    this.pageSize,
  ]) {
    // Calculate last page when the object is created
    // print("totaltotaltotaltotal ${total}", );
    // print("pageSizepageSizepageSize ${pageSize}", );
    lastPage = (total != null && pageSize != null && pageSize! > 0)
        ? (total! / pageSize!).ceil()
        : null;
    // print("lastPagelastPagelastPage ${lastPage}");
  }

  static int _parsePage(dynamic json) {
    if (json is String) {
      return int.tryParse(json) ?? 0; // Parse string to int
    } else if (json is int) {
      return json; // If it's already int, return it directly
    }
    return 0; // Default to 0 if neither String nor int
  }

  // static int totalFromPagination(Map<String, dynamic>? json) =>
  //     json == null ? 0 : json['total'];

  factory BaseResponseModel.fromJson(
      Map<String, dynamic> json, T Function(Object? json) fromJsonT) {
    return _$BaseResponseModelFromJson(json, fromJsonT);
  }

// Map<String, dynamic> toJson() => _$BaseResponseModelToJson(this);
}
// class MetaModel {
//   MetaModel({
//     required this.currentPage,
//     required this.from,
//     required this.lastPage,
//     required this.links,
//     required this.path,
//     required this.perPage,
//     required this.to,
//     required this.total,
//   });
//
//   int? total;
//   int? page;
//   int? pageSize;
//   int? lastPage;
//   List<Link> links;
//   String? path;
//   int? to;
//
//   factory MetaModel.fromJson(Map<String, dynamic> json) {
//     return MetaModel(
//       currentPage: json["current_page"],
//       from: json["from"],
//       lastPage: json["last_page"],
//       links: json["links"] == null
//           ? []
//           : List<Link>.from(json["links"]!.map((x) => Link.fromJson(x))),
//       path: json["path"],
//       perPage: json["per_page"],
//       to: json["to"],
//       total: json["total"],
//     );
//   }
//
//   Map<String, dynamic> toJson() => {
//         "current_page": currentPage,
//         "from": from,
//         "last_page": lastPage,
//         "links": links.map((x) => x.toJson()).toList(),
//         "path": path,
//         "per_page": perPage,
//         "to": to,
//         "total": total,
//       };
// }
//
// class Link {
//   Link({
//     required this.url,
//     required this.label,
//     required this.active,
//   });
//
//   final String? url;
//   final String? label;
//   final bool? active;
//
//   factory Link.fromJson(Map<String, dynamic> json) {
//     return Link(
//       url: json["url"],
//       label: json["label"],
//       active: json["active"],
//     );
//   }
//
//   Map<String, dynamic> toJson() => {
//         "url": url,
//         "label": label,
//         "active": active,
//       };
// }

//
// @JsonSerializable(genericArgumentFactories: true, createToJson: false)
// class BaseResponse<T> {
//   T? results;
//
//
//   BaseResponse({this.results});
//
//   factory BaseResponse.fromJson(json, T Function(Object? json) fromJsonT) {
//     return _$BaseResponseFromJson(json, fromJsonT);
//   }
//
// // factory BaseResponse.fromJson(Map<String, dynamic> json) =>
// //     _$BaseResponseFromJson(json);
//
//   // Map<String, dynamic> toJson() => _$BaseResponseToJson(this);
// }
