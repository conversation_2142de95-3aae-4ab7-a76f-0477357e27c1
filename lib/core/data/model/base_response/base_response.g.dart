// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseResponseModel<T> _$BaseResponseModelFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    BaseResponseModel<T>(
      _$nullableGenericFromJson(json['data'], fromJsonT),
      json['message'] as String?,
      json['check'] as bool?,
      (json['total'] as num?)?.toInt(),
      BaseResponseModel._parsePage(json['page']),
      (json['pageSize'] as num?)?.toInt(),
    )..lastPage = (json['lastPage'] as num?)?.toInt();

Map<String, dynamic> _$BaseResponseModelToJson<T>(
  BaseResponseModel<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'data': _$nullableGenericToJson(instance.data, toJsonT),
      'message': instance.message,
      'check': instance.check,
      'total': instance.total,
      'page': instance.currentPage,
      'pageSize': instance.pageSize,
      'lastPage': instance.lastPage,
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);
