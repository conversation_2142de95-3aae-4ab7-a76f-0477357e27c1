// import 'dart:developer';
// import 'package:clean_arc/core/data/services/shared_prefs/shared_pref.dart';
// import 'package:clean_arc/core/data/services/shared_prefs/shared_pref.dart';
// import 'package:clean_arc/core/data/services/shared_prefs/shared_prefs_key.dart';
// import 'package:clean_arc/core/data/services/shared_prefs/shared_prefs_key.dart';
// import 'package:clean_arc/core/utils_package/utils_package.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:geocoding/geocoding.dart';
// import 'package:geolocator/geolocator.dart';
// // import 'package:nb_utils/nb_utils.dart';
//
// Future<Position> getUserLocationPosition(BuildContext context) async {
//   bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
//   LocationPermission permission = await Geolocator.checkPermission();
//
//
//   if (permission == LocationPermission.denied) {
//     permission = await Geolocator.requestPermission();
//     if (permission == LocationPermission.denied) {
//       await Geolocator.openAppSettings();
//       throw '${context.translate.lblLocationPermissionDenied}';
//     }
//   }
//
//   if (permission == LocationPermission.deniedForever) {
//     throw '${context.translate.lblLocationPermissionDeniedPermanently}';
//   }
//
//   return await Geolocator.getCurrentPosition(
//           desiredAccuracy: LocationAccuracy.high)
//       .then((value) {
//     return value;
//   }).catchError((e) async {
//     return await Geolocator.getLastKnownPosition().then((value) async {
//       if (value != null) {
//         return value;
//       } else {
//         throw '${context.translate.lblEnableLocation}';
//       }
//     }).catchError((e) {
//       // toast(e.toString());
//     });
//   });
// }
//
// Future<String> getUserLocation(BuildContext context) async {
//   Position position = await getUserLocationPosition(context).catchError((e) {
//     throw e.toString();
//   });
//
//   return await buildFullAddressFromLatLong(
//       position.latitude, position.longitude);
// }
//
// Future<String> buildFullAddressFromLatLong(
//     double latitude, double longitude) async {
//   List<Placemark> placeMark =
//       await placemarkFromCoordinates(latitude, longitude).catchError((e) async {
//     log(e.toString());
//     throw 'errorSomethingWentWrong';
//   });
//
//   await SharedPref().setDouble(PrefKeys.latitude, latitude);
//   await SharedPref().setDouble(PrefKeys.longitude, latitude);
//   // setValue(LONGITUDE, longitude);
//
//   Placemark place = placeMark[0];
//
//   log(place.toJson().toString());
//
//   String address = '';
//
//   if (!place.name!.isEmptyOrNull &&
//       !place.street!.isEmptyOrNull &&
//       place.name != place.street) address = '${place.name.validate()}, ';
//   if (!place.street.isEmptyOrNull) {
//     address = '$address${place.street.validate()}';
//   }
//   if (!place.locality.isEmptyOrNull) {
//     address = '$address, ${place.locality.validate()}';
//   }
//   if (!place.administrativeArea.isEmptyOrNull) {
//     address = '$address, ${place.administrativeArea.validate()}';
//   }
//   if (!place.postalCode.isEmptyOrNull) {
//     address = '$address, ${place.postalCode.validate()}';
//   }
//   if (!place.country.isEmptyOrNull) {
//     address = '$address, ${place.country.validate()}';
//   }
//
//   SharedPref().setString(PrefKeys.currentAddress, address);
//
//   return address;
// }
