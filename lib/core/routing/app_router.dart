import 'package:auto_route/auto_route.dart';

import 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'View,ViewRoute')
class AppRouter extends $AppRouter {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(page: SplashViewRoute.page, initial: true),
        AutoRoute(
          page: OnBoardingViewRoute.page,
        ),
        AutoRoute(
          page: UserTypeViewRoute.page,
        ),
        AutoRoute(
          page: LoginViewRoute.page,
        ),
        AutoRoute(
          page: RegisterViewRoute.page,
        ),
        // AutoRoute(
        //   page: TrackOrderViewRoute.page,
        // ),
        // AutoRoute(
        //   page: ConfirmOtpViewRoute.page,
        // ),
        AutoRoute(
          page: UserProfileViewRoute.page,
        ),
        AutoRoute(
          page: UpdateProfileViewRoute.page,
        ),
        AutoRoute(page: UserLayoutViewRoute.page
            // ,initial: true

            ),
        // AutoRoute(
        //   page: CreateShipmentOrderViewRoute.page,
        // ),
        // AutoRoute(
        //   page: OrderDetailsViewRoute.page,
        // ),
        AutoRoute(
          page: CheckoutViewRoute.page,
        ),
        AutoRoute(
          page: AddCardViewRoute.page,
        ),
        AutoRoute(
          page: SupportViewRoute.page,
        ),
        AutoRoute(
          page: CardsViewRoute.page,
        ),
        AutoRoute(
          page: MyWalletViewRoute.page,
        ),
        AutoRoute(
          page: NotificationViewRoute.page,
        ),

        ///==============================================
        AutoRoute(
          page: SelectSexViewRoute.page,
        ),
        AutoRoute(
          page: SelectAgeViewRoute.page,
        ),
        AutoRoute(
          page: SelectBestObjectViewRoute.page,
        ),
        AutoRoute(
          page: FreeTryingViewRoute.page,
          // initial: true
        ),
        AutoRoute(page: CategoryListViewRoute.page
            // ,initial: true
            ),
        AutoRoute(page: SubCategoryListViewRoute.page),
        AutoRoute(page: SolveProblemMethodViewRoute.page),
        AutoRoute(page: VideoRecordedViewRoute.page),
        AutoRoute(page: SongViewRoute.page),
        AutoRoute(
          page: QuestionsViewRoute.page,
          // initial: true
        ),
        AutoRoute(
          page: ProfileViewRoute.page,
        ),
        AutoRoute(
          page: StartNowViewRoute.page,
        ),
        AutoRoute(
          page: CheckoutViewRoute.page,
        ),
        AutoRoute(
          page: RepetitionViewRoute.page,
        ),
        AutoRoute(
          page: BookViewRoute.page,
        ),
        AutoRoute(
          page: TreatmentVideoViewRoute.page,
        ),
        AutoRoute(
          page: LectureSubcategoriesViewRoute.page,
        ),
        AutoRoute(
          page: LectureVideoPlayerViewRoute.page,
        ),
      ];
}
