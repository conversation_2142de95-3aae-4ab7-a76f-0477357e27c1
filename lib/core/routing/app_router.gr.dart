// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i32;
import 'package:clean_arc/features/auth_feature/presentation/view/login/login_view.dart'
    as _i9;
import 'package:clean_arc/features/auth_feature/presentation/view/register/register_view.dart'
    as _i15;
import 'package:clean_arc/features/auth_feature/presentation/view/register/select_age_view.dart'
    as _i17;
import 'package:clean_arc/features/auth_feature/presentation/view/register/select_best_object_view.dart'
    as _i18;
import 'package:clean_arc/features/auth_feature/presentation/view/register/select_sex_view.dart'
    as _i19;
import 'package:clean_arc/features/auth_feature/presentation/view/user_type_screen/user_type_screen.dart'
    as _i30;
import 'package:clean_arc/features/book_feature/presentation/view/book_view.dart'
    as _i2;
import 'package:clean_arc/features/concerns_feature/presentation/view/select_category_view.dart'
    as _i4;
import 'package:clean_arc/features/concerns_feature/presentation/view/solve_problem_method.dart'
    as _i20;
import 'package:clean_arc/features/concerns_feature/presentation/view/subcategory_view.dart'
    as _i24;
import 'package:clean_arc/features/concerns_feature/presentation/view/video_recorded.dart'
    as _i31;
import 'package:clean_arc/features/forma_feature/presentation/view/questions_view.dart'
    as _i14;
import 'package:clean_arc/features/forma_feature/presentation/view/repetition_view.dart'
    as _i16;
import 'package:clean_arc/features/layout_feature/view/user_layout_view.dart'
    as _i28;
import 'package:clean_arc/features/lecture_feature/domain/model/lecture_model.dart'
    as _i34;
import 'package:clean_arc/features/lecture_feature/presentation/view/lecture_subcategories_view.dart'
    as _i7;
import 'package:clean_arc/features/lecture_feature/presentation/view/lecture_video_player_view.dart'
    as _i8;
import 'package:clean_arc/features/payment_feature/presentation/view/check_out_view.dart'
    as _i5;
import 'package:clean_arc/features/payment_feature/presentation/view/free_trying.dart'
    as _i6;
import 'package:clean_arc/features/payment_feature/presentation/view/my_wallet.dart'
    as _i10;
import 'package:clean_arc/features/player_feature/persentation/view/player_view.dart'
    as _i21;
import 'package:clean_arc/features/profile_feature/presentation/view/profile_view.dart'
    as _i13;
import 'package:clean_arc/features/splash_feature/presentation/view/onboarding_view.dart'
    as _i12;
import 'package:clean_arc/features/splash_feature/presentation/view/splash_screen.dart'
    as _i22;
import 'package:clean_arc/features/splash_feature/presentation/view/start_now_view.dart'
    as _i23;
import 'package:clean_arc/features/treatment_feature/presentation/view/treatment_video_view.dart'
    as _i26;
import 'package:clean_arc/features/user_layout/cards_features/view/add_card.dart'
    as _i1;
import 'package:clean_arc/features/user_layout/cards_features/view/cards_view.dart'
    as _i3;
import 'package:clean_arc/features/user_layout/notification_feature/presentation/view/notification_view.dart'
    as _i11;
import 'package:clean_arc/features/user_layout/support_feature/presentation/view/support_view.dart'
    as _i25;
import 'package:clean_arc/features/user_profile_feature/view/update_profile.dart'
    as _i27;
import 'package:clean_arc/features/user_profile_feature/view/user_profile_view.dart'
    as _i29;
import 'package:flutter/cupertino.dart' as _i35;
import 'package:flutter/material.dart' as _i33;

abstract class $AppRouter extends _i32.RootStackRouter {
  $AppRouter({super.navigatorKey});

  @override
  final Map<String, _i32.PageFactory> pagesMap = {
    AddCardViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i1.AddCardView(),
      );
    },
    BookViewRoute.name: (routeData) {
      final args = routeData.argsAs<BookViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i2.BookView(
          key: args.key,
          id: args.id,
        ),
      );
    },
    CardsViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i3.CardsView(),
      );
    },
    CategoryListViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i4.CategoryListView(),
      );
    },
    CheckoutViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i5.CheckoutView(),
      );
    },
    FreeTryingViewRoute.name: (routeData) {
      final args = routeData.argsAs<FreeTryingViewRouteArgs>(
          orElse: () => const FreeTryingViewRouteArgs());
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i6.FreeTryingView(key: args.key),
      );
    },
    LectureSubcategoriesViewRoute.name: (routeData) {
      final args = routeData.argsAs<LectureSubcategoriesViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i7.LectureSubcategoriesView(
          key: args.key,
          lecture: args.lecture,
        ),
      );
    },
    LectureVideoPlayerViewRoute.name: (routeData) {
      final args = routeData.argsAs<LectureVideoPlayerViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i8.LectureVideoPlayerView(
          key: args.key,
          title: args.title,
          description: args.description,
          videoUrl: args.videoUrl,
        ),
      );
    },
    LoginViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i9.LoginView(),
      );
    },
    MyWalletViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i10.MyWalletView(),
      );
    },
    NotificationViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i11.NotificationView(),
      );
    },
    OnBoardingViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i12.OnBoardingView(),
      );
    },
    ProfileViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i13.ProfileView(),
      );
    },
    QuestionsViewRoute.name: (routeData) {
      final args = routeData.argsAs<QuestionsViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i14.QuestionsView(
          key: args.key,
          thoughtId: args.thoughtId,
        ),
      );
    },
    RegisterViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i15.RegisterView(),
      );
    },
    RepetitionViewRoute.name: (routeData) {
      final args = routeData.argsAs<RepetitionViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i16.RepetitionView(
          key: args.key,
          thoughtId: args.thoughtId,
        ),
      );
    },
    SelectAgeViewRoute.name: (routeData) {
      final args = routeData.argsAs<SelectAgeViewRouteArgs>(
          orElse: () => const SelectAgeViewRouteArgs());
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i17.SelectAgeView(key: args.key),
      );
    },
    SelectBestObjectViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i18.SelectBestObjectView(),
      );
    },
    SelectSexViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i19.SelectSexView(),
      );
    },
    SolveProblemMethodViewRoute.name: (routeData) {
      final args = routeData.argsAs<SolveProblemMethodViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i20.SolveProblemMethodView(
          key: args.key,
          thoughtsId: args.thoughtsId,
        ),
      );
    },
    SongViewRoute.name: (routeData) {
      final args = routeData.argsAs<SongViewRouteArgs>(
          orElse: () => const SongViewRouteArgs());
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i21.SongView(key: args.key),
      );
    },
    SplashViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i22.SplashView(),
      );
    },
    StartNowViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i23.StartNowView(),
      );
    },
    SubCategoryListViewRoute.name: (routeData) {
      final args = routeData.argsAs<SubCategoryListViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i24.SubCategoryListView(
          key: args.key,
          id: args.id,
        ),
      );
    },
    SupportViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i25.SupportView(),
      );
    },
    TreatmentVideoViewRoute.name: (routeData) {
      final args = routeData.argsAs<TreatmentVideoViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i26.TreatmentVideoView(
          key: args.key,
          id: args.id,
        ),
      );
    },
    UpdateProfileViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i27.UpdateProfileView(),
      );
    },
    UserLayoutViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i28.UserLayoutView(),
      );
    },
    UserProfileViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i29.UserProfileView(),
      );
    },
    UserTypeViewRoute.name: (routeData) {
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i30.UserTypeView(),
      );
    },
    VideoRecordedViewRoute.name: (routeData) {
      final args = routeData.argsAs<VideoRecordedViewRouteArgs>();
      return _i32.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i31.VideoRecordedView(
          key: args.key,
          thoughtsId: args.thoughtsId,
        ),
      );
    },
  };
}

/// generated route for
/// [_i1.AddCardView]
class AddCardViewRoute extends _i32.PageRouteInfo<void> {
  const AddCardViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          AddCardViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'AddCardViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i2.BookView]
class BookViewRoute extends _i32.PageRouteInfo<BookViewRouteArgs> {
  BookViewRoute({
    _i33.Key? key,
    required int id,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          BookViewRoute.name,
          args: BookViewRouteArgs(
            key: key,
            id: id,
          ),
          initialChildren: children,
        );

  static const String name = 'BookViewRoute';

  static const _i32.PageInfo<BookViewRouteArgs> page =
      _i32.PageInfo<BookViewRouteArgs>(name);
}

class BookViewRouteArgs {
  const BookViewRouteArgs({
    this.key,
    required this.id,
  });

  final _i33.Key? key;

  final int id;

  @override
  String toString() {
    return 'BookViewRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [_i3.CardsView]
class CardsViewRoute extends _i32.PageRouteInfo<void> {
  const CardsViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          CardsViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'CardsViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i4.CategoryListView]
class CategoryListViewRoute extends _i32.PageRouteInfo<void> {
  const CategoryListViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          CategoryListViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'CategoryListViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i5.CheckoutView]
class CheckoutViewRoute extends _i32.PageRouteInfo<void> {
  const CheckoutViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          CheckoutViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'CheckoutViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i6.FreeTryingView]
class FreeTryingViewRoute extends _i32.PageRouteInfo<FreeTryingViewRouteArgs> {
  FreeTryingViewRoute({
    _i33.Key? key,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          FreeTryingViewRoute.name,
          args: FreeTryingViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'FreeTryingViewRoute';

  static const _i32.PageInfo<FreeTryingViewRouteArgs> page =
      _i32.PageInfo<FreeTryingViewRouteArgs>(name);
}

class FreeTryingViewRouteArgs {
  const FreeTryingViewRouteArgs({this.key});

  final _i33.Key? key;

  @override
  String toString() {
    return 'FreeTryingViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [_i7.LectureSubcategoriesView]
class LectureSubcategoriesViewRoute
    extends _i32.PageRouteInfo<LectureSubcategoriesViewRouteArgs> {
  LectureSubcategoriesViewRoute({
    _i33.Key? key,
    required _i34.LectureModel lecture,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          LectureSubcategoriesViewRoute.name,
          args: LectureSubcategoriesViewRouteArgs(
            key: key,
            lecture: lecture,
          ),
          initialChildren: children,
        );

  static const String name = 'LectureSubcategoriesViewRoute';

  static const _i32.PageInfo<LectureSubcategoriesViewRouteArgs> page =
      _i32.PageInfo<LectureSubcategoriesViewRouteArgs>(name);
}

class LectureSubcategoriesViewRouteArgs {
  const LectureSubcategoriesViewRouteArgs({
    this.key,
    required this.lecture,
  });

  final _i33.Key? key;

  final _i34.LectureModel lecture;

  @override
  String toString() {
    return 'LectureSubcategoriesViewRouteArgs{key: $key, lecture: $lecture}';
  }
}

/// generated route for
/// [_i8.LectureVideoPlayerView]
class LectureVideoPlayerViewRoute
    extends _i32.PageRouteInfo<LectureVideoPlayerViewRouteArgs> {
  LectureVideoPlayerViewRoute({
    _i33.Key? key,
    required String title,
    required String description,
    required String videoUrl,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          LectureVideoPlayerViewRoute.name,
          args: LectureVideoPlayerViewRouteArgs(
            key: key,
            title: title,
            description: description,
            videoUrl: videoUrl,
          ),
          initialChildren: children,
        );

  static const String name = 'LectureVideoPlayerViewRoute';

  static const _i32.PageInfo<LectureVideoPlayerViewRouteArgs> page =
      _i32.PageInfo<LectureVideoPlayerViewRouteArgs>(name);
}

class LectureVideoPlayerViewRouteArgs {
  const LectureVideoPlayerViewRouteArgs({
    this.key,
    required this.title,
    required this.description,
    required this.videoUrl,
  });

  final _i33.Key? key;

  final String title;

  final String description;

  final String videoUrl;

  @override
  String toString() {
    return 'LectureVideoPlayerViewRouteArgs{key: $key, title: $title, description: $description, videoUrl: $videoUrl}';
  }
}

/// generated route for
/// [_i9.LoginView]
class LoginViewRoute extends _i32.PageRouteInfo<void> {
  const LoginViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          LoginViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i10.MyWalletView]
class MyWalletViewRoute extends _i32.PageRouteInfo<void> {
  const MyWalletViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          MyWalletViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'MyWalletViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i11.NotificationView]
class NotificationViewRoute extends _i32.PageRouteInfo<void> {
  const NotificationViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          NotificationViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i12.OnBoardingView]
class OnBoardingViewRoute extends _i32.PageRouteInfo<void> {
  const OnBoardingViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          OnBoardingViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'OnBoardingViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i13.ProfileView]
class ProfileViewRoute extends _i32.PageRouteInfo<void> {
  const ProfileViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          ProfileViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i14.QuestionsView]
class QuestionsViewRoute extends _i32.PageRouteInfo<QuestionsViewRouteArgs> {
  QuestionsViewRoute({
    _i33.Key? key,
    required int thoughtId,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          QuestionsViewRoute.name,
          args: QuestionsViewRouteArgs(
            key: key,
            thoughtId: thoughtId,
          ),
          initialChildren: children,
        );

  static const String name = 'QuestionsViewRoute';

  static const _i32.PageInfo<QuestionsViewRouteArgs> page =
      _i32.PageInfo<QuestionsViewRouteArgs>(name);
}

class QuestionsViewRouteArgs {
  const QuestionsViewRouteArgs({
    this.key,
    required this.thoughtId,
  });

  final _i33.Key? key;

  final int thoughtId;

  @override
  String toString() {
    return 'QuestionsViewRouteArgs{key: $key, thoughtId: $thoughtId}';
  }
}

/// generated route for
/// [_i15.RegisterView]
class RegisterViewRoute extends _i32.PageRouteInfo<void> {
  const RegisterViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          RegisterViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'RegisterViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i16.RepetitionView]
class RepetitionViewRoute extends _i32.PageRouteInfo<RepetitionViewRouteArgs> {
  RepetitionViewRoute({
    _i33.Key? key,
    required int thoughtId,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          RepetitionViewRoute.name,
          args: RepetitionViewRouteArgs(
            key: key,
            thoughtId: thoughtId,
          ),
          initialChildren: children,
        );

  static const String name = 'RepetitionViewRoute';

  static const _i32.PageInfo<RepetitionViewRouteArgs> page =
      _i32.PageInfo<RepetitionViewRouteArgs>(name);
}

class RepetitionViewRouteArgs {
  const RepetitionViewRouteArgs({
    this.key,
    required this.thoughtId,
  });

  final _i33.Key? key;

  final int thoughtId;

  @override
  String toString() {
    return 'RepetitionViewRouteArgs{key: $key, thoughtId: $thoughtId}';
  }
}

/// generated route for
/// [_i17.SelectAgeView]
class SelectAgeViewRoute extends _i32.PageRouteInfo<SelectAgeViewRouteArgs> {
  SelectAgeViewRoute({
    _i33.Key? key,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          SelectAgeViewRoute.name,
          args: SelectAgeViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SelectAgeViewRoute';

  static const _i32.PageInfo<SelectAgeViewRouteArgs> page =
      _i32.PageInfo<SelectAgeViewRouteArgs>(name);
}

class SelectAgeViewRouteArgs {
  const SelectAgeViewRouteArgs({this.key});

  final _i33.Key? key;

  @override
  String toString() {
    return 'SelectAgeViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [_i18.SelectBestObjectView]
class SelectBestObjectViewRoute extends _i32.PageRouteInfo<void> {
  const SelectBestObjectViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          SelectBestObjectViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'SelectBestObjectViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i19.SelectSexView]
class SelectSexViewRoute extends _i32.PageRouteInfo<void> {
  const SelectSexViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          SelectSexViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'SelectSexViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i20.SolveProblemMethodView]
class SolveProblemMethodViewRoute
    extends _i32.PageRouteInfo<SolveProblemMethodViewRouteArgs> {
  SolveProblemMethodViewRoute({
    _i33.Key? key,
    required int thoughtsId,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          SolveProblemMethodViewRoute.name,
          args: SolveProblemMethodViewRouteArgs(
            key: key,
            thoughtsId: thoughtsId,
          ),
          initialChildren: children,
        );

  static const String name = 'SolveProblemMethodViewRoute';

  static const _i32.PageInfo<SolveProblemMethodViewRouteArgs> page =
      _i32.PageInfo<SolveProblemMethodViewRouteArgs>(name);
}

class SolveProblemMethodViewRouteArgs {
  const SolveProblemMethodViewRouteArgs({
    this.key,
    required this.thoughtsId,
  });

  final _i33.Key? key;

  final int thoughtsId;

  @override
  String toString() {
    return 'SolveProblemMethodViewRouteArgs{key: $key, thoughtsId: $thoughtsId}';
  }
}

/// generated route for
/// [_i21.SongView]
class SongViewRoute extends _i32.PageRouteInfo<SongViewRouteArgs> {
  SongViewRoute({
    _i35.Key? key,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          SongViewRoute.name,
          args: SongViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SongViewRoute';

  static const _i32.PageInfo<SongViewRouteArgs> page =
      _i32.PageInfo<SongViewRouteArgs>(name);
}

class SongViewRouteArgs {
  const SongViewRouteArgs({this.key});

  final _i35.Key? key;

  @override
  String toString() {
    return 'SongViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [_i22.SplashView]
class SplashViewRoute extends _i32.PageRouteInfo<void> {
  const SplashViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          SplashViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i23.StartNowView]
class StartNowViewRoute extends _i32.PageRouteInfo<void> {
  const StartNowViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          StartNowViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'StartNowViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i24.SubCategoryListView]
class SubCategoryListViewRoute
    extends _i32.PageRouteInfo<SubCategoryListViewRouteArgs> {
  SubCategoryListViewRoute({
    _i33.Key? key,
    required int id,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          SubCategoryListViewRoute.name,
          args: SubCategoryListViewRouteArgs(
            key: key,
            id: id,
          ),
          initialChildren: children,
        );

  static const String name = 'SubCategoryListViewRoute';

  static const _i32.PageInfo<SubCategoryListViewRouteArgs> page =
      _i32.PageInfo<SubCategoryListViewRouteArgs>(name);
}

class SubCategoryListViewRouteArgs {
  const SubCategoryListViewRouteArgs({
    this.key,
    required this.id,
  });

  final _i33.Key? key;

  final int id;

  @override
  String toString() {
    return 'SubCategoryListViewRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [_i25.SupportView]
class SupportViewRoute extends _i32.PageRouteInfo<void> {
  const SupportViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          SupportViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'SupportViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i26.TreatmentVideoView]
class TreatmentVideoViewRoute
    extends _i32.PageRouteInfo<TreatmentVideoViewRouteArgs> {
  TreatmentVideoViewRoute({
    _i33.Key? key,
    required int id,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          TreatmentVideoViewRoute.name,
          args: TreatmentVideoViewRouteArgs(
            key: key,
            id: id,
          ),
          initialChildren: children,
        );

  static const String name = 'TreatmentVideoViewRoute';

  static const _i32.PageInfo<TreatmentVideoViewRouteArgs> page =
      _i32.PageInfo<TreatmentVideoViewRouteArgs>(name);
}

class TreatmentVideoViewRouteArgs {
  const TreatmentVideoViewRouteArgs({
    this.key,
    required this.id,
  });

  final _i33.Key? key;

  final int id;

  @override
  String toString() {
    return 'TreatmentVideoViewRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [_i27.UpdateProfileView]
class UpdateProfileViewRoute extends _i32.PageRouteInfo<void> {
  const UpdateProfileViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          UpdateProfileViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'UpdateProfileViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i28.UserLayoutView]
class UserLayoutViewRoute extends _i32.PageRouteInfo<void> {
  const UserLayoutViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          UserLayoutViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'UserLayoutViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i29.UserProfileView]
class UserProfileViewRoute extends _i32.PageRouteInfo<void> {
  const UserProfileViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          UserProfileViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'UserProfileViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i30.UserTypeView]
class UserTypeViewRoute extends _i32.PageRouteInfo<void> {
  const UserTypeViewRoute({List<_i32.PageRouteInfo>? children})
      : super(
          UserTypeViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'UserTypeViewRoute';

  static const _i32.PageInfo<void> page = _i32.PageInfo<void>(name);
}

/// generated route for
/// [_i31.VideoRecordedView]
class VideoRecordedViewRoute
    extends _i32.PageRouteInfo<VideoRecordedViewRouteArgs> {
  VideoRecordedViewRoute({
    _i33.Key? key,
    required int thoughtsId,
    List<_i32.PageRouteInfo>? children,
  }) : super(
          VideoRecordedViewRoute.name,
          args: VideoRecordedViewRouteArgs(
            key: key,
            thoughtsId: thoughtsId,
          ),
          initialChildren: children,
        );

  static const String name = 'VideoRecordedViewRoute';

  static const _i32.PageInfo<VideoRecordedViewRouteArgs> page =
      _i32.PageInfo<VideoRecordedViewRouteArgs>(name);
}

class VideoRecordedViewRouteArgs {
  const VideoRecordedViewRouteArgs({
    this.key,
    required this.thoughtsId,
  });

  final _i33.Key? key;

  final int thoughtsId;

  @override
  String toString() {
    return 'VideoRecordedViewRouteArgs{key: $key, thoughtsId: $thoughtsId}';
  }
}
