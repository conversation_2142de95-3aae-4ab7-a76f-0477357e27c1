import 'dart:developer';

import 'package:clean_arc/core/presentation/util/app_dimensions.dart';
import 'package:clean_arc/core/presentation/widget/custom_loading.dart';
import 'package:clean_arc/core/presentation/widget/empty_widget.dart';
import 'package:flutter/material.dart';

class PaginatedListView extends StatefulWidget {
  final ScrollController scrollController;
  final Function(int? page) onPaginate;

  // final int? totalSize;
  // final int? from;
  final Widget itemView;
  final bool enabledPagination;
  final bool reverse;
  final String? message;
  final int length;
  String? currentPage;
  String? lastPage;

  PaginatedListView({
    super.key,
    required this.scrollController,
    required this.onPaginate,
    // required this.totalSize,
    // required this.from,
    required this.itemView,
    required this.message,
    required this.length,
    required this.currentPage,
    required this.lastPage,
    this.enabledPagination = true,
    this.reverse = false,
  });

  @override
  State<PaginatedListView> createState() => _PaginatedListViewState();
}

class _PaginatedListViewState extends State<PaginatedListView> {
  // int? _offset;
  // late List<int?> _offsetList;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // _offset = 1;
    // _offsetList = [1];

    widget.scrollController.addListener(() {
      if (widget.scrollController.position.pixels ==
              widget.scrollController.position.maxScrollExtent &&
          !_isLoading &&
          widget.enabledPagination) {
        if (mounted) {
          _paginate();
        }
      }
    });
  }

  void _paginate() async {
    // int totalPages = (widget.totalSize! / 10).ceil();
    log("====> offset==> ${widget.currentPage} / ${widget.lastPage}");

    // if (_offset! < totalPages && !_offsetList.contains(_offset! + 1)) {

    // print(object)
    if ((int.parse(widget.currentPage??'1')) <= (int.tryParse(widget.lastPage??'1')??1)) {
      setState(() {
        // _offset = _offset! + 1;
        // _offsetList.add(_offset);
        _isLoading = true;
      });

      await widget.onPaginate(int.parse(widget.currentPage!));

      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // print("totalSize: ${widget.totalSize}");

    // if (widget.from != null) {
    //   log("offset updated to: ${widget.from}");
    //   // _offset = widget.from;
    //   // _offsetList = [];
    //   // for (int index = 1; index <= widget.from!; index++) {
    //   //   _offsetList.add(index);
    //   // }
    // }
    return Column(
      children: [
        widget.reverse
            ? const SizedBox()
            : widget.length == 0
                ? Column(
                    children: [
                      Container(
                        height: MediaQuery.of(context).size.height / 1.7,
                        child: CustomEmptyWidget(
                          title: '',
                          // description1: widget.message,
                          // description2: '',
                        ),
                      ),
                    ],
                  )
                : widget.itemView,
        (!_isLoading)
            ? const SizedBox()
            : Center(
                child: Padding(
                  padding: _isLoading
                      ? const EdgeInsets.all(AppDimensions.paddingSizeSmall)
                      : EdgeInsets.zero,
                  child: _isLoading ? CustomLoading() : const SizedBox(),
                ),
              ),
        widget.reverse ? widget.itemView : const SizedBox(),
      ],
    );
  }
}
