import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';

class EmojiScaleDialog extends StatefulWidget {
  final String? title;
  final String? description;
  final Function(int value) onSubmit;

  const EmojiScaleDialog({
    Key? key,
    this.title,
    this.description,
    required this.onSubmit,
  }) : super(key: key);

  @override
  State<EmojiScaleDialog> createState() => _EmojiScaleDialogState();
}

class _EmojiScaleDialogState extends State<EmojiScaleDialog> {
  int? selectedValue;

  // Emoji mapping from 1 to 10 based on the provided image
  final Map<int, String> emojiMap = {
    1: '😌', // Green - Very calm/relaxed
    2: '🙂', // Light green - Happy/content
    3: '😕', // Yellow - Slightly concerned
    4: '😕', // Yellow - Concerned
    5: '🙁', // Yellow - Sad/worried
    6: '😟', // Orange - Worried
    7: '😣', // Orange - Distressed
    8: '😫', // Red-orange - Very distressed
    9: '😰', // Red - Anxious/panicked
    10: '😱', // Dark red - Extremely panicked
  };

  // Color mapping from 1 to 10 with exact HEX colors
  final Map<int, Color> colorMap = {
    1: Color(0xFF25C252), // Soft green (calm)
    2: Color(0xFFA6E86A), // Light green
    3: Color(0xFFF3DC70), // Pale yellow
    4: Color(0xFFF9D460), // Yellow-orange
    5: Color(0xFFF2BA3C), // Golden orange
    6: Color(0xFFF4A649), // Light orange
    7: Color(0xFFED792C), // Orange-red
    8: Color(0xFFDE5A3A), // Dark orange-red
    9: Color(0xFFC53030), // Deep red
    10: Color(0xFFB41C1C), // Dark red (panic)
  };

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            if (widget.title != null)
              Text(
                widget.title!,
                style: context.textStyle.copyWith(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: context.color.primaryColor,
                ),
                textAlign: TextAlign.center,
              ),

            if (widget.title != null) SizedBox(height: 12),

            // Description
            if (widget.description != null)
              Text(
                widget.description!,
                style: context.textStyle.copyWith(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),

            if (widget.description != null) SizedBox(height: 20),

            // Scale instruction
            Text(
              'اختر مستوى شعورك من 1 إلى 10',
              style: context.textStyle.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 20),

            // Emoji scale list
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: List.generate(10, (index) {
                    final value = index + 1;
                    final emoji = emojiMap[value]!;
                    final backgroundColor = colorMap[value]!;
                    final isSelected = selectedValue == value;

                    return Padding(
                      padding: EdgeInsets.symmetric(vertical: 4),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            selectedValue = value;
                          });
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 16,
                          ),
                          decoration: BoxDecoration(
                            color: backgroundColor.withValues(
                                alpha: isSelected ? 1.0 : 0.8),
                            border: Border.all(
                              color: isSelected
                                  ? Colors.white
                                  : backgroundColor.withValues(alpha: 0.3),
                              width: isSelected ? 3 : 1,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: isSelected
                                ? [
                                    BoxShadow(
                                      color: backgroundColor.withValues(
                                          alpha: 0.4),
                                      blurRadius: 8,
                                      offset: Offset(0, 2),
                                    ),
                                  ]
                                : null,
                          ),
                          child: Row(
                            children: [
                              // Number
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.white.withValues(alpha: 0.9),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: backgroundColor,
                                    width: 2,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    value.toString(),
                                    style: context.textStyle.copyWith(
                                      color: backgroundColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ),

                              SizedBox(width: 16),

                              // Emoji
                              Text(
                                emoji,
                                style: TextStyle(fontSize: 32),
                              ),

                              Spacer(),

                              // Selection indicator
                              if (isSelected)
                                Container(
                                  padding: EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.check_circle,
                                    color: backgroundColor,
                                    size: 20,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ),
            ),

            SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                // Cancel button
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey[400]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Text(
                      'إلغاء',
                      style: context.textStyle.copyWith(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 16),

                // Submit button
                Expanded(
                  child: SizedBox(
                    height: 50,
                    child: CustomButton(
                      onPressed: selectedValue != null
                          ? () {
                              Navigator.of(context).pop();
                              widget.onSubmit(selectedValue!);
                            }
                          : null,
                      child: Text(
                        'تأكيد',
                        style: context.textStyleButton,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Helper function to show the dialog
Future<void> showEmojiScaleDialog({
  required BuildContext context,
  String? title,
  String? description,
  required Function(int value) onSubmit,
}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return EmojiScaleDialog(
        title: title,
        description: description,
        onSubmit: onSubmit,
      );
    },
  );
}
