import 'package:clean_arc/core/routing/app_router.gr.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

AppBar CustomAppBar(BuildContext context,
    {required title,
    // required keys,
    bool centerTitle = false,
    bool showNotification = true,
    Widget? additionIcon}) {
  return AppBar(
      elevation: 0,
      centerTitle: centerTitle,
      backgroundColor: Colors.transparent,
      title: TextApp(
        text: title,
        style: context.textStyle.copyWith(
            color: context.color.textColor,
            fontWeight: FontWeightHelper.bold,
            fontSize: AppDimensions.fontSizeLarge),
        // fontWeight: FontWeight.w600,
      ),
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        // <-- SEE HERE
        statusBarIconBrightness: Brightness.dark,
        //<-- For Android SEE HERE (dark icons)
        statusBarBrightness:
            Brightness.light, //<-- For iOS SEE HERE (dark icons)
      ),
      actions: <Widget>[
        additionIcon ?? SizedBox(),
        // InkWell(
        //   onTap: () {
        //     Navigator.push(
        //         context, MaterialPageRoute(builder: (context) => MainScreen()));
        //   },
        //   child: Image.asset(
        //     "images/logo_white.png",
        //     height: 50,
        //     width: 50,
        //   ),
        // ),
        showNotification
            ? IconButton(
                icon: AppImages.images.svgIcon.notification.svg(),
                // OsSvgIcon(name: Images.svgImage.menuIcon,height: 100,width: 100,color: Colors.white,) ,
                onPressed: () {
                  context.router.push(NotificationViewRoute());
                },
              )
            : SizedBox()
      ]);
}
