import 'package:clean_arc/core/presentation/extintions/context_extintions.dart';
import 'package:clean_arc/core/presentation/util/app_dimensions.dart';
import 'package:clean_arc/core/presentation/widget/code_picker_widget.dart';
import 'package:clean_arc/core/presentation/widget/custom_text_field.dart';
import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextField2 extends StatefulWidget {
  final String titleText;
  final String hintText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final FocusNode? nextFocus;
  final TextInputType inputType;
  final TextInputAction inputAction;
  final bool isPassword;
  final Function? onChanged;
  final Function? onSubmit;
  final bool isEnabled;
  final int maxLines;
  final TextCapitalization capitalization;
  final String? prefixImage;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final double prefixSize;
  final TextAlign textAlign;
  final bool isAmount;
  final bool isNumber;
  final bool showTitle;
  final bool showBorder;
  final double iconSize;
  final bool isPhone;
  final String? countryDialCode;
  final Function(CountryCode countryCode)? onCountryChanged;
  final bool showLabelText;
  final bool required;
  final String? labelText;
  final String? Function(String?)? onValidate;
  final double? labelTextSize;
  final Widget? suffixChild;

  const CustomTextField2({
    super.key,
    this.titleText = 'Write something...',
    this.hintText = '',
    this.controller,
    this.focusNode,
    this.nextFocus,
    this.isEnabled = true,
    this.inputType = TextInputType.text,
    this.inputAction = TextInputAction.next,
    this.maxLines = 1,
    this.onSubmit,
    this.onChanged,
    this.prefixImage,
    this.prefixIcon,
    this.suffixIcon,
    this.capitalization = TextCapitalization.none,
    this.isPassword = false,
    this.prefixSize = AppDimensions.paddingSizeSmall,
    this.textAlign = TextAlign.start,
    this.isAmount = false,
    this.isNumber = false,
    this.showTitle = false,
    this.showBorder = true,
    this.iconSize = 18,
    this.isPhone = false,
    this.countryDialCode,
    this.onCountryChanged,
    this.showLabelText = true,
    this.required = false,
    this.labelText,
    this.onValidate,
    this.labelTextSize,
    this.suffixChild,
  });

  @override
  CustomTextField2State createState() => CustomTextField2State();
}

class CustomTextField2State extends State<CustomTextField2> {
  bool _obscureText = true;
  static const Color grayColor = Colors.grey; // Define the gray color

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
            constraints: BoxConstraints(minWidth: 75),
            height: 50,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusSmall),
                bottomLeft: Radius.circular(AppDimensions.radiusSmall),
              ),
            ),
            margin: const EdgeInsets.only(right: 0),
            padding: const EdgeInsets.only(left: 5),
            child: CodePickerWidget(
              boxDecoration: BoxDecoration(color: Theme.of(context).cardColor),
              flagWidth: 30,
              padding: EdgeInsets.zero,
              onChanged: widget.onCountryChanged,
              initialSelection: widget.countryDialCode,
              // favorite: [widget.countryDialCode!],
              textStyle: context.textStyle.copyWith(
                fontSize: AppDimensions.fontSizeDefault,
                color: Theme.of(context).textTheme.bodyMedium!.color,
              ),
              builder: (p0) {
                return Row(
                  children: [
                    Text('${p0.dialCode}'),
                    SizedBox(
                      width: 5,
                    ),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: Image.asset(
                        p0!.flagUri!,
                        package: 'country_code_picker',
                        // width: widget.flagWidth,
                        width: 35,
                      ),
                    ),
                  ],
                );
              },
            )),
        SizedBox(
          width: 5,
        ),
        Expanded(
          child: CustomTextField(
            onValidate: widget.onValidate,
            controller: widget.controller,
            titleText: widget.titleText,
            hintText: widget.titleText,
            prefixIcon: widget.prefixIcon,
          ),
        ),
      ],
    );
  }

  void _toggle() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }
}
