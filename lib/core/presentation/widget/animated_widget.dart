// AnimatedContainer buildOptions({
//   required int index,
// }) {

// }

import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:flutter/material.dart';

class AnimatedCardWidget extends StatefulWidget {
  const AnimatedCardWidget({
    required this.titles,
    required this.index,
    required this.currentIndex,
    required this.containerColor,
    required this.textColor,
    super.key,
    this.onTap,
  });

  final List<String> titles;
  final int index;
  final int currentIndex;

  final void Function()? onTap;
  final Color containerColor;
  final Color textColor;

  @override
  State<AnimatedCardWidget> createState() => _AnimatedCardWidgetState();
}

class _AnimatedCardWidgetState extends State<AnimatedCardWidget> {
  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: InkWell(
        onTap: widget.onTap,
        //  () {
        // setState(() {
        // widget.currentIndex = widget.index;
        // });
        // },
        child: Container(
          width: context.width * 0.458,
          height: context.height * 0.07,
          decoration: BoxDecoration(
            color: widget.containerColor,
            borderRadius: BorderRadius.circular(15),
          ),
          child: Center(
            child: TextApp(
              text: widget.titles[widget.index],
              style: context.textStyle.copyWith(
                fontSize: AppDimensions.fontSizeLarge,
                color: widget.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
