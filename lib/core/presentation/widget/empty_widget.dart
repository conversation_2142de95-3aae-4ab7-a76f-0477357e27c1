import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:clean_arc/features/auth_feature/controller/auth_cubit.dart';
import 'package:flutter/material.dart';

class CustomEmptyWidget extends StatelessWidget {
  String title;

  CustomEmptyWidget({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    // print("${currentUser?.value.data?.auth?.id.toString()}sdsd");
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppImages.images.core.notFoundIllustration.svg(
              width: MediaQuery.of(context).size.width * 0.2,
              height: MediaQuery.of(context).size.height * 0.2),
          SizedBox(
            height: 24,
          ),
          TextApp(
            text: title,
            style: TextStyle(
                fontSize: AppDimensions.fontSizeExtraLarge,
                fontWeight: FontWeightHelper.bold),
          ),
        ],
      ),
    );
  }
}
