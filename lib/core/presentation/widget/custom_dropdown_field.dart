import 'package:clean_arc/core/utils_package/utils_package.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable

enum DropDownType {
  bottomSheetType,
  dialogType,
  dropDownType,
}

//  CustomDropdownField<ShipmentTypeModel>(
//                   isRow: true,
//                   asyncItems: (p0) async =>
//                       await cubit.getShipmentSubStatues(id: '11'),
//                   filedBuilder: (context, item) {
//                     return TextApp((context.isEnLocale == true)
//                         ? (item?.title ?? '')
//                         : (item?.titleAr ?? ''));
//                   },
//                   itemBuilder: (context, item, isSelected) {
//                     return Padding(
//                       padding:
//                           const EdgeInsets.all(AppDimensions.fontSizeDefault),
//                       child: TextApp(
//                         (context.isEnLocale == true)
//                             ? (item.title ?? '')
//                             : (item.titleAr ?? ''),
//                       ),
//                     );
//                   },
//                   dropDownType: DropDownType.dialogType,
//                   onChanged: (value) {
//                     // cubit.changeShipmentStatues(value!);
//
//                     cubit.onChangeShipmentSubStatues(value!.id.toString());
//                   },
//                   title: context.translate.status,
//                 ),

class CustomDropdownField<T> extends StatelessWidget {
  CustomDropdownField(
      {this.keykey,
      this.value,
      required this.onChanged,
      this.items,
      this.isRow = false,
      this.asyncItems,
      this.filedBuilder,
      this.dropDownType,
      this.validator,
      this.itemBuilder,
      this.itemAsString,
      required this.title});

  Widget Function(BuildContext context, T? item)? filedBuilder;
  Widget Function(
    BuildContext context,
    T item,
    bool isSelected,
  )? itemBuilder;
  String? Function(T?)? validator;
  String Function(T)? itemAsString;
  final T? value;
  bool? isRow;
  final String title;
  final GlobalKey? keykey;
  final DropDownType? dropDownType;
  void Function(T? value) onChanged;
  List<T>? items;
  Future<List<T>> Function(String)? asyncItems;

  @override
  Widget build(BuildContext context) {
    var drop = DropdownSearch<T>(
      key: keykey,
      asyncItems: asyncItems,
      dropdownBuilder: filedBuilder,

      // filterFn: (T item, String filter) {
      //   return
      // },
      validator: validator ??
          (value) {
            print('valuevaluevalue ${value}');
            if (value == null) {
              return context.translate.thisFieldIsRequired;
            } else {
              return null;
            }
          },

      popupProps: dropDownType == DropDownType.bottomSheetType
          ? PopupProps.bottomSheet(
              itemBuilder: itemBuilder,
              fit: FlexFit.loose,
              bottomSheetProps: BottomSheetProps(
                backgroundColor: Color.fromRGBO(245, 248, 252, 1),
              ))
          : dropDownType == DropDownType.dialogType
              ? PopupProps.dialog(
                  itemBuilder: itemBuilder,
                  fit: FlexFit.loose,
                  dialogProps: DialogProps(
                    backgroundColor: Color.fromRGBO(245, 248, 252, 1),
                  ))
              : dropDownType == DropDownType.dropDownType
                  ? PopupProps.menu(
                      itemBuilder: itemBuilder,
                      fit: FlexFit.loose,
                      menuProps: MenuProps(
                        backgroundColor: Color.fromRGBO(245, 248, 252, 1),
                      ))
                  : PopupProps.bottomSheet(
                      itemBuilder: itemBuilder,
                      fit: FlexFit.loose,
                      bottomSheetProps: BottomSheetProps(
                        backgroundColor: Color.fromRGBO(245, 248, 252, 1),
                      )),
      items: items ?? [],
      selectedItem: value,
      itemAsString: itemAsString,
      onChanged: onChanged,
      dropdownButtonProps: DropdownButtonProps(
        icon: Icon(
          Icons.keyboard_arrow_down_rounded,
          size: 18,
          color: context.color.darkBlackBlueColor,
        ),
      ),

      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          // labelText: title,
          hintText: title,
          filled: true,
          fillColor: context.color.fillColor,
          border: InputBorder.none,
          enabledBorder: getBorder(context),
          focusedBorder: getBorder(context),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: const BorderSide(),
          ),
          errorStyle: context.textStyle.copyWith(
              color: Theme.of(context).colorScheme.error,
              fontSize: AppDimensions.fontSizeSmall),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 20, vertical: 11),
        ),
      ),
    );

    return (isRow == true)
        ? Row(
            children: [
              // TextApp(
              // text:   title,
              //   style: context.textStyle.copyWith(
              //       fontSize: AppDimensions.fontSizeDefault,
              //       fontWeight: FontWeightHelper.regular),
              // ),
              // const SizedBox(width: 10),
              Expanded(child: drop)
            ],
          )
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //  TextApp(
              // text:    title,
              //    style: context.textStyle.copyWith(
              //        fontSize: AppDimensions.fontSizeDefault,
              //        fontWeight: FontWeightHelper.regular),
              //  ),
              //  const SizedBox(width: 10),
              drop
            ],
          );
  }

  OutlineInputBorder getBorder(BuildContext context, {bool isError = false}) =>
      OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: BorderSide(
            style: true ? BorderStyle.solid : BorderStyle.none,
            width: true ? 1 : 0,
            color:
                // isError == true ?
                context.color.borderColor!
            // : Colors.red,
            // color: context.color.borderColor!,
            ),
      );
}
