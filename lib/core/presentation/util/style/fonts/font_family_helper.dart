import 'package:clean_arc/core/data/services/shared_prefs/shared_pref.dart';
import 'package:clean_arc/core/data/services/shared_prefs/shared_prefs_key.dart';
import 'package:google_fonts/google_fonts.dart';

class FontFamilyHelper {
  const FontFamilyHelper._();

  static const String cairoArabic = 'Cairo';

  static const String poppinsEnglish = 'montserrat';

  static String geLocalozedFontFamily() {
    final currentLanguage = SharedPref().getString(PrefKeys.language);
    print("currentLanguage ${currentLanguage}");
    if (currentLanguage == 'en') {
      return GoogleFonts.tajawal().fontFamily ?? '';
      poppinsEnglish;
    } else {
      return GoogleFonts.tajawal().fontFamily ?? '';
      // cairoArabic;
    }
  }
}
