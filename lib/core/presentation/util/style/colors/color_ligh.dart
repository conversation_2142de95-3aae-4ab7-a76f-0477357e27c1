import 'package:flutter/material.dart';

class ColorsLight {
  static const Color mainColor = Color(0xffffffff);
  static const Color primaryColorLight = Color(0xff58ABE9);
  static const Color orangeLight = Color(0xffE45C0E);
  static const Color primaryColorDark = Color(0xff166CAC);

  // navBarSelectedTab
  static const Color greenColor = Color(0xff54AE90);
  static const Color textColorLight = Color(0xff454545);

  static const Color black = Color(0xff170605);
  static const Color descriptionColor = Color(0xff808D9E);
  static const Color white = Color(0xffFFFFFF);
  static const Color borderColorLight = Color(0xffE1E4EB);
  static const Color greyCounterBorderColor = Color(0xffD0D5DD);
  static const Color hintColorLight = Color(0xff667085);
  static const Color grayLight = Color(0xff828282);
  static const Color blueLight = Color(0xff41BCE8);
  static const Color darkBlue = Color(0xff3BA9D1);

  static const Color pinkColor = Color(0xffFF375F);
  static const Color fillColor = Color(0xffF9FAFB);
  static const Color redColor = Color(0xffE10B18);
  static const Color darkRedColor = Color(0xffC50000);
  static const Color darkBlackBlueColor = Color(0xff12215C);
  static const Color yellowColor = Color(0xfffFFBE15);
  static const Color skyBlueColor = Color(0xffD9F2FA);
  static const Color lightBlackColor = Color(0xff344054);
  static const Color lightGrey2 = Color(0xffE0E0E8);
}
