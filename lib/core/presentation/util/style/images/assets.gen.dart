/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// Directory path: assets/images/core
  $AssetsImagesCoreGen get core => const $AssetsImagesCoreGen();

  /// Directory path: assets/images/demo
  $AssetsImagesDemoGen get demo => const $AssetsImagesDemoGen();

  /// Directory path: assets/images/salim_icon
  $AssetsImagesSalimIconGen get salimIcon => const $AssetsImagesSalimIconGen();

  /// Directory path: assets/images/svg
  $AssetsImagesSvgGen get svg => const $AssetsImagesSvgGen();

  /// Directory path: assets/images/svg_icon
  $AssetsImagesSvgIconGen get svgIcon => const $AssetsImagesSvgIconGen();
}

class $AssetsImagesCoreGen {
  const $AssetsImagesCoreGen();

  /// File path: assets/images/core/Vector.png
  AssetGenImage get vector =>
      const AssetGenImage('assets/images/core/Vector.png');

  /// File path: assets/images/core/barcode.png
  AssetGenImage get barcode =>
      const AssetGenImage('assets/images/core/barcode.png');

  /// File path: assets/images/core/barcode2.png
  AssetGenImage get barcode2 =>
      const AssetGenImage('assets/images/core/barcode2.png');

  /// File path: assets/images/core/barcode2_dark.png
  AssetGenImage get barcode2Dark =>
      const AssetGenImage('assets/images/core/barcode2_dark.png');

  /// File path: assets/images/core/barcode_dark.png
  AssetGenImage get barcodeDark =>
      const AssetGenImage('assets/images/core/barcode_dark.png');

  /// File path: assets/images/core/calendar.png
  AssetGenImage get calendar =>
      const AssetGenImage('assets/images/core/calendar.png');

  /// File path: assets/images/core/calendar_dark.png
  AssetGenImage get calendarDark =>
      const AssetGenImage('assets/images/core/calendar_dark.png');

  /// File path: assets/images/core/cancel.png
  AssetGenImage get cancel =>
      const AssetGenImage('assets/images/core/cancel.png');

  /// File path: assets/images/core/cell-phone.png
  AssetGenImage get cellPhone =>
      const AssetGenImage('assets/images/core/cell-phone.png');

  /// File path: assets/images/core/cell-phone_unactive.png
  AssetGenImage get cellPhoneUnactive =>
      const AssetGenImage('assets/images/core/cell-phone_unactive.png');

  /// File path: assets/images/core/check.png
  AssetGenImage get check =>
      const AssetGenImage('assets/images/core/check.png');

  /// File path: assets/images/core/development-and-progress.png
  AssetGenImage get developmentAndProgress =>
      const AssetGenImage('assets/images/core/development-and-progress.png');

  /// File path: assets/images/core/email.svg
  SvgGenImage get email => const SvgGenImage('assets/images/core/email.svg');

  /// File path: assets/images/core/home.png
  AssetGenImage get home => const AssetGenImage('assets/images/core/home.png');

  /// File path: assets/images/core/home_active.png
  AssetGenImage get homeActive =>
      const AssetGenImage('assets/images/core/home_active.png');

  /// File path: assets/images/core/home_icon.svg
  SvgGenImage get homeIcon =>
      const SvgGenImage('assets/images/core/home_icon.svg');

  /// File path: assets/images/core/home_tab.png
  AssetGenImage get homeTab =>
      const AssetGenImage('assets/images/core/home_tab.png');

  /// File path: assets/images/core/home_unactive.png
  AssetGenImage get homeUnactive =>
      const AssetGenImage('assets/images/core/home_unactive.png');

  /// File path: assets/images/core/language-square.svg
  SvgGenImage get languageSquare =>
      const SvgGenImage('assets/images/core/language-square.svg');

  /// File path: assets/images/core/left_black_arrow.svg
  SvgGenImage get leftBlackArrow =>
      const SvgGenImage('assets/images/core/left_black_arrow.svg');

  /// File path: assets/images/core/location.png
  AssetGenImage get location =>
      const AssetGenImage('assets/images/core/location.png');

  /// File path: assets/images/core/location_unactive.png
  AssetGenImage get locationUnactive =>
      const AssetGenImage('assets/images/core/location_unactive.png');

  /// File path: assets/images/core/log_out.png
  AssetGenImage get logOut =>
      const AssetGenImage('assets/images/core/log_out.png');

  /// File path: assets/images/core/log_out_dark.png
  AssetGenImage get logOutDark =>
      const AssetGenImage('assets/images/core/log_out_dark.png');

  /// File path: assets/images/core/log_out_main_color.png
  AssetGenImage get logOutMainColor =>
      const AssetGenImage('assets/images/core/log_out_main_color.png');

  /// File path: assets/images/core/logo_video.gif
  AssetGenImage get logoVideoGif =>
      const AssetGenImage('assets/images/core/logo_video.gif');

  /// File path: assets/images/core/logo_video.webm
  String get logoVideoWebm => 'assets/images/core/logo_video.webm';

  /// Directory path: assets/images/core/logos
  $AssetsImagesCoreLogosGen get logos => const $AssetsImagesCoreLogosGen();

  /// File path: assets/images/core/micc.png
  AssetGenImage get micc => const AssetGenImage('assets/images/core/micc.png');

  /// File path: assets/images/core/mobile-phone.png
  AssetGenImage get mobilePhone =>
      const AssetGenImage('assets/images/core/mobile-phone.png');

  /// File path: assets/images/core/next.png
  AssetGenImage get next => const AssetGenImage('assets/images/core/next.png');

  /// File path: assets/images/core/next_dark.png
  AssetGenImage get nextDark =>
      const AssetGenImage('assets/images/core/next_dark.png');

  /// File path: assets/images/core/not_found_Illustration.svg
  SvgGenImage get notFoundIllustration =>
      const SvgGenImage('assets/images/core/not_found_Illustration.svg');

  /// Directory path: assets/images/core/onboarding
  $AssetsImagesCoreOnboardingGen get onboarding =>
      const $AssetsImagesCoreOnboardingGen();

  /// File path: assets/images/core/pencil.png
  AssetGenImage get pencil =>
      const AssetGenImage('assets/images/core/pencil.png');

  /// File path: assets/images/core/pencil_dark.png
  AssetGenImage get pencilDark =>
      const AssetGenImage('assets/images/core/pencil_dark.png');

  /// File path: assets/images/core/phone-call.svg
  SvgGenImage get phoneCall =>
      const SvgGenImage('assets/images/core/phone-call.svg');

  /// File path: assets/images/core/profile.png
  AssetGenImage get profile =>
      const AssetGenImage('assets/images/core/profile.png');

  /// File path: assets/images/core/profile_bg.svg
  SvgGenImage get profileBg =>
      const SvgGenImage('assets/images/core/profile_bg.svg');

  /// File path: assets/images/core/profile_icon.svg
  SvgGenImage get profileIcon =>
      const SvgGenImage('assets/images/core/profile_icon.svg');

  /// File path: assets/images/core/right_arrow.svg
  SvgGenImage get rightArrow =>
      const SvgGenImage('assets/images/core/right_arrow.svg');

  /// File path: assets/images/core/search_active.png
  AssetGenImage get searchActive =>
      const AssetGenImage('assets/images/core/search_active.png');

  /// File path: assets/images/core/search_tab.png
  AssetGenImage get searchTab =>
      const AssetGenImage('assets/images/core/search_tab.png');

  /// File path: assets/images/core/search_unactive.png
  AssetGenImage get searchUnactive =>
      const AssetGenImage('assets/images/core/search_unactive.png');

  /// File path: assets/images/core/show_password.svg
  SvgGenImage get showPassword =>
      const SvgGenImage('assets/images/core/show_password.svg');

  /// File path: assets/images/core/sort.png
  AssetGenImage get sort => const AssetGenImage('assets/images/core/sort.png');

  /// File path: assets/images/core/sort_dark.png
  AssetGenImage get sortDark =>
      const AssetGenImage('assets/images/core/sort_dark.png');

  /// File path: assets/images/core/splach_screen.png
  AssetGenImage get splachScreen =>
      const AssetGenImage('assets/images/core/splach_screen.png');

  /// File path: assets/images/core/user-unactive.png
  AssetGenImage get userUnactive =>
      const AssetGenImage('assets/images/core/user-unactive.png');

  /// File path: assets/images/core/user_active.png
  AssetGenImage get userActive =>
      const AssetGenImage('assets/images/core/user_active.png');

  /// File path: assets/images/core/user_image.png
  AssetGenImage get userImage =>
      const AssetGenImage('assets/images/core/user_image.png');

  /// File path: assets/images/core/user_tab.png
  AssetGenImage get userTab =>
      const AssetGenImage('assets/images/core/user_tab.png');

  /// File path: assets/images/core/vidddddddd.mp4
  String get vidddddddd => 'assets/images/core/vidddddddd.mp4';

  /// File path: assets/images/core/vide.png
  AssetGenImage get vide => const AssetGenImage('assets/images/core/vide.png');

  /// File path: assets/images/core/video1.png
  AssetGenImage get video1 =>
      const AssetGenImage('assets/images/core/video1.png');

  /// File path: assets/images/core/windows.png
  AssetGenImage get windows =>
      const AssetGenImage('assets/images/core/windows.png');

  /// File path: assets/images/core/windows_dark.png
  AssetGenImage get windowsDark =>
      const AssetGenImage('assets/images/core/windows_dark.png');

  /// List of all assets
  List<dynamic> get values => [
        vector,
        barcode,
        barcode2,
        barcode2Dark,
        barcodeDark,
        calendar,
        calendarDark,
        cancel,
        cellPhone,
        cellPhoneUnactive,
        check,
        developmentAndProgress,
        email,
        home,
        homeActive,
        homeIcon,
        homeTab,
        homeUnactive,
        languageSquare,
        leftBlackArrow,
        location,
        locationUnactive,
        logOut,
        logOutDark,
        logOutMainColor,
        logoVideoGif,
        logoVideoWebm,
        micc,
        mobilePhone,
        next,
        nextDark,
        notFoundIllustration,
        pencil,
        pencilDark,
        phoneCall,
        profile,
        profileBg,
        profileIcon,
        rightArrow,
        searchActive,
        searchTab,
        searchUnactive,
        showPassword,
        sort,
        sortDark,
        splachScreen,
        userUnactive,
        userActive,
        userImage,
        userTab,
        vidddddddd,
        vide,
        video1,
        windows,
        windowsDark
      ];
}

class $AssetsImagesDemoGen {
  const $AssetsImagesDemoGen();

  /// File path: assets/images/demo/Background (4).png
  AssetGenImage get background4 =>
      const AssetGenImage('assets/images/demo/Background (4).png');

  /// File path: assets/images/demo/book.png
  AssetGenImage get book => const AssetGenImage('assets/images/demo/book.png');

  /// File path: assets/images/demo/c1.png
  AssetGenImage get c1 => const AssetGenImage('assets/images/demo/c1.png');

  /// File path: assets/images/demo/c2.png
  AssetGenImage get c2 => const AssetGenImage('assets/images/demo/c2.png');

  /// File path: assets/images/demo/c3.png
  AssetGenImage get c3 => const AssetGenImage('assets/images/demo/c3.png');

  /// File path: assets/images/demo/c4.png
  AssetGenImage get c4 => const AssetGenImage('assets/images/demo/c4.png');

  /// File path: assets/images/demo/c5.png
  AssetGenImage get c5 => const AssetGenImage('assets/images/demo/c5.png');

  /// File path: assets/images/demo/c6.png
  AssetGenImage get c6 => const AssetGenImage('assets/images/demo/c6.png');

  /// File path: assets/images/demo/i1.png
  AssetGenImage get i1 => const AssetGenImage('assets/images/demo/i1.png');

  /// File path: assets/images/demo/i2.png
  AssetGenImage get i2 => const AssetGenImage('assets/images/demo/i2.png');

  /// File path: assets/images/demo/i3.png
  AssetGenImage get i3 => const AssetGenImage('assets/images/demo/i3.png');

  /// File path: assets/images/demo/i4.png
  AssetGenImage get i4 => const AssetGenImage('assets/images/demo/i4.png');

  /// File path: assets/images/demo/i5.png
  AssetGenImage get i5 => const AssetGenImage('assets/images/demo/i5.png');

  /// File path: assets/images/demo/i6.png
  AssetGenImage get i6 => const AssetGenImage('assets/images/demo/i6.png');

  /// File path: assets/images/demo/rate_bar.png
  AssetGenImage get rateBar =>
      const AssetGenImage('assets/images/demo/rate_bar.png');

  /// File path: assets/images/demo/user_image.png
  AssetGenImage get userImage =>
      const AssetGenImage('assets/images/demo/user_image.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        background4,
        book,
        c1,
        c2,
        c3,
        c4,
        c5,
        c6,
        i1,
        i2,
        i3,
        i4,
        i5,
        i6,
        rateBar,
        userImage
      ];
}

class $AssetsImagesSalimIconGen {
  const $AssetsImagesSalimIconGen();

  /// File path: assets/images/salim_icon/check-circle.svg
  SvgGenImage get checkCircle =>
      const SvgGenImage('assets/images/salim_icon/check-circle.svg');

  /// File path: assets/images/salim_icon/cloud_icon.svg
  SvgGenImage get cloudIcon =>
      const SvgGenImage('assets/images/salim_icon/cloud_icon.svg');

  /// File path: assets/images/salim_icon/coupon_.svg
  SvgGenImage get coupon =>
      const SvgGenImage('assets/images/salim_icon/coupon_.svg');

  /// File path: assets/images/salim_icon/daimond.svg
  SvgGenImage get daimond =>
      const SvgGenImage('assets/images/salim_icon/daimond.svg');

  /// File path: assets/images/salim_icon/forma_image.png
  AssetGenImage get formaImage =>
      const AssetGenImage('assets/images/salim_icon/forma_image.png');

  /// File path: assets/images/salim_icon/google.svg
  SvgGenImage get google =>
      const SvgGenImage('assets/images/salim_icon/google.svg');

  /// File path: assets/images/salim_icon/header_music_icon.png
  AssetGenImage get headerMusicIcon =>
      const AssetGenImage('assets/images/salim_icon/header_music_icon.png');

  /// File path: assets/images/salim_icon/key.svg
  SvgGenImage get key => const SvgGenImage('assets/images/salim_icon/key.svg');

  /// File path: assets/images/salim_icon/male.svg
  SvgGenImage get male =>
      const SvgGenImage('assets/images/salim_icon/male.svg');

  /// File path: assets/images/salim_icon/no_play_icon.svg
  SvgGenImage get noPlayIcon =>
      const SvgGenImage('assets/images/salim_icon/no_play_icon.svg');

  /// File path: assets/images/salim_icon/packge.png
  AssetGenImage get packge =>
      const AssetGenImage('assets/images/salim_icon/packge.png');

  /// File path: assets/images/salim_icon/play_icon.svg
  SvgGenImage get playIcon =>
      const SvgGenImage('assets/images/salim_icon/play_icon.svg');

  /// File path: assets/images/salim_icon/player_image.png
  AssetGenImage get playerImage =>
      const AssetGenImage('assets/images/salim_icon/player_image.png');

  /// File path: assets/images/salim_icon/profile-circle.svg
  SvgGenImage get profileCircle =>
      const SvgGenImage('assets/images/salim_icon/profile-circle.svg');

  /// File path: assets/images/salim_icon/profile_icon.svg
  SvgGenImage get profileIcon =>
      const SvgGenImage('assets/images/salim_icon/profile_icon.svg');

  /// File path: assets/images/salim_icon/puse.png
  AssetGenImage get puse =>
      const AssetGenImage('assets/images/salim_icon/puse.png');

  /// File path: assets/images/salim_icon/share.svg
  SvgGenImage get share =>
      const SvgGenImage('assets/images/salim_icon/share.svg');

  /// File path: assets/images/salim_icon/user_icon.png
  AssetGenImage get userIcon =>
      const AssetGenImage('assets/images/salim_icon/user_icon.png');

  /// File path: assets/images/salim_icon/womment.svg
  SvgGenImage get womment =>
      const SvgGenImage('assets/images/salim_icon/womment.svg');

  /// List of all assets
  List<dynamic> get values => [
        checkCircle,
        cloudIcon,
        coupon,
        daimond,
        formaImage,
        google,
        headerMusicIcon,
        key,
        male,
        noPlayIcon,
        packge,
        playIcon,
        playerImage,
        profileCircle,
        profileIcon,
        puse,
        share,
        userIcon,
        womment
      ];
}

class $AssetsImagesSvgGen {
  const $AssetsImagesSvgGen();

  /// File path: assets/images/svg/103364-success-check.json
  LottieGenImage get a103364SuccessCheck =>
      const LottieGenImage('assets/images/svg/103364-success-check.json');

  /// File path: assets/images/svg/arrow_back.svg
  SvgGenImage get arrowBack =>
      const SvgGenImage('assets/images/svg/arrow_back.svg');

  /// File path: assets/images/svg/build_developer.svg
  SvgGenImage get buildDeveloper =>
      const SvgGenImage('assets/images/svg/build_developer.svg');

  /// File path: assets/images/svg/build_version.svg
  SvgGenImage get buildVersion =>
      const SvgGenImage('assets/images/svg/build_version.svg');

  /// File path: assets/images/svg/car_shop.svg
  SvgGenImage get carShop =>
      const SvgGenImage('assets/images/svg/car_shop.svg');

  /// File path: assets/images/svg/categories_tap_icon.svg
  SvgGenImage get categoriesTapIcon =>
      const SvgGenImage('assets/images/svg/categories_tap_icon.svg');

  /// File path: assets/images/svg/dark_mode.svg
  SvgGenImage get darkMode =>
      const SvgGenImage('assets/images/svg/dark_mode.svg');

  /// File path: assets/images/svg/favourite_tab_icon.svg
  SvgGenImage get favouriteTabIcon =>
      const SvgGenImage('assets/images/svg/favourite_tab_icon.svg');

  /// File path: assets/images/svg/home_tab_icon.svg
  SvgGenImage get homeTabIcon =>
      const SvgGenImage('assets/images/svg/home_tab_icon.svg');

  /// File path: assets/images/svg/key_icon.svg
  SvgGenImage get keyIcon =>
      const SvgGenImage('assets/images/svg/key_icon.svg');

  /// File path: assets/images/svg/language.svg
  SvgGenImage get language =>
      const SvgGenImage('assets/images/svg/language.svg');

  /// File path: assets/images/svg/lock_icon.svg
  SvgGenImage get lockIcon =>
      const SvgGenImage('assets/images/svg/lock_icon.svg');

  /// File path: assets/images/svg/logout.svg
  SvgGenImage get logout => const SvgGenImage('assets/images/svg/logout.svg');

  /// File path: assets/images/svg/master.svg
  SvgGenImage get master => const SvgGenImage('assets/images/svg/master.svg');

  /// File path: assets/images/svg/notification_icon.svg
  SvgGenImage get notificationIcon =>
      const SvgGenImage('assets/images/svg/notification_icon.svg');

  /// File path: assets/images/svg/profile_tab_icon.svg
  SvgGenImage get profileTabIcon =>
      const SvgGenImage('assets/images/svg/profile_tab_icon.svg');

  /// File path: assets/images/svg/search.svg
  SvgGenImage get search => const SvgGenImage('assets/images/svg/search.svg');

  /// File path: assets/images/svg/visa.svg
  SvgGenImage get visa => const SvgGenImage('assets/images/svg/visa.svg');

  /// List of all assets
  List<dynamic> get values => [
        a103364SuccessCheck,
        arrowBack,
        buildDeveloper,
        buildVersion,
        carShop,
        categoriesTapIcon,
        darkMode,
        favouriteTabIcon,
        homeTabIcon,
        keyIcon,
        language,
        lockIcon,
        logout,
        master,
        notificationIcon,
        profileTabIcon,
        search,
        visa
      ];
}

class $AssetsImagesSvgIconGen {
  const $AssetsImagesSvgIconGen();

  /// File path: assets/images/svg_icon/Despoit.svg
  SvgGenImage get despoit =>
      const SvgGenImage('assets/images/svg_icon/Despoit.svg');

  /// File path: assets/images/svg_icon/Withdraw.svg
  SvgGenImage get withdraw =>
      const SvgGenImage('assets/images/svg_icon/Withdraw.svg');

  /// File path: assets/images/svg_icon/box.svg
  SvgGenImage get box => const SvgGenImage('assets/images/svg_icon/box.svg');

  /// File path: assets/images/svg_icon/calender.svg
  SvgGenImage get calender =>
      const SvgGenImage('assets/images/svg_icon/calender.svg');

  /// File path: assets/images/svg_icon/close.svg
  SvgGenImage get close =>
      const SvgGenImage('assets/images/svg_icon/close.svg');

  /// File path: assets/images/svg_icon/company.svg
  SvgGenImage get company =>
      const SvgGenImage('assets/images/svg_icon/company.svg');

  /// File path: assets/images/svg_icon/edit.svg
  SvgGenImage get edit => const SvgGenImage('assets/images/svg_icon/edit.svg');

  /// File path: assets/images/svg_icon/home_image_header.png
  AssetGenImage get homeImageHeader =>
      const AssetGenImage('assets/images/svg_icon/home_image_header.png');

  /// File path: assets/images/svg_icon/home_image_two.png
  AssetGenImage get homeImageTwo =>
      const AssetGenImage('assets/images/svg_icon/home_image_two.png');

  /// File path: assets/images/svg_icon/ic_destination.svg
  SvgGenImage get icDestination =>
      const SvgGenImage('assets/images/svg_icon/ic_destination.svg');

  /// File path: assets/images/svg_icon/ic_source.svg
  SvgGenImage get icSource =>
      const SvgGenImage('assets/images/svg_icon/ic_source.svg');

  /// File path: assets/images/svg_icon/location.svg
  SvgGenImage get location =>
      const SvgGenImage('assets/images/svg_icon/location.svg');

  /// File path: assets/images/svg_icon/mail_icon.svg
  SvgGenImage get mailIcon =>
      const SvgGenImage('assets/images/svg_icon/mail_icon.svg');

  /// File path: assets/images/svg_icon/map_icon.png
  AssetGenImage get mapIconPng =>
      const AssetGenImage('assets/images/svg_icon/map_icon.png');

  /// File path: assets/images/svg_icon/map_icon.svg
  SvgGenImage get mapIconSvg =>
      const SvgGenImage('assets/images/svg_icon/map_icon.svg');

  /// File path: assets/images/svg_icon/money.svg
  SvgGenImage get money =>
      const SvgGenImage('assets/images/svg_icon/money.svg');

  /// File path: assets/images/svg_icon/music1.png
  AssetGenImage get music1 =>
      const AssetGenImage('assets/images/svg_icon/music1.png');

  /// File path: assets/images/svg_icon/music2.png
  AssetGenImage get music2 =>
      const AssetGenImage('assets/images/svg_icon/music2.png');

  /// File path: assets/images/svg_icon/music3.png
  AssetGenImage get music3 =>
      const AssetGenImage('assets/images/svg_icon/music3.png');

  /// File path: assets/images/svg_icon/my_order.svg
  SvgGenImage get myOrder =>
      const SvgGenImage('assets/images/svg_icon/my_order.svg');

  /// File path: assets/images/svg_icon/note.svg
  SvgGenImage get note => const SvgGenImage('assets/images/svg_icon/note.svg');

  /// File path: assets/images/svg_icon/notification.svg
  SvgGenImage get notification =>
      const SvgGenImage('assets/images/svg_icon/notification.svg');

  /// File path: assets/images/svg_icon/notification_right_icon.svg
  SvgGenImage get notificationRightIcon =>
      const SvgGenImage('assets/images/svg_icon/notification_right_icon.svg');

  /// File path: assets/images/svg_icon/phone)icon.svg
  SvgGenImage get phoneIcon =>
      const SvgGenImage('assets/images/svg_icon/phone)icon.svg');

  /// File path: assets/images/svg_icon/photo.svg
  SvgGenImage get photo =>
      const SvgGenImage('assets/images/svg_icon/photo.svg');

  /// File path: assets/images/svg_icon/success.png
  AssetGenImage get success =>
      const AssetGenImage('assets/images/svg_icon/success.png');

  /// File path: assets/images/svg_icon/ticket-star.svg
  SvgGenImage get ticketStar =>
      const SvgGenImage('assets/images/svg_icon/ticket-star.svg');

  /// File path: assets/images/svg_icon/truck.svg
  SvgGenImage get truck =>
      const SvgGenImage('assets/images/svg_icon/truck.svg');

  /// File path: assets/images/svg_icon/truck_icon.svg
  SvgGenImage get truckIcon =>
      const SvgGenImage('assets/images/svg_icon/truck_icon.svg');

  /// File path: assets/images/svg_icon/truck_small.svg
  SvgGenImage get truckSmall =>
      const SvgGenImage('assets/images/svg_icon/truck_small.svg');

  /// File path: assets/images/svg_icon/upload.png
  AssetGenImage get upload =>
      const AssetGenImage('assets/images/svg_icon/upload.png');

  /// File path: assets/images/svg_icon/upload_icon.svg
  SvgGenImage get uploadIcon =>
      const SvgGenImage('assets/images/svg_icon/upload_icon.svg');

  /// File path: assets/images/svg_icon/user.svg
  SvgGenImage get user => const SvgGenImage('assets/images/svg_icon/user.svg');

  /// File path: assets/images/svg_icon/views.svg
  SvgGenImage get views =>
      const SvgGenImage('assets/images/svg_icon/views.svg');

  /// File path: assets/images/svg_icon/wallet.svg
  SvgGenImage get wallet =>
      const SvgGenImage('assets/images/svg_icon/wallet.svg');

  /// File path: assets/images/svg_icon/weight.svg
  SvgGenImage get weight =>
      const SvgGenImage('assets/images/svg_icon/weight.svg');

  /// File path: assets/images/svg_icon/whatsapp_icon.svg
  SvgGenImage get whatsappIcon =>
      const SvgGenImage('assets/images/svg_icon/whatsapp_icon.svg');

  /// List of all assets
  List<dynamic> get values => [
        despoit,
        withdraw,
        box,
        calender,
        close,
        company,
        edit,
        homeImageHeader,
        homeImageTwo,
        icDestination,
        icSource,
        location,
        mailIcon,
        mapIconPng,
        mapIconSvg,
        money,
        music1,
        music2,
        music3,
        myOrder,
        note,
        notification,
        notificationRightIcon,
        phoneIcon,
        photo,
        success,
        ticketStar,
        truck,
        truckIcon,
        truckSmall,
        upload,
        uploadIcon,
        user,
        views,
        wallet,
        weight,
        whatsappIcon
      ];
}

class $AssetsImagesCoreLogosGen {
  const $AssetsImagesCoreLogosGen();

  /// File path: assets/images/core/logos/Background.png
  AssetGenImage get background =>
      const AssetGenImage('assets/images/core/logos/Background.png');

  /// File path: assets/images/core/logos/app_logo.png
  AssetGenImage get appLogo =>
      const AssetGenImage('assets/images/core/logos/app_logo.png');

  /// File path: assets/images/core/logos/horizontal-white-line-701751694604734frpu64cql9.png
  AssetGenImage get horizontalWhiteLine701751694604734frpu64cql9 =>
      const AssetGenImage(
          'assets/images/core/logos/horizontal-white-line-701751694604734frpu64cql9.png');

  /// File path: assets/images/core/logos/logo.png
  AssetGenImage get logo =>
      const AssetGenImage('assets/images/core/logos/logo.png');

  /// File path: assets/images/core/logos/logoColum.png
  AssetGenImage get logoColum =>
      const AssetGenImage('assets/images/core/logos/logoColum.png');

  /// File path: assets/images/core/logos/logoColum2.png
  AssetGenImage get logoColum2 =>
      const AssetGenImage('assets/images/core/logos/logoColum2.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        background,
        appLogo,
        horizontalWhiteLine701751694604734frpu64cql9,
        logo,
        logoColum,
        logoColum2
      ];
}

class $AssetsImagesCoreOnboardingGen {
  const $AssetsImagesCoreOnboardingGen();

  /// File path: assets/images/core/onboarding/onboarding_1.png
  AssetGenImage get onboarding1 =>
      const AssetGenImage('assets/images/core/onboarding/onboarding_1.png');

  /// File path: assets/images/core/onboarding/onboarding_2.png
  AssetGenImage get onboarding2 =>
      const AssetGenImage('assets/images/core/onboarding/onboarding_2.png');

  /// File path: assets/images/core/onboarding/onboarding_3.png
  AssetGenImage get onboarding3 =>
      const AssetGenImage('assets/images/core/onboarding/onboarding_3.png');

  /// File path: assets/images/core/onboarding/onboarding_4.png
  AssetGenImage get onboarding4 =>
      const AssetGenImage('assets/images/core/onboarding/onboarding_4.png');

  /// File path: assets/images/core/onboarding/onboarding_curve.png
  AssetGenImage get onboardingCurve =>
      const AssetGenImage('assets/images/core/onboarding/onboarding_curve.png');

  /// List of all assets
  List<AssetGenImage> get values =>
      [onboarding1, onboarding2, onboarding3, onboarding4, onboardingCurve];
}

class AppImages {
  const AppImages._();

  static const String aEnv = '.env.prod';
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const String shorebird = 'shorebird.yaml';

  /// List of all assets
  static List<String> get values => [aEnv, shorebird];
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(
    this._assetName, {
    this.flavors = const {},
  });

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(
      BuildContext,
      Widget,
      _lottie.LottieComposition?,
    )? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
    _lottie.LottieDecoder? decoder,
    _lottie.RenderCache? renderCache,
    bool? backgroundLoading,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
      decoder: decoder,
      renderCache: renderCache,
      backgroundLoading: backgroundLoading,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
