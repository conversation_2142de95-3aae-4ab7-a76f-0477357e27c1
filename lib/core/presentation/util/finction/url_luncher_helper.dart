import 'dart:io';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../widget/screen_utils.dart';

class UrlLauncherPhone extends StatelessWidget {
  final Widget child;
  final String phone;

  const UrlLauncherPhone({super.key, required this.phone, required this.child});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        launchUrlString('tel://${phone}');
      },
      child: child,
    );
  }
}

class UrlLauncherMap extends StatelessWidget {
  final String latitude;
  final String longitude;
  final String address;
  final Widget child;

  UrlLauncherMap(
      {required this.latitude, required this.longitude, required this.child, required this.address});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        late String gMapsUrl;
        if (latitude.isEmpty || longitude.isEmpty) {
          String query = Uri.encodeComponent(address);
          gMapsUrl = 'https://www.google.com/maps/search/?api=1&query=$query';
        } else {
          gMapsUrl =
          'https://www.google.com/maps/dir/api=1&destination=$latitude,$longitude&travelmode=driving';
        }


        launchUrlString('$gMapsUrl');
      },
      child: child,
    );
  }
}

class UrlLauncherWhatsapp extends StatefulWidget {
  final Widget child;
  final String phone;

  const UrlLauncherWhatsapp(
      {super.key, required this.phone, required this.child});

  @override
  State<UrlLauncherWhatsapp> createState() => _UrlLauncherWhatsappState();
}

class _UrlLauncherWhatsappState extends State<UrlLauncherWhatsapp>
    with ScreenUtils {
  void openWhatsapp({required String text, required String number}) async {
    var whatsapp = number; // Ensure number is in the correct format
    String url() {
      if (Platform.isAndroid) {
        // add the [https]
        return "https://wa.me/$number/?text=${Uri.parse(text)}"; // new line
      } else {
        // add the [https]
        return "https://api.whatsapp.com/send?phone=$number=${Uri.parse(text)}"; // new line
      }
    }

    if (Platform.isIOS) {
      // if (await canLaunch(url())) {
      await launch(url(), forceSafariVC: false);
      // } else {
      //   showError(customMessage: 'WhatsApp is not installed on your iOS device.');
      // }
    } else {
      // if (await canLaunch(url())) {
      await launch(url());
      // } else {
      //   // showError(customMessage: 'WhatsApp is not installed on your Android device.');
      // }
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        openWhatsapp(text: '', number: widget.phone);
      },
      child: widget.child,
    );
  }
}
