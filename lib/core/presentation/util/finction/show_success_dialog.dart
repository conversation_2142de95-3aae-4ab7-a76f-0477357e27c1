import 'package:clean_arc/core/presentation/extintions/context_extintions.dart';
import 'package:clean_arc/core/presentation/util/app_dimensions.dart';
import 'package:clean_arc/core/presentation/util/style/fonts/font_weight_helper.dart';
import 'package:clean_arc/core/presentation/util/style/images/assets.gen.dart';
import 'package:clean_arc/core/presentation/widget/custom_button_button.dart';
import 'package:clean_arc/core/presentation/widget/text_app.dart';
import 'package:flutter/material.dart';

showSuccessDialog(BuildContext context,
    {required String message,
    required String title,
    required VoidCallback? onTap}) {
  return showDialog(
    context: context,
    builder: (context) {
      return Dialog(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 20,
              ),
              AppImages.images.svgIcon.success.image(height: 100, width: 100),
              SizedBox(
                height: 10,
              ),
              TextApp(
                text: message,
                style: context.textStyle.copyWith(
                    fontSize: AppDimensions.fontSizeLarge,
                    color: context.color.descriptionColor,
                    fontWeight: FontWeightHelper.medium),
              ),
              SizedBox(
                height: 5,
              ),
              TextApp(
                text: title,
                style: context.textStyle.copyWith(
                    fontSize: AppDimensions.fontSizeExtraLarge,
                    color: context.color.textColor,
                    fontWeight: FontWeightHelper.bold),
              ),
              SizedBox(
                height: 20,
              ),
              CustomButton(
                  width: context.width / 2,
                  onPressed: onTap,
                  child: TextApp(
                    text: context.translate.close,
                    style: context.textStyleButton,
                  ))
            ],
          ),
        ),
      );
    },
  );
}
