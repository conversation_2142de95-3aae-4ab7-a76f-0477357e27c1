// ! Check if he not bought the (treatment) session should be in this api response from login, show buy page and view title from parent and description as html view
// ! If bought the treatment or make skip go to TreatmentVideoView but if skip and inside sub page videos show first video only and others be locked
// ! Enhance Page UI cards and remove static image
// ! When click on one card go to page that has multi video page when click show video widget (View title of parent and sub parent)
// ! If need to buy view price of parent price and view 1 option (If not subscribed view parent price with no discount - If subscribed on treatment in subscriptions and make it's offer and view before and after)
// ! If there not subscribed to website offers treatment view to user text that show he can subscribe in website and get some offers
//! Buy from paypal and send that's done to API

import 'package:clean_arc/app.dart';
import 'package:clean_arc/core/app/bloc_observer.dart';
import 'package:clean_arc/core/app/env.variable.dart';
import 'package:clean_arc/core/data/services/shared_prefs/shared_pref.dart';
import 'package:clean_arc/core/services/get_storage_service.dart';
import 'package:clean_arc/features/favorite_feature/controller/fav_manage_ment.dart';
import 'package:clean_arc/features/user_layout/lip_feature/controller/lib_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized(); // await setupInjector();
  var dir = await getApplicationDocumentsDirectory();
  await Hive.initFlutter(dir.path);
  await Hive.openBox('Name');
  await Hive.openBox('Favorite');
  await Hive.openBox('AllPlaylist');
  await GetStorageService.init();
  await FavoritesManager.init();
  await BookManage.init(); // تسجيل الـ Adapter إذا لم يكن مسجلاً
  await EnvVariable.instance.init(envType: EnvTypeEnum.prod);
  await SharedPref().instantiatePreferences();
  Bloc.observer = AppBlocObserver();
  await SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown],
  ).then((_) {
    runApp(AgzgantyApp());
  });
} //https://agza5nty.vercel.app/ //! Make Validation if there yes or no question //! When before 2 select send on API from 1 to 10 (Api is -> post) //! Finished button from last audio detail view go to rate again then to 2 select (Api is -> put after_audio) //! Add title of parent thought in last page summary //! After last page summary show again rate (Api IS -> put after_score)

String userCurrency =
    '\$'; // ! instead of انتهيت be التالي //! If yes or no make navigate to index in yes_route_id or no route_id (index is id on it) //! beside bookmark add to bookmark //! Make sure of next progress //! Make prev in questions page too //! and if from another index navigate to that prev index //! Last page should be ملخص الاستمارة //! Should instead of التالي to be انتهيت //! VIew parent title and idea he choose, View all summary fields on last page and save in storage to view later and for pages he saw only if did not saw and he navigate to another route don't show //! And when click انتهيت from last page go again to ideas
