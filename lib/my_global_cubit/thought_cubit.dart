import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:clean_arc/features/concerns_feature/domain/model/concerns_model.dart';
import 'package:injectable/injectable.dart';

@injectable
class ThoughtCubit extends Cubit<Thought?> {
  ThoughtCubit() : super(null);

  void setSelectedThought(Thought thought) {
    emit(thought);
  }

  void clearSelectedThought() {
    emit(null);
  }

  Thought? get selectedThought => state;
}
