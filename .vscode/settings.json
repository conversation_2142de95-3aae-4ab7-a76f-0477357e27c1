{
  "yaml.schemas": {
      "file:///c%3A/Users/<USER>/.cursor/extensions/atlassian.atlascode-3.0.10/resources/schemas/pipelines-schema.json": "bitbucket-pipelines.yml"
  },
  "workbench.colorTheme": "Darcula Theme from IntelliJ",
  "[dart]": {
      "editor.formatOnSave": true,
      "editor.formatOnType": true,
      "editor.rulers": [
          80
      ],
      "editor.selectionHighlight": false,
      "editor.tabCompletion": "onlySnippets",
      "editor.wordBasedSuggestions": "off"
  },
  // Add this new section
  "editor.tokenColorCustomizations": {
      "textMateRules": [
          {
              "scope": "variable",
              "settings": {
                  "foreground": "#6A8759"
              }
          }
      ]
  }
}